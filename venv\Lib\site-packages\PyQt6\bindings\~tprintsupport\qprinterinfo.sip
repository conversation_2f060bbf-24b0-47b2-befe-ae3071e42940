// qprinterinfo.sip generated by MetaSIP
//
// This file is part of the QtPrintSupport Python extension module.
//
// Copyright (c) 2022 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (PyQt_Printer)

class QPrinterInfo
{
%TypeHeaderCode
#include <qprinterinfo.h>
%End

public:
    QPrinterInfo();
    QPrinterInfo(const QPrinterInfo &src);
    explicit QPrinterInfo(const QPrinter &printer);
    ~QPrinterInfo();
    QString printerName() const;
    bool isNull() const;
    bool isDefault() const;
    static QList<QPrinterInfo> availablePrinters();
    static QPrinterInfo defaultPrinter();
    QString description() const;
    QString location() const;
    QString makeAndModel() const;
    static QPrinterInfo printerInfo(const QString &printerName);
    bool isRemote() const;
    QPrinter::PrinterState state() const;
    QList<QPageSize> supportedPageSizes() const;
    QPageSize defaultPageSize() const;
    bool supportsCustomPageSizes() const;
    QPageSize minimumPhysicalPageSize() const;
    QPageSize maximumPhysicalPageSize() const;
    QList<int> supportedResolutions() const;
    static QStringList availablePrinterNames();
    static QString defaultPrinterName();
    QPrinter::DuplexMode defaultDuplexMode() const;
    QList<QPrinter::DuplexMode> supportedDuplexModes() const;
    QPrinter::ColorMode defaultColorMode() const;
    QList<QPrinter::ColorMode> supportedColorModes() const;
};

%End
