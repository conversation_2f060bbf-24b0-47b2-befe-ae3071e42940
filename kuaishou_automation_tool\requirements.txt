# Kuaishou Automation Tool Dependencies
# Install with: pip install -r requirements.txt

# Core GUI Framework (PySide6 recommended, PyQt6 as fallback)
PySide6>=6.4.0
PyQt6>=6.4.0
PyQt6-tools>=6.4.0

# Web Automation
selenium>=4.15.0

# Data Processing
pandas>=1.5.0
numpy>=1.21.0

# HTTP Requests
requests>=2.28.0
urllib3>=1.26.0

# JSON and Configuration
pyyaml>=6.0

# Database
sqlalchemy>=1.4.0

# Logging and Utilities
colorlog>=6.7.0

# Threading and Async
threading-timeout>=0.1.0

# Development Dependencies
pytest>=7.0.0
pytest-qt>=4.2.0
black>=22.0.0
flake8>=4.0.0
