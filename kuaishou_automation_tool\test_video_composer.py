#!/usr/bin/env python3
"""
测试图文视频合成功能
"""

import os
import sys
from PIL import Image

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def create_test_images():
    """创建测试图片"""
    test_dir = os.path.join(current_dir, "test_images")
    os.makedirs(test_dir, exist_ok=True)
    
    # 创建3张测试图片
    colors = [
        (255, 100, 100),  # 红色
        (100, 255, 100),  # 绿色
        (100, 100, 255),  # 蓝色
    ]
    
    texts = [
        "产品展示图1",
        "产品展示图2", 
        "产品展示图3"
    ]
    
    image_paths = []
    
    for i, (color, text) in enumerate(zip(colors, texts)):
        # 创建1080x1920的竖屏图片
        img = Image.new('RGB', (1080, 1920), color)
        
        # 保存图片
        img_path = os.path.join(test_dir, f"test_image_{i+1}.jpg")
        img.save(img_path, 'JPEG', quality=95)
        image_paths.append(img_path)
        print(f"✅ 创建测试图片: {img_path}")
    
    return image_paths

def test_video_composer():
    """测试视频合成功能"""
    try:
        from business.video_composer import VideoComposer
        
        print("🎬 开始测试图文视频合成功能...")
        
        # 创建测试图片
        image_paths = create_test_images()
        
        # 测试文案
        test_text = """
        欢迎来到我们的直播间！今天为大家推荐这款超值好物。
        这个产品有以下几个特点：质量优良、价格实惠、性价比超高。
        现在下单还有特别优惠，机会难得，不要错过！
        """
        
        # 输出路径
        output_path = os.path.join(current_dir, "test_output_video.mp4")
        
        # 创建视频合成器
        composer = VideoComposer()
        
        # 配置参数
        config = {
            'video_width': 1080,
            'video_height': 1920,
            'fps': 30,
            'image_duration': 3.0,
            'voice_speed': 1.0,
            'voice_volume': 0.8
        }
        
        print("🔄 开始合成视频...")
        
        # 合成视频
        success = composer.create_video_from_images_and_text(
            images=image_paths,
            text_content=test_text.strip(),
            output_path=output_path,
            config=config
        )
        
        if success:
            print(f"🎉 视频合成成功！")
            print(f"📁 输出文件: {output_path}")
            
            # 检查文件是否存在
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
                print(f"📊 文件大小: {file_size:.2f} MB")
            else:
                print("⚠️ 输出文件不存在")
        else:
            print("❌ 视频合成失败")
            
        return success
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dependencies():
    """测试依赖库"""
    print("🔍 检查依赖库...")
    
    dependencies = [
        ("PIL", "Pillow图像处理库"),
        ("pyttsx3", "离线语音合成库"),
        ("gtts", "Google语音合成库"),
        ("moviepy", "视频处理库"),
        ("ffmpeg", "FFmpeg视频处理库"),
    ]
    
    success_count = 0
    
    for module, description in dependencies:
        try:
            __import__(module)
            print(f"✅ {description} - 已安装")
            success_count += 1
        except ImportError:
            print(f"❌ {description} - 未安装")
    
    print(f"\n📊 依赖检查结果: {success_count}/{len(dependencies)} 个库可用")
    return success_count == len(dependencies)

def main():
    """主函数"""
    print("🚀 图文视频合成功能测试")
    print("=" * 50)
    
    # 检查依赖
    deps_ok = test_dependencies()
    
    if not deps_ok:
        print("\n⚠️ 部分依赖库缺失，可能影响功能")
        print("💡 请运行: pip install pyttsx3 gTTS moviepy ffmpeg-python Pillow")
    
    print("\n" + "=" * 50)
    
    # 测试视频合成
    success = test_video_composer()
    
    print("\n" + "=" * 50)
    
    if success:
        print("🎉 测试完成！图文视频合成功能正常工作")
        print("\n💡 现在可以在主界面使用图文视频功能了：")
        print("1. 启动应用程序")
        print("2. 点击左侧 '🎬 图文视频' 选项卡")
        print("3. 选择图片和输入文案")
        print("4. 生成专业带货视频")
    else:
        print("❌ 测试失败，请检查错误信息")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        input("\n按回车键退出...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
        sys.exit(1)
