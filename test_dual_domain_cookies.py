#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双域名Cookie转换功能测试脚本
"""

import sys
import os
import json
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.join(os.getcwd(), 'kuaishou_automation_tool'))

def test_cookie_conversion():
    """测试Cookie双域名转换功能"""
    print("🧪 开始测试双域名Cookie转换功能...")
    
    # 模拟kuaishou.com的Cookie
    kuaishou_cookies = [
        {
            'name': 'userId',
            'value': '12345678',
            'domain': '.kuaishou.com',
            'path': '/'
        },
        {
            'name': 'bUserId',
            'value': 'b12345678',
            'domain': '.kuaishou.com',
            'path': '/'
        },
        {
            'name': 'kuaishou.web.cp.api_ph',
            'value': 'ph_token_123',
            'domain': '.kuaishou.com',
            'path': '/'
        },
        {
            'name': 'kuaishou.web.cp.api_st',
            'value': 'st_token_456',
            'domain': '.kuaishou.com',
            'path': '/'
        },
        {
            'name': 'did',
            'value': 'device_id_789',
            'domain': '.kuaishou.com',
            'path': '/'
        }
    ]
    
    # 模拟kwaixiaodian.com的Cookie
    kwaixiaodian_cookies = [
        {
            'name': 'userId',
            'value': '87654321',
            'domain': '.kwaixiaodian.com',
            'path': '/'
        },
        {
            'name': 'sid',
            'value': 'session_id_abc',
            'domain': '.kwaixiaodian.com',
            'path': '/'
        },
        {
            'name': 'kuaishou.shop.b_ph',
            'value': 'shop_ph_def',
            'domain': '.kwaixiaodian.com',
            'path': '/'
        },
        {
            'name': 'kuaishou.shop.b_st',
            'value': 'shop_st_ghi',
            'domain': '.kwaixiaodian.com',
            'path': '/'
        }
    ]
    
    try:
        # 测试Cookie管理器
        from business.cookie_manager import CookieManager
        cookie_manager = CookieManager()
        
        print("\n📋 测试1: Cookie域名分析")
        analysis = cookie_manager._analyze_cookie_domains(kuaishou_cookies)
        print(f"  kuaishou.com Cookie分析结果:")
        print(f"    主域名: {analysis['primary']}")
        print(f"    支持域名: {analysis['all']}")
        print(f"    通用Cookie: {analysis['universal']}")
        print(f"    可转换: {analysis['convertible']}")
        
        analysis2 = cookie_manager._analyze_cookie_domains(kwaixiaodian_cookies)
        print(f"  kwaixiaodian.com Cookie分析结果:")
        print(f"    主域名: {analysis2['primary']}")
        print(f"    支持域名: {analysis2['all']}")
        print(f"    通用Cookie: {analysis2['universal']}")
        print(f"    可转换: {analysis2['convertible']}")
        
        print("\n📋 测试2: Cookie保存和加载")
        # 保存kuaishou Cookie
        file_path1 = cookie_manager.save_cookies("test_kuaishou", kuaishou_cookies, domain_info="kuaishou.com")
        if file_path1:
            print(f"  ✅ kuaishou Cookie保存成功: {file_path1}")
        else:
            print(f"  ❌ kuaishou Cookie保存失败")
            
        # 保存kwaixiaodian Cookie
        file_path2 = cookie_manager.save_cookies("test_kwaixiaodian", kwaixiaodian_cookies, domain_info="kwaixiaodian.com")
        if file_path2:
            print(f"  ✅ kwaixiaodian Cookie保存成功: {file_path2}")
        else:
            print(f"  ❌ kwaixiaodian Cookie保存失败")
        
        print("\n📋 测试3: 域名支持信息查询")
        domain_info1 = cookie_manager.get_cookie_domain_info("test_kuaishou")
        if domain_info1:
            print(f"  kuaishou Cookie域名信息:")
            print(f"    主域名: {domain_info1['primary_domain']}")
            print(f"    支持域名: {domain_info1['supported_domains']}")
            print(f"    双域名支持: {domain_info1['dual_domain_support']}")
            print(f"    转换能力: {domain_info1['conversion_capable']}")
        
        domain_info2 = cookie_manager.get_cookie_domain_info("test_kwaixiaodian")
        if domain_info2:
            print(f"  kwaixiaodian Cookie域名信息:")
            print(f"    主域名: {domain_info2['primary_domain']}")
            print(f"    支持域名: {domain_info2['supported_domains']}")
            print(f"    双域名支持: {domain_info2['dual_domain_support']}")
            print(f"    转换能力: {domain_info2['conversion_capable']}")
        
        print("\n📋 测试4: 转换能力检查")
        can_convert1 = cookie_manager.can_convert_to_domain("test_kuaishou", "kwaixiaodian.com")
        can_convert2 = cookie_manager.can_convert_to_domain("test_kwaixiaodian", "kuaishou.com")
        print(f"  kuaishou -> kwaixiaodian 转换能力: {can_convert1}")
        print(f"  kwaixiaodian -> kuaishou 转换能力: {can_convert2}")
        
        print("\n✅ Cookie管理器测试完成")
        
    except Exception as e:
        print(f"❌ Cookie管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试UI组件的转换功能
    try:
        print("\n📋 测试5: UI组件Cookie转换")
        
        # 创建一个模拟的UI组件来测试转换方法
        class MockAccountWidget:
            def _convert_cookies_to_dual_domain(self, cookies, target_domain):
                """模拟UI组件的转换方法"""
                # 这里复制UI组件中的转换逻辑进行测试
                print(f"🔄 开始智能双域名Cookie转换，目标域名: {target_domain}")
                converted_cookies = []

                # 定义Cookie分类和转换规则
                cookie_rules = {
                    'universal': {
                        'userId', 'bUserId', 'did', '_did', 'clientid', 'client_key'
                    },
                    'kuaishou_specific': {
                        'kuaishou.web.cp.api_ph', 'kuaishou.web.cp.api_st', 
                        'kuaishou.web.st', 'kuaishou.web.ph'
                    },
                    'kwaixiaodian_specific': {
                        'sid', 'kuaishou.shop.b_ph', 'kuaishou.shop.b_st',
                        'kuaishou.shop.st', 'kuaishou.shop.ph'
                    },
                    'mapping': {
                        'kuaishou.web.cp.api_ph': 'kuaishou.shop.b_ph',
                        'kuaishou.web.cp.api_st': 'kuaishou.shop.b_st',
                        'kuaishou.shop.b_ph': 'kuaishou.web.cp.api_ph',
                        'kuaishou.shop.b_st': 'kuaishou.web.cp.api_st',
                        'sid': 'kuaishou.web.cp.api_st'
                    }
                }

                clean_target_domain = target_domain.lstrip('.')
                
                for cookie in cookies:
                    if isinstance(cookie, dict) and 'name' in cookie and 'value' in cookie:
                        cookie_name = cookie['name']
                        
                        if cookie_name in cookie_rules['universal']:
                            converted_cookie = {
                                'name': cookie['name'],
                                'value': cookie['value'],
                                'domain': target_domain,
                                'path': cookie.get('path', '/'),
                            }
                            converted_cookies.append(converted_cookie)
                            print(f"🔄 转换通用Cookie: {cookie_name} -> {target_domain}")
                        
                        elif cookie_name in cookie_rules['kuaishou_specific']:
                            if 'kuaishou.com' in clean_target_domain:
                                converted_cookie = {
                                    'name': cookie['name'],
                                    'value': cookie['value'],
                                    'domain': target_domain,
                                    'path': cookie.get('path', '/'),
                                }
                                converted_cookies.append(converted_cookie)
                                print(f"✅ 保留kuaishou特有Cookie: {cookie_name}")
                            elif 'kwaixiaodian.com' in clean_target_domain:
                                mapped_name = cookie_rules['mapping'].get(cookie_name)
                                if mapped_name:
                                    converted_cookie = {
                                        'name': mapped_name,
                                        'value': cookie['value'],
                                        'domain': target_domain,
                                        'path': cookie.get('path', '/'),
                                    }
                                    converted_cookies.append(converted_cookie)
                                    print(f"🔄 转换kuaishou Cookie: {cookie_name} -> {mapped_name}")

                print(f"🔄 转换完成，原始: {len(cookies)}，转换后: {len(converted_cookies)}")
                return converted_cookies
        
        mock_widget = MockAccountWidget()
        
        # 测试kuaishou -> kwaixiaodian转换
        print(f"\n  测试 kuaishou.com -> kwaixiaodian.com 转换:")
        converted1 = mock_widget._convert_cookies_to_dual_domain(kuaishou_cookies, ".kwaixiaodian.com")
        print(f"    转换结果: {len(converted1)} 个Cookie")
        for cookie in converted1:
            print(f"      {cookie['name']} -> {cookie['domain']}")
        
        # 测试kwaixiaodian -> kuaishou转换
        print(f"\n  测试 kwaixiaodian.com -> kuaishou.com 转换:")
        converted2 = mock_widget._convert_cookies_to_dual_domain(kwaixiaodian_cookies, ".kuaishou.com")
        print(f"    转换结果: {len(converted2)} 个Cookie")
        for cookie in converted2:
            print(f"      {cookie['name']} -> {cookie['domain']}")
        
        print("\n✅ UI组件转换测试完成")
        
    except Exception as e:
        print(f"❌ UI组件转换测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n🎉 双域名Cookie转换功能测试完成！")

if __name__ == "__main__":
    test_cookie_conversion()
