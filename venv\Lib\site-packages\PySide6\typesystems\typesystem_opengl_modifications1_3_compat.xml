<!--
// Copyright (C) 2019 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<modify-function signature="^gl(Load|Mult)TransposeMatrix[df]\(.*$">
    <modify-argument index="1"><array/></modify-argument>
</modify-function>
<modify-function signature="^glMultiTexCoord\d[a-z]v\(.*$">
    <modify-argument index="2"><array/></modify-argument>
</modify-function>
