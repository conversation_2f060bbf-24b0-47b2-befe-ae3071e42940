"""
快手无人带货工具 - 系统设置组件
Kuaishou Automation Tool - Settings Widget

本模块实现系统设置界面，包括：
- Cookie存储路径配置
- 自动化相关设置
- 系统参数配置
- 配置的保存和恢复
"""

import os
from pathlib import Path

# 尝试导入Qt库，优先使用PySide6，回退到PyQt6
try:
    from PySide6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
        QLineEdit, QGroupBox, QFormLayout, QSpinBox, QCheckBox,
        QFileDialog, QMessageBox, QTextEdit, QComboBox, QSlider
    )
    from PySide6.QtCore import Qt, Signal as pyqtSignal
    from PySide6.QtGui import QFont
    QT_LIBRARY = "PySide6"
except ImportError:
    try:
        from PyQt6.QtWidgets import (
            QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
            QLineEdit, QGroupBox, QFormLayout, QSpinBox, QCheckBox,
            QFileDialog, QMessageBox, QTextEdit, QComboBox, QSlider
        )
        from PyQt6.QtCore import Qt, pyqtSignal
        from PyQt6.QtGui import QFont
        QT_LIBRARY = "PyQt6"
    except ImportError as e:
        print("❌ 无法导入Qt库，请使用命令行版本: python main_cli.py")
        import sys
        sys.exit(1)

# 修复导入问题
try:
    from data.models.base import ConfigManager
    from business.cookie_manager import CookieManager
    from utils.logger import get_logger
except ImportError:
    try:
        from kuaishou_automation_tool.data.models.base import ConfigManager
        from kuaishou_automation_tool.business.cookie_manager import CookieManager
        from kuaishou_automation_tool.utils.logger import get_logger
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        # 创建简单的替代类
        class ConfigManager:
            def get(self, key, default=None): return default
            def set(self, key, value): pass
            def save(self): pass
        class CookieManager:
            def __init__(self): pass
        def get_logger(name):
            import logging
            return logging.getLogger(name)


class SettingsWidget(QWidget):
    """
    系统设置组件类
    
    实现系统设置的完整功能，包括Cookie存储配置、自动化参数设置、
    系统参数配置等。支持配置的实时保存和恢复。
    
    Signals:
        settings_changed: 设置变更信号
        
    Attributes:
        config_manager (ConfigManager): 配置管理器
        cookie_manager (CookieManager): Cookie管理器
        logger: 日志记录器
    """
    
    # 定义信号
    settings_changed = pyqtSignal(str, object)
    
    def __init__(self, parent=None):
        """
        初始化设置组件
        
        Args:
            parent: 父组件，默认为None
        """
        super().__init__(parent)
        self.config_manager = ConfigManager()
        self.cookie_manager = CookieManager(self.config_manager)
        self.logger = get_logger("SettingsWidget")
        self.init_ui()
        self.load_settings()
        
    def init_ui(self):
        """
        初始化用户界面
        
        创建设置界面的完整布局：
        - 页面标题和描述
        - Cookie存储设置
        - 自动化参数设置
        - 系统参数设置
        - 操作按钮区域
        """
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(20)
        
        # 创建各个设置区域
        self.create_header_section(main_layout)
        self.create_cookie_settings_section(main_layout)
        self.create_automation_settings_section(main_layout)
        self.create_system_settings_section(main_layout)
        self.create_action_buttons_section(main_layout)
        
        # 添加弹性空间
        main_layout.addStretch()
        
    def create_header_section(self, parent_layout):
        """
        创建页面标题区域
        
        Args:
            parent_layout: 父布局对象
        """
        # 页面标题
        title = QLabel("系统设置")
        title.setObjectName("pageTitle")
        parent_layout.addWidget(title)
        
        # 功能描述
        description = QLabel("配置系统参数、Cookie存储路径和自动化相关设置")
        description.setStyleSheet("color: #cccccc; font-size: 14px; margin-bottom: 10px;")
        parent_layout.addWidget(description)
        
    def create_cookie_settings_section(self, parent_layout):
        """
        创建Cookie设置区域
        
        Args:
            parent_layout: 父布局对象
        """
        cookie_group = QGroupBox("Cookie存储设置")
        cookie_layout = QFormLayout(cookie_group)
        
        # Cookie存储路径
        cookie_path_layout = QHBoxLayout()
        self.cookie_path_edit = QLineEdit()
        self.cookie_path_edit.setPlaceholderText("选择Cookie文件存储目录")
        self.cookie_path_edit.setReadOnly(True)
        
        self.browse_cookie_path_button = QPushButton("浏览...")
        self.browse_cookie_path_button.clicked.connect(self.browse_cookie_path)
        
        cookie_path_layout.addWidget(self.cookie_path_edit)
        cookie_path_layout.addWidget(self.browse_cookie_path_button)
        
        cookie_layout.addRow("存储路径:", cookie_path_layout)
        
        # Cookie有效期设置
        self.cookie_max_age_spinbox = QSpinBox()
        self.cookie_max_age_spinbox.setRange(1, 365)
        self.cookie_max_age_spinbox.setValue(30)
        self.cookie_max_age_spinbox.setSuffix(" 天")
        cookie_layout.addRow("Cookie有效期:", self.cookie_max_age_spinbox)
        
        # 自动清理设置
        self.auto_cleanup_checkbox = QCheckBox("启用自动清理过期Cookie")
        self.auto_cleanup_checkbox.setChecked(True)
        cookie_layout.addRow("自动清理:", self.auto_cleanup_checkbox)
        
        parent_layout.addWidget(cookie_group)
        
    def create_automation_settings_section(self, parent_layout):
        """
        创建自动化设置区域
        
        Args:
            parent_layout: 父布局对象
        """
        automation_group = QGroupBox("自动化参数设置")
        automation_layout = QFormLayout(automation_group)
        
        # 登录超时时间
        self.login_timeout_spinbox = QSpinBox()
        self.login_timeout_spinbox.setRange(60, 1800)  # 1分钟到30分钟
        self.login_timeout_spinbox.setValue(300)  # 默认5分钟
        self.login_timeout_spinbox.setSuffix(" 秒")
        automation_layout.addRow("登录超时时间:", self.login_timeout_spinbox)
        
        # 浏览器设置
        self.browser_combo = QComboBox()
        self.browser_combo.addItems(["Chrome", "Firefox", "Edge"])
        self.browser_combo.setCurrentText("Chrome")
        automation_layout.addRow("默认浏览器:", self.browser_combo)
        
        # 无头模式
        self.headless_checkbox = QCheckBox("启用无头模式（后台运行）")
        self.headless_checkbox.setChecked(False)
        automation_layout.addRow("运行模式:", self.headless_checkbox)
        
        # 重试次数
        self.retry_count_spinbox = QSpinBox()
        self.retry_count_spinbox.setRange(0, 10)
        self.retry_count_spinbox.setValue(3)
        automation_layout.addRow("失败重试次数:", self.retry_count_spinbox)
        
        parent_layout.addWidget(automation_group)
        
    def create_system_settings_section(self, parent_layout):
        """
        创建系统设置区域
        
        Args:
            parent_layout: 父布局对象
        """
        system_group = QGroupBox("系统参数设置")
        system_layout = QFormLayout(system_group)
        
        # 日志级别
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"])
        self.log_level_combo.setCurrentText("INFO")
        system_layout.addRow("日志级别:", self.log_level_combo)
        
        # 最大工作线程数
        self.max_workers_spinbox = QSpinBox()
        self.max_workers_spinbox.setRange(1, 16)
        self.max_workers_spinbox.setValue(4)
        system_layout.addRow("最大工作线程数:", self.max_workers_spinbox)
        
        # 启动时检查更新
        self.check_update_checkbox = QCheckBox("启动时检查更新")
        self.check_update_checkbox.setChecked(True)
        system_layout.addRow("自动更新:", self.check_update_checkbox)
        
        # 保存窗口状态
        self.save_window_state_checkbox = QCheckBox("保存窗口位置和大小")
        self.save_window_state_checkbox.setChecked(True)
        system_layout.addRow("窗口状态:", self.save_window_state_checkbox)
        
        parent_layout.addWidget(system_group)
        
    def create_action_buttons_section(self, parent_layout):
        """
        创建操作按钮区域
        
        Args:
            parent_layout: 父布局对象
        """
        button_layout = QHBoxLayout()
        
        # 保存设置按钮
        self.save_button = QPushButton("💾 保存设置")
        self.save_button.clicked.connect(self.save_settings)
        
        # 重置设置按钮
        self.reset_button = QPushButton("🔄 重置为默认")
        self.reset_button.setObjectName("secondaryButton")
        self.reset_button.clicked.connect(self.reset_settings)
        
        # 导出设置按钮
        self.export_button = QPushButton("📤 导出设置")
        self.export_button.setObjectName("secondaryButton")
        self.export_button.clicked.connect(self.export_settings)
        
        # 导入设置按钮
        self.import_button = QPushButton("📥 导入设置")
        self.import_button.setObjectName("secondaryButton")
        self.import_button.clicked.connect(self.import_settings)
        
        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.reset_button)
        button_layout.addWidget(self.export_button)
        button_layout.addWidget(self.import_button)
        button_layout.addStretch()
        
        parent_layout.addLayout(button_layout)
        
    def browse_cookie_path(self):
        """浏览Cookie存储路径"""
        current_path = self.cookie_path_edit.text() or self.cookie_manager.get_storage_path()
        
        selected_path = QFileDialog.getExistingDirectory(
            self, "选择Cookie存储目录", current_path
        )
        
        if selected_path:
            self.cookie_path_edit.setText(selected_path)
            
    def load_settings(self):
        """加载设置"""
        try:
            # Cookie设置
            cookie_path = self.config_manager.get('automation.cookie_storage_path', 
                                                self.cookie_manager.get_storage_path())
            self.cookie_path_edit.setText(cookie_path)
            
            cookie_max_age = self.config_manager.get('automation.cookie_max_age_days', 30)
            self.cookie_max_age_spinbox.setValue(cookie_max_age)
            
            auto_cleanup = self.config_manager.get('automation.auto_cleanup_cookies', True)
            self.auto_cleanup_checkbox.setChecked(auto_cleanup)
            
            # 自动化设置
            login_timeout = self.config_manager.get('automation.login_timeout_seconds', 300)
            self.login_timeout_spinbox.setValue(login_timeout)
            
            browser = self.config_manager.get('automation.default_browser', 'Chrome')
            self.browser_combo.setCurrentText(browser)
            
            headless = self.config_manager.get('automation.headless_mode', False)
            self.headless_checkbox.setChecked(headless)
            
            retry_count = self.config_manager.get('automation.retry_count', 3)
            self.retry_count_spinbox.setValue(retry_count)
            
            # 系统设置
            log_level = self.config_manager.get('logging.level', 'INFO')
            self.log_level_combo.setCurrentText(log_level)
            
            max_workers = self.config_manager.get('system.max_workers', 4)
            self.max_workers_spinbox.setValue(max_workers)
            
            check_update = self.config_manager.get('system.check_update_on_startup', True)
            self.check_update_checkbox.setChecked(check_update)
            
            save_window_state = self.config_manager.get('ui.save_window_state', True)
            self.save_window_state_checkbox.setChecked(save_window_state)
            
            self.logger.info("设置已加载")
            
        except Exception as e:
            self.logger.error(f"加载设置失败: {e}")
            QMessageBox.warning(self, "错误", f"加载设置失败：{e}")
            
    def save_settings(self):
        """保存设置"""
        try:
            # Cookie设置
            cookie_path = self.cookie_path_edit.text().strip()
            if cookie_path:
                self.config_manager.set('automation.cookie_storage_path', cookie_path)
                # 更新Cookie管理器的存储路径
                self.cookie_manager.set_storage_path(cookie_path)
                
            self.config_manager.set('automation.cookie_max_age_days', 
                                  self.cookie_max_age_spinbox.value())
            self.config_manager.set('automation.auto_cleanup_cookies', 
                                  self.auto_cleanup_checkbox.isChecked())
            
            # 自动化设置
            self.config_manager.set('automation.login_timeout_seconds', 
                                  self.login_timeout_spinbox.value())
            self.config_manager.set('automation.default_browser', 
                                  self.browser_combo.currentText())
            self.config_manager.set('automation.headless_mode', 
                                  self.headless_checkbox.isChecked())
            self.config_manager.set('automation.retry_count', 
                                  self.retry_count_spinbox.value())
            
            # 系统设置
            self.config_manager.set('logging.level', self.log_level_combo.currentText())
            self.config_manager.set('system.max_workers', self.max_workers_spinbox.value())
            self.config_manager.set('system.check_update_on_startup', 
                                  self.check_update_checkbox.isChecked())
            self.config_manager.set('ui.save_window_state', 
                                  self.save_window_state_checkbox.isChecked())
            
            # 保存配置文件
            self.config_manager.save_config()
            
            # 发送设置变更信号
            self.settings_changed.emit("all", None)
            
            QMessageBox.information(self, "成功", "设置已保存！")
            self.logger.info("设置已保存")
            
        except Exception as e:
            self.logger.error(f"保存设置失败: {e}")
            QMessageBox.critical(self, "错误", f"保存设置失败：{e}")
            
    def reset_settings(self):
        """重置为默认设置"""
        reply = QMessageBox.question(
            self, "确认重置",
            "确定要重置所有设置为默认值吗？\n此操作不可撤销。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                # 重置配置管理器
                self.config_manager.reset_to_default()
                self.config_manager.save_config()
                
                # 重新加载设置
                self.load_settings()
                
                QMessageBox.information(self, "成功", "设置已重置为默认值！")
                self.logger.info("设置已重置为默认值")
                
            except Exception as e:
                self.logger.error(f"重置设置失败: {e}")
                QMessageBox.critical(self, "错误", f"重置设置失败：{e}")
                
    def export_settings(self):
        """导出设置"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出设置", "kuaishou_settings.json", "JSON文件 (*.json)"
            )
            
            if file_path:
                import json
                settings = self.config_manager.get_all()
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(settings, f, ensure_ascii=False, indent=2)
                    
                QMessageBox.information(self, "成功", f"设置已导出到：{file_path}")
                self.logger.info(f"设置已导出到: {file_path}")
                
        except Exception as e:
            self.logger.error(f"导出设置失败: {e}")
            QMessageBox.critical(self, "错误", f"导出设置失败：{e}")
            
    def import_settings(self):
        """导入设置"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "导入设置", "", "JSON文件 (*.json)"
            )
            
            if file_path:
                import json
                
                with open(file_path, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    
                # 更新配置
                self.config_manager.update(settings)
                self.config_manager.save_config()
                
                # 重新加载设置
                self.load_settings()
                
                QMessageBox.information(self, "成功", "设置已导入！")
                self.logger.info(f"设置已从 {file_path} 导入")
                
        except Exception as e:
            self.logger.error(f"导入设置失败: {e}")
            QMessageBox.critical(self, "错误", f"导入设置失败：{e}")
