# 快手无人带货自动化工具

一个基于PyQt6开发的快手无人带货自动化管理工具，提供账号管理、商品管理、直播设置等功能。

## 🚀 项目特性

- **现代化界面**: 采用PyQt6框架，快手官方配色主题
- **模块化设计**: 遵循MECE原则，功能模块相互独立
- **数据持久化**: 基于SQLAlchemy的数据库管理
- **响应式布局**: 支持窗口大小调整，适配不同屏幕
- **完整测试**: 包含单元测试和集成测试

## 📋 功能模块

### ✅ 已实现功能

1. **账号管理模块**
   - 账号信息的增删改查
   - 账号状态监控（在线/离线）
   - 账号类型分类管理
   - 批量操作支持

2. **数据管理系统**
   - SQLite数据库存储
   - 数据模型验证
   - 配置文件管理
   - 日志记录系统

3. **用户界面**
   - 左侧导航栏设计
   - 账号列表表格显示
   - 添加/编辑账号对话框
   - 快手主题样式

### 🔄 规划中功能

- 商品管理模块
- 直播设置模块
- 数据统计分析
- 自动化规则配置
- 系统设置管理

## 🛠️ 技术栈

- **前端框架**: PyQt6
- **数据库**: SQLite + SQLAlchemy ORM
- **配置管理**: JSON配置文件
- **日志系统**: Python logging
- **测试框架**: unittest

## 📁 项目结构

```
kuaishou_automation_tool/
├── main.py                     # 应用程序入口
├── requirements.txt            # 依赖包列表
├── test_core.py               # 核心功能测试
├── config/                    # 配置文件目录
├── ui/                        # 用户界面层
│   ├── main_window.py         # 主窗口
│   ├── components/            # UI组件
│   │   ├── navigation.py      # 导航栏组件
│   │   └── account_widget.py  # 账号管理组件
│   └── styles/                # 样式文件
│       └── kuaishou_theme.qss # 快手主题样式
├── business/                  # 业务逻辑层
├── data/                      # 数据访问层
│   ├── models/                # 数据模型
│   │   ├── base.py           # 基础模型
│   │   └── account.py        # 账号模型
│   └── repositories/          # 数据仓库
│       └── account_repository.py # 账号数据访问
├── utils/                     # 工具支持层
│   └── logger.py             # 日志工具
└── tests/                     # 测试目录
```

## 🔧 安装和运行

### 环境要求

- Python 3.9+
- Windows 10/11 / Linux / macOS
- PySide6 (推荐) 或 PyQt6 (GUI版本)

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd kuaishou_automation_tool
   ```

2. **创建虚拟环境**
   ```bash
   python -m venv venv
   source venv/Scripts/activate  # Windows
   # 或
   source venv/bin/activate      # Linux/Mac
   ```

3. **安装依赖**
   ```bash
   # 安装核心依赖
   pip install sqlalchemy

   # 安装GUI依赖 (推荐PySide6)
   pip install PySide6
   ```

4. **运行测试**
   ```bash
   python test_core.py
   ```

5. **启动应用 (推荐使用启动脚本)**
   ```bash
   # 自动选择最佳启动方式
   python start.py

   # 或手动选择
   python start.py --gui    # GUI版本
   python start.py --cli    # 命令行版本
   ```

### 常见问题

#### Qt库导入失败
如果遇到Qt库导入错误，请按以下步骤解决：

1. **推荐解决方案：使用PySide6**
   ```bash
   pip uninstall PyQt6 -y
   pip install PySide6
   ```

2. **如果仍要使用PyQt6**
   - Windows: 安装 Visual C++ Redistributable
   - 重新安装PyQt6：
     ```bash
     pip uninstall PyQt6 -y
     pip install PyQt6==6.4.0
     ```

3. **备用方案：使用命令行版本**
   ```bash
   python start.py --cli
   ```

#### 数据库连接问题
- 确保有写入权限到项目目录
- 检查SQLite是否正确安装

#### 启动问题
- 使用 `python start.py` 自动检测环境
- 查看启动日志了解具体错误信息

## 🧪 测试

### 运行核心功能测试
```bash
python test_core.py
```

测试覆盖：
- ✅ 数据库连接和初始化
- ✅ 配置管理系统
- ✅ 账号数据模型
- ✅ 数据仓库CRUD操作
- ✅ 日志记录系统

### 测试结果示例
```
======================================================================
快手无人带货工具 - 核心功能测试
======================================================================
🎉 所有核心功能测试通过！
✅ 数据库连接正常
✅ 配置管理正常
✅ 数据模型正常
✅ 数据仓库正常
✅ 日志系统正常
```

## 📊 开发进度

| 模块 | 状态 | 完成度 |
|------|------|--------|
| 项目架构设计 | ✅ 完成 | 100% |
| 数据模型设计 | ✅ 完成 | 100% |
| 账号管理界面 | ✅ 完成 | 100% |
| 导航栏组件 | ✅ 完成 | 100% |
| 样式主题 | ✅ 完成 | 100% |
| 核心功能测试 | ✅ 完成 | 100% |
| 商品管理 | 🔄 规划中 | 0% |
| 直播设置 | 🔄 规划中 | 0% |
| 数据统计 | 🔄 规划中 | 0% |

## 🎨 界面预览

### 主界面布局
- **左侧导航栏**: 功能模块切换
- **右侧内容区**: 具体功能界面
- **快手配色**: 红色主题 (#FF6B35) + 深色背景

### 账号管理界面
- **账号列表**: 表格形式显示
- **操作按钮**: 添加、编辑、删除、刷新
- **状态监控**: 在线/离线状态显示
- **统计信息**: 账号数量统计

## 🔮 后续规划

### 短期目标 (1-2周)
- [ ] 完善GUI界面兼容性
- [ ] 实现商品管理模块
- [ ] 添加直播设置功能

### 中期目标 (1个月)
- [ ] 数据统计和分析
- [ ] 自动化规则配置
- [ ] 系统设置管理

### 长期目标 (3个月)
- [ ] 实际快手API集成
- [ ] 自动化脚本执行
- [ ] 多账号批量操作

## 📝 开发规范

项目严格遵循以下开发原则：

1. **MECE原则**: 功能模块相互独立，完全穷尽
2. **代码质量**: 完整的注释和文档字符串
3. **测试驱动**: 每个功能都有对应测试
4. **模块化设计**: 清晰的层次结构和职责分离

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和平台服务条款。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

---

**注意**: 本工具仅为技术演示项目，实际使用时请确保遵守快手平台的服务条款和相关法律法规。
