"""
元素检测测试工具
用于测试快手页面的登录成功元素检测
"""

import sys
import os
import time

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_element_detection():
    """测试元素检测"""
    try:
        from selenium import webdriver
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        from selenium.webdriver.chrome.options import Options
        from selenium.common.exceptions import TimeoutException
        
        print("🔍 启动元素检测测试...")
        
        # 设置Chrome选项
        options = Options()
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--start-maximized")
        
        # 创建WebDriver
        print("🚀 启动浏览器...")
        driver = webdriver.Chrome(options=options)
        
        # 导航到快手登录页面
        print("🌐 打开快手登录页面...")
        driver.get("https://passport.kuaishou.com/pc/account/login/?sid=kuaishou.web.cp.api&callback=https%3A%2F%2Fcp.kuaishou.com%2Frest%2Finfra%2Fsts%3FfollowUrl%3Dhttps%253A%252F%252Fcp.kuaishou.com%252Fprofile%26setRootDomain%3Dtrue")
        
        print("⏳ 请在浏览器中完成登录，然后按回车键继续...")
        input()
        
        # 测试多个可能的元素
        test_selectors = [
            "/html/body/div[1]/div[1]/header/section[2]/div[4]/span/div[2]/div[2]",  # 指定元素
            "/html/body/div[1]/div[1]/header",  # header元素
            "//header",  # 任何header
            "//div[contains(@class, 'header')]",  # header类
            "//span//div[2]//div[2]",  # 简化的目标元素
            "//*[contains(@class, 'avatar')]",  # 头像元素
            "//*[contains(@class, 'user')]",  # 用户相关元素
        ]
        
        print("\n🔍 开始检测元素...")
        
        for i, selector in enumerate(test_selectors):
            print(f"\n{i+1}. 测试元素: {selector}")
            try:
                # 短时间等待，看元素是否存在
                wait = WebDriverWait(driver, 2)
                element = wait.until(
                    EC.presence_of_element_located((By.XPATH, selector))
                )
                print(f"   ✅ 找到元素！")
                print(f"   📝 元素文本: '{element.text[:50]}...' " if element.text else "   📝 元素无文本内容")
                print(f"   🏷️  元素标签: {element.tag_name}")
                
                # 尝试获取元素属性
                try:
                    class_attr = element.get_attribute("class")
                    if class_attr:
                        print(f"   🎨 CSS类: {class_attr}")
                except:
                    pass
                    
            except TimeoutException:
                print(f"   ❌ 元素不存在或未加载")
            except Exception as e:
                print(f"   ⚠️ 检测出错: {e}")
        
        # 获取当前页面信息
        print(f"\n📄 当前页面信息:")
        print(f"   🔗 URL: {driver.current_url}")
        print(f"   📋 标题: {driver.title}")
        
        # 尝试获取页面源码中的关键信息
        print(f"\n🔍 页面源码分析:")
        page_source = driver.page_source
        
        keywords = ["header", "avatar", "user", "profile", "login", "logout"]
        for keyword in keywords:
            count = page_source.lower().count(keyword)
            print(f"   '{keyword}': 出现 {count} 次")
        
        # 尝试执行JavaScript获取元素
        print(f"\n🔧 JavaScript检测:")
        try:
            # 检查目标元素
            js_result = driver.execute_script("""
                var element = document.evaluate('/html/body/div[1]/div[1]/header/section[2]/div[4]/span/div[2]/div[2]', 
                                               document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
                if (element.singleNodeValue) {
                    return {
                        found: true,
                        text: element.singleNodeValue.textContent,
                        tagName: element.singleNodeValue.tagName,
                        className: element.singleNodeValue.className
                    };
                } else {
                    return {found: false};
                }
            """)
            
            if js_result['found']:
                print(f"   ✅ JavaScript找到目标元素！")
                print(f"   📝 文本: {js_result.get('text', 'N/A')}")
                print(f"   🏷️  标签: {js_result.get('tagName', 'N/A')}")
                print(f"   🎨 类名: {js_result.get('className', 'N/A')}")
            else:
                print(f"   ❌ JavaScript未找到目标元素")
                
        except Exception as e:
            print(f"   ⚠️ JavaScript执行出错: {e}")
        
        print(f"\n⏸️  测试完成，按回车键关闭浏览器...")
        input()
        
        driver.quit()
        print("✅ 测试结束")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_element_detection()
