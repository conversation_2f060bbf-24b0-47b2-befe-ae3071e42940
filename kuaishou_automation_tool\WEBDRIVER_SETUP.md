# WebDriver 环境配置指南

## 概述

快手无人带货自动化工具的自动化登录功能需要使用WebDriver来控制浏览器。本工具已配置为直接使用系统环境中的WebDriver，无需自动下载。

## 支持的浏览器

工具按以下优先级尝试启动浏览器：
1. **Chrome** (推荐)
2. **Firefox**
3. **Microsoft Edge**

## WebDriver 安装方法

### 方法一：Chrome + ChromeDriver (推荐)

#### 1. 安装Chrome浏览器
- 下载地址：https://www.google.com/chrome/
- 安装最新版本的Chrome浏览器

#### 2. 下载ChromeDriver
- 访问：https://chromedriver.chromium.org/
- 或者：https://googlechromelabs.github.io/chrome-for-testing/
- 下载与您的Chrome版本匹配的ChromeDriver

#### 3. 配置环境变量
**Windows:**
```bash
# 将chromedriver.exe放到以下任一位置：
# 1. C:\Windows\System32\
# 2. 或者添加到PATH环境变量中
```

**macOS/Linux:**
```bash
# 将chromedriver放到 /usr/local/bin/ 或其他PATH目录
sudo mv chromedriver /usr/local/bin/
sudo chmod +x /usr/local/bin/chromedriver
```

### 方法二：Firefox + GeckoDriver

#### 1. 安装Firefox浏览器
- 下载地址：https://www.mozilla.org/firefox/

#### 2. 下载GeckoDriver
- 访问：https://github.com/mozilla/geckodriver/releases
- 下载最新版本的GeckoDriver

#### 3. 配置环境变量
将geckodriver添加到系统PATH中，方法同ChromeDriver。

### 方法三：Microsoft Edge + EdgeDriver

#### 1. 确认Edge浏览器
- Windows 10/11通常已预装Edge浏览器

#### 2. 下载EdgeDriver
- 访问：https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/
- 下载与Edge版本匹配的EdgeDriver

#### 3. 配置环境变量
将msedgedriver.exe添加到系统PATH中。

## 验证安装

### 检查浏览器版本
```bash
# Chrome
chrome --version

# Firefox
firefox --version

# Edge (Windows)
msedge --version
```

### 检查WebDriver
```bash
# ChromeDriver
chromedriver --version

# GeckoDriver
geckodriver --version

# EdgeDriver
msedgedriver --version
```

### 测试WebDriver
在命令行中运行以下命令测试：

```python
# 测试Chrome
from selenium import webdriver
driver = webdriver.Chrome()
driver.get("https://www.baidu.com")
driver.quit()

# 测试Firefox
driver = webdriver.Firefox()
driver.get("https://www.baidu.com")
driver.quit()

# 测试Edge
driver = webdriver.Edge()
driver.get("https://www.baidu.com")
driver.quit()
```

## 常见问题

### 1. "chromedriver not found" 错误
**解决方案：**
- 确认ChromeDriver已下载并放在PATH目录中
- 检查ChromeDriver版本是否与Chrome浏览器版本匹配
- 在Windows上确保文件名为 `chromedriver.exe`

### 2. 版本不匹配错误
**解决方案：**
- 检查浏览器版本：在浏览器地址栏输入 `chrome://version/`
- 下载对应版本的WebDriver
- 确保主版本号匹配（如Chrome 120.x 对应 ChromeDriver 120.x）

### 3. 权限错误
**解决方案：**
```bash
# macOS/Linux
sudo chmod +x /path/to/webdriver

# Windows: 以管理员身份运行命令提示符
```

### 4. PATH环境变量问题
**Windows 配置PATH：**
1. 右键"此电脑" → "属性"
2. "高级系统设置" → "环境变量"
3. 在"系统变量"中找到"Path"
4. 点击"编辑" → "新建"
5. 添加WebDriver所在目录路径

**验证PATH配置：**
```bash
# 在命令行中应该能直接运行
chromedriver --version
```

## 自动化登录使用说明

1. **启动工具**：运行 `python start.py --gui`
2. **添加账号**：点击"添加账号"按钮
3. **填写信息**：输入用户名等基本信息
4. **启用自动化**：勾选"添加账号后立即进行自动化登录配置"
5. **确认添加**：点击"确认添加"按钮
6. **浏览器登录**：在自动打开的浏览器中完成快手登录
7. **保存Cookie**：登录成功后确认保存Cookie文件

## 技术支持

如果遇到WebDriver配置问题：

1. **检查系统环境**：确认浏览器和WebDriver版本匹配
2. **查看错误日志**：工具会显示详细的错误信息
3. **尝试其他浏览器**：工具会自动尝试Chrome → Firefox → Edge
4. **手动测试**：使用上述Python代码手动测试WebDriver

## 注意事项

- **版本兼容性**：定期更新浏览器和WebDriver以保持兼容
- **安全考虑**：WebDriver具有完整的浏览器控制权限，请确保来源可信
- **网络环境**：某些企业网络可能限制WebDriver的使用
- **系统资源**：自动化操作会消耗一定的系统资源

## 更新维护

建议定期检查和更新：
- 浏览器版本
- WebDriver版本
- Selenium库版本

保持这些组件的版本同步可以避免大部分兼容性问题。
