#!/usr/bin/env python3
"""
测试文本清理功能
"""

import re

def clean_text_for_speech(text: str) -> str:
    """清理文本，移除不适合语音合成的内容"""
    
    print(f"原始文本: {text}")
    print("-" * 50)
    
    # 移除网址
    original_text = text
    text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
    text = re.sub(r'www\.(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
    if text != original_text:
        print("✅ 移除了网址")
    
    # 移除邮箱地址
    original_text = text
    text = re.sub(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', '', text)
    if text != original_text:
        print("✅ 移除了邮箱地址")
    
    # 移除电话号码
    original_text = text
    text = re.sub(r'\b\d{3,4}[-.\s]?\d{3,4}[-.\s]?\d{4}\b', '', text)
    text = re.sub(r'\b1[3-9]\d{9}\b', '', text)
    if text != original_text:
        print("✅ 移除了电话号码")
    
    # 移除特殊符号和多余空格
    original_text = text
    text = re.sub(r'[#@$%^&*()_+=\[\]{}|\\:";\'<>?,./]', ' ', text)
    text = re.sub(r'\s+', ' ', text)
    if text != original_text:
        print("✅ 移除了特殊符号")
    
    # 移除数字串（超过4位的纯数字）
    original_text = text
    text = re.sub(r'\b\d{5,}\b', '', text)
    if text != original_text:
        print("✅ 移除了长数字串")
    
    # 清理首尾空格
    text = text.strip()
    
    print("-" * 50)
    print(f"清理后文本: {text}")
    print(f"长度变化: {len(text)} (原: {len(text)})")
    
    return text

def test_text_cleaning():
    """测试文本清理功能"""
    print("🧹 文本清理功能测试")
    print("=" * 60)
    
    # 测试用例
    test_cases = [
        # 包含网址的文本
        """
        欢迎来到我们的直播间！今天为大家推荐这款超值好物。
        更多详情请访问：https://www.example.com/product
        这个产品有以下几个特点：质量优良、价格实惠、性价比超高。
        现在下单还有特别优惠，机会难得，不要错过！
        """,
        
        # 包含联系方式的文本
        """
        欢迎来到我们的直播间！
        客服微信：<EMAIL>
        客服电话：400-123-4567
        手机号码：13812345678
        这款产品质量很好，大家可以放心购买。
        """,
        
        # 包含特殊符号的文本
        """
        【限时优惠】这款产品原价￥299，现在只要￥199！！！
        产品编号：#PRD123456789
        优惠码：SAVE50%OFF
        立即下单>>>www.shop.com<<<
        """,
        
        # 正常的带货文案
        """
        欢迎来到我们的直播间！今天为大家推荐这款超值好物。
        这个产品有以下几个特点：质量优良、价格实惠、性价比超高。
        现在下单还有特别优惠，机会难得，不要错过！
        点击下方链接立即购买，数量有限，先到先得！
        """
    ]
    
    for i, test_text in enumerate(test_cases, 1):
        print(f"\n📝 测试用例 {i}:")
        print("=" * 40)
        
        cleaned = clean_text_for_speech(test_text.strip())
        
        print(f"\n🎤 适合语音合成: {'✅ 是' if cleaned and len(cleaned) > 10 else '❌ 否'}")
        print("=" * 40)

def main():
    """主函数"""
    print("🎤 语音文本清理测试工具")
    print("=" * 60)
    print("这个工具用于测试文本清理功能，确保语音合成不会读出网址等无关内容。")
    print()
    
    test_text_cleaning()
    
    print("\n" + "=" * 60)
    print("📋 清理规则说明:")
    print("• 移除网址 (http://, https://, www.)")
    print("• 移除邮箱地址")
    print("• 移除电话号码")
    print("• 移除特殊符号 (#@$%^&*等)")
    print("• 移除长数字串 (超过4位)")
    print("• 清理多余空格")
    
    print("\n💡 使用建议:")
    print("• 在输入文案时避免包含网址和联系方式")
    print("• 使用'🔍 预览清理后的文本'按钮检查效果")
    print("• 专注于产品介绍和购买引导的文字内容")
    
    print("\n🎉 现在语音合成不会再读出网址了！")

if __name__ == "__main__":
    try:
        main()
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
