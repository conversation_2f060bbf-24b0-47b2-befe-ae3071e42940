Metadata-Version: 2.1
Name: qt6-applications
Version: 6.1.0.2.2
Summary: The collection of Qt tools easily installable in Python
Home-page: https://github.com/altendky/qt-applications
Author: <PERSON>
Author-email: <EMAIL>
License: LGPLv3
Platform: UNKNOWN
Classifier: Development Status :: 4 - Beta
Classifier: Environment :: Win32 (MS Windows)
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: GNU Lesser General Public License v3 (LGPLv3)
Classifier: Operating System :: POSIX :: Linux
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: Microsoft :: Windows
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Topic :: Software Development
Classifier: Topic :: Utilities
Requires-Python: >=3.5
Description-Content-Type: text/x-rst
License-File: COPYING

===============
qt-applications
===============


|PyPI| |Pythons| |GitHub|

Perhaps docs will follow but for now see `the pyqt-tools readme`_ and
`the qt-tools readme`_.  This package provides just the collection of Qt
applications to avoid repeating the same large files in several packages for
different Python versions.  These files are not intended to be used directly.


.. |PyPI| image:: https://img.shields.io/pypi/v/qt5-applications.svg
   :alt: PyPI version
   :target: https://pypi.org/project/qt5-applications/

.. |Pythons| image:: https://img.shields.io/pypi/pyversions/qt5-applications.svg
   :alt: supported Python versions
   :target: https://pypi.org/project/qt5-applications/

.. |GitHub| image:: https://img.shields.io/github/last-commit/altendky/qt-applications/main.svg
   :alt: source on GitHub
   :target: https://github.com/altendky/qt-applications

.. _`the pyqt-tools readme`: https://github.com/altendky/pyqt-tools#pyqt-tools
.. _`the qt-tools readme`: https://github.com/altendky/qt-tools#qt-tools


