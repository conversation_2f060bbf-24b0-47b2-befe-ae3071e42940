from enum import IntFlag

import comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4 as __wrapper_module__
from comtypes.gen._C866CA3A_32F7_11D2_9602_00C04F8EE628_0_5_4 import (
    SVEStartInputStream, DISPID_SRCPause, SAFTCCITT_ALaw_22kHzMono,
    SPWORDPRONUNCIATIONLIST, DISPID_SOTIsUISupported, SVEViseme,
    DISPID_SRGReset, eWORDTYPE_DELETED, SAFTCCITT_ALaw_11kHzStereo,
    SWTAdded, eLEXTYPE_MORPHOLOGY, SPWF_SRENGINE,
    DISPID_SPIProperties, VARIANT, SSTTWildcard, SVEPrivate,
    eLEXTYPE_RESERVED10, ISpeechRecoContext, SDTAll, eLEXTYPE_APP,
    SpeechMicTraining, DISPID_SPIAudioSizeTime, SAFT44kHz8BitMono,
    ISpRecoContext, SBONone, SECFIgnore<PERSON>idth, SAFT12k<PERSON><PERSON>16<PERSON><PERSON>Stereo,
    DISPID_SLWWord, DISPID_SDKOpenKey, ISpeechPhraseInfo, SRTStandard,
    SGDSActiveWithAutoPause, SpeechAudioFormatGUIDWave,
    ISpeechFileStream, SVPOver, SAFTCCITT_ALaw_8kHzStereo, SBOPause,
    eLEXTYPE_PRIVATE11, DISPID_SRSAudioStatus, DISPID_SLGetWords,
    SPEI_RESERVED3, ISpeechXMLRecoResult, SPDKL_LocalMachine,
    ISpeechRecoResult, DISPID_SGRSTransitions, ISpRecognizer3,
    DISPID_SLWsCount, SPGS_EXCLUSIVE, SRCS_Disabled, SRARoot,
    DISPID_SOTId, DISPID_SPEsItem, ISpObjectToken, SP_VISEME_1,
    SpMemoryStream, SRADynamic, DISPID_SPPNumberOfElements,
    SpeechCategoryAppLexicons, SP_VISEME_8, SPEI_INTERFERENCE,
    SAFT32kHz16BitMono, ISpeechRecoResultDispatch, DISPID_SRIsShared,
    ISpEventSink, LONG_PTR, SRESoundEnd, SREAdaptation,
    DISPID_SOTCGetDataKey, DISPID_SRGRules, DISPID_SLPsItem,
    SAFT44kHz16BitStereo, SREFalseRecognition,
    DISPIDSPTSI_SelectionLength, SPCS_ENABLED,
    DISPID_SRCVoicePurgeEvent, SVP_1, SVP_6, SpeechAddRemoveWord,
    DISPID_SWFESamplesPerSec, SPRECOGNIZERSTATUS,
    DISPID_SRGCmdLoadFromObject, _check_version, SpPhoneConverter,
    SPSHT_EMAIL, SPEI_VISEME, DISPID_SVSLastStreamNumberQueued,
    DISPID_SRSetPropertyString, SGRSTTRule, IEnumSpObjectTokens,
    DISPID_SGRClear, DISPID_SPRuleFirstElement, SPSERIALIZEDPHRASE,
    SAFTADPCM_44kHzMono, SPPHRASERULE, SPINTERFERENCE_NOISE,
    SPEI_ADAPTATION, SPRST_INACTIVE, STCAll, ISpeechGrammarRuleState,
    SP_VISEME_3, DISPID_SOTDisplayUI, DISPID_SWFEExtraData,
    SAFT11kHz16BitMono, SPFM_CREATE, HRESULT, SPPS_Noncontent,
    SPPS_Function, SVPAlert, SVP_18, DISPID_SMSADeviceId,
    DISPID_SRGIsPronounceable, SRERequestUI, eLEXTYPE_PRIVATE8,
    DISPID_SLAddPronunciationByPhoneIds, SVP_17,
    SSSPTRelativeToCurrentPosition, SAFTCCITT_uLaw_8kHzMono,
    DISPID_SRCState, ISpeechMemoryStream, DISPID_SRRRecoContext,
    SPAS_PAUSE, ISpeechLexiconWords, SVP_4, ISequentialStream,
    DISPID_SPAsItem, SpObjectTokenCategory, SPAO_NONE,
    SPEI_SENTENCE_BOUNDARY, SPINTERFERENCE_LATENCY_TRUNCATE_END,
    eLEXTYPE_PRIVATE3, DISPID_SVEBookmark, SPAS_CLOSED,
    eLEXTYPE_PRIVATE15, SPPS_RESERVED1, SPGS_ENABLED,
    DISPID_SPEs_NewEnum, DISPID_SMSGetData, DISPID_SVPriority,
    DISPID_SVStatus, ISpeechPhraseProperties, ISpeechAudioBufferInfo,
    DISPID_SRRTTickCount, DISPID_SVEViseme,
    DISPID_SVSpeakCompleteEvent, SPWT_DISPLAY, SpeechCategoryAudioIn,
    SPSLMA, DISPID_SVEWord, SP_VISEME_16,
    SPWP_UNKNOWN_WORD_PRONOUNCEABLE, DISPID_SGRSAddRuleTransition,
    DISPID_SVSkip, DISPID_SLGenerationId, ISpObjectTokenCategory,
    eLEXTYPE_PRIVATE12, DISPID_SRCEPropertyNumberChange,
    SpSharedRecognizer, SpeechRecoProfileProperties,
    SAFTCCITT_uLaw_11kHzMono, DISPID_SRGSetTextSelection,
    ISpRecognizer2, DISPIDSPTSI_SelectionOffset,
    SAFTCCITT_uLaw_8kHzStereo, SVSFPurgeBeforeSpeak, tagSTATSTG,
    DISPID_SGRAddResource, DISPID_SASNonBlockingIO, SPRULE,
    DISPID_SVSpeakStream, DISPID_SOTsCount, DISPID_SVResume,
    ISpeechAudioFormat, SPEI_HYPOTHESIS, _FILETIME,
    DISPID_SRCEInterference, SPPHRASEELEMENT,
    DISPID_SPEEngineConfidence, SpSharedRecoContext, SASPause,
    SITooSlow, SAFT12kHz8BitStereo, DISPID_SWFEChannels,
    DISPID_SPEDisplayAttributes, SRAInterpreter, SAFT12kHz16BitMono,
    DISPID_SGRSTs_NewEnum, SPWORD, DISPID_SPRuleConfidence,
    DISPID_SRState, DISPID_SRCEventInterests,
    SpeechGrammarTagWildcard, SRTReSent,
    SpeechCategoryPhoneConverters, SPEI_SOUND_START,
    DISPID_SRAllowAudioInputFormatChangesOnNextSet, SVP_11,
    SREAllEvents, SAFTCCITT_ALaw_8kHzMono, DISPID_SRIsUISupported,
    DISPID_SRRDiscardResultInfo, SP_VISEME_11, SpLexicon,
    Speech_Default_Weight, DISPID_SVVolume, SREPrivate, SGDSInactive,
    ISpeechPhoneConverter, SRAImport, DISPID_SLWLangId,
    SGRSTTWildcard, SAFTCCITT_ALaw_11kHzMono, SPRST_ACTIVE_ALWAYS,
    DISPID_SPIGetText, DISPID_SPRText, DISPID_SGRs_NewEnum,
    ISpeechDataKey, DISPID_SPPFirstElement, DISPID_SAFGuid,
    SpeechUserTraining, SAFTText, DISPID_SRGCmdLoadFromMemory,
    SPDKL_CurrentUser, DISPID_SPRulesItem, SVSFNLPMask,
    ISpRecoCategory, ISpeechPhraseRule, BSTR, eLEXTYPE_RESERVED7,
    SVF_Emphasis, SpeechCategoryRecognizers, SP_VISEME_15,
    DISPID_SVEStreamStart, ISpResourceManager, DISPID_SRStatus,
    DISPID_SPCPhoneToId, DISPID_SWFEFormatTag, STSF_FlagCreate,
    SPPS_RESERVED2, ISpStream, SDTReplacement, ISpPhoneConverter,
    ISpeechRecoResultTimes, SPPS_Unknown, DISPID_SRGetPropertyString,
    SFTSREngine, DISPID_SRGCmdSetRuleState, SPEI_TTS_PRIVATE,
    DISPID_SRCERequestUI, SVSFlagsAsync, DISPID_SRCRequestedUIType,
    SPRS_ACTIVE_WITH_AUTO_PAUSE, DISPID_SRGId,
    SWPKnownWordPronounceable, DISPID_SPRulesCount,
    SpeechCategoryAudioOut, DISPID_SRRTimes, DISPID_SDKEnumKeys,
    SPSERIALIZEDRESULT, SPSMF_SAPI_PROPERTIES, SVP_5,
    SAFT44kHz16BitMono, STCInprocHandler, SDTDisplayText,
    DISPID_SRCVoice, IInternetSecurityMgrSite, SVSFUnusedFlags,
    SITooFast, SVP_10, DISPID_SGRSTsCount, SPEVENT,
    DISPID_SPAs_NewEnum, DISPID_SRAudioInputStream,
    SAFTGSM610_22kHzMono, DISPID_SRRecognizer, SAFTGSM610_8kHzMono,
    SPEI_END_INPUT_STREAM, SAFTADPCM_22kHzStereo, eLEXTYPE_PRIVATE4,
    SVF_Stressed, DISPID_SPPValue, ISpeechVoiceStatus,
    SPRECOCONTEXTSTATUS, STSF_LocalAppData, SRSEIsSpeaking,
    DISPID_SABufferInfo, SPEI_TTS_AUDIO_LEVEL, SPXRO_SML,
    DISPID_SPPConfidence, SGRSTTWord, SVEAllEvents, SPSInterjection,
    DISPID_SASCurrentDevicePosition, SpPhraseInfoBuilder,
    SpeechPropertyLowConfidenceThreshold, ISpeechPhraseProperty,
    DISPID_SVSPhonemeId, SREPropertyStringChange, DISPID_SDKCreateKey,
    _ISpeechRecoContextEvents, ISpeechMMSysAudio,
    DISPID_SLRemovePronunciation, DISPID_SRCEHypothesis,
    SPCT_SUB_COMMAND, _ULARGE_INTEGER, SpStreamFormatConverter,
    SPPS_Noun, SPBO_NONE, SpeechAudioVolume,
    DISPID_SRGetPropertyNumber, SPAR_Unknown, ISpRecognizer,
    DISPID_SVWaitUntilDone, SPINTERFERENCE_NOSIGNAL, ISpPhraseAlt,
    DISPID_SDKGetStringValue, STCLocalServer,
    SDA_Consume_Leading_Spaces, Speech_Max_Word_Length,
    SpeechAudioFormatGUIDText, SAFT8kHz16BitMono, DISPID_SBSSeek,
    SREPropertyNumChange, SDKLCurrentConfig,
    SPRS_ACTIVE_USER_DELIMITED, SPSVerb, DISPID_SLPSymbolic,
    SPEI_FALSE_RECOGNITION, SWPUnknownWordPronounceable,
    DISPID_SVSLastResult, DISPID_SLWsItem, DISPID_SGRsAdd,
    DISPID_SRGCmdLoadFromFile, ISpeechRecoResult2,
    ISpeechGrammarRuleStateTransition, ISpeechGrammarRules,
    DISPID_SPRuleNumberOfElements, SPINTERFERENCE_TOOFAST,
    DISPID_SPIEngineId, SGDSActive, DISPID_SPPs_NewEnum,
    Speech_StreamPos_Asap, SPEI_END_SR_STREAM, WSTRING,
    DISPID_SRSClsidEngine, SpeechVoiceCategoryTTSRate,
    SpeechEngineProperties, SVSFIsNotXML,
    __MIDL___MIDL_itf_sapi_0000_0020_0002, DISPID_SRCEBookmark,
    SPEI_TTS_BOOKMARK, SSFMCreate, DISPID_SPIRule,
    DISPID_SGRSAddSpecialTransition, DISPID_SGRAttributes,
    DISPID_SRAllowVoiceFormatMatchingOnNextSet, SPTEXTSELECTIONINFO,
    ISpeechObjectTokens, DISPID_SAVolume, SpInProcRecoContext,
    DISPID_SPRsItem, DISPID_SOTGetStorageFileName,
    __MIDL___MIDL_itf_sapi_0000_0020_0001, SDTAudio, DISPID_SRRAudio,
    ISpStreamFormat, SSSPTRelativeToEnd, DISPID_SDKDeleteValue,
    DISPID_SRSCurrentStreamPosition, eLEXTYPE_PRIVATE14,
    DISPID_SRGDictationLoad, eLEXTYPE_PRIVATE5, SSFMOpenReadWrite,
    DISPID_SPCLangId, DISPIDSPTSI_ActiveLength, eLEXTYPE_PRIVATE17,
    tagSPPROPERTYINFO, SVSFIsXML, DISPID_SGRSTRule,
    DISPID_SVSLastBookmarkId, SREPhraseStart, SPAUDIOBUFFERINFO,
    DISPID_SREmulateRecognition, DISPID_SRCERecognition, SPVPRI_OVER,
    SPINTERFERENCE_LATENCY_WARNING, SRAONone,
    ISpPhoneticAlphabetSelection, SDA_One_Trailing_Space,
    SDKLCurrentUser, ISpEventSource, SPAR_Medium,
    DISPID_SOTGetDescription, DISPID_SOTsItem, SECLowConfidence,
    DISPID_SRSCurrentStreamNumber, DISPID_SVGetProfiles, SPBO_AHEAD,
    SPSEMANTICERRORINFO, DISPID_SRCEFalseRecognition, SPSNoun,
    DISPID_SVSCurrentStreamNumber, DISPID_SVEventInterests,
    SAFT16kHz16BitStereo, SINoise, DISPID_SPERetainedStreamOffset,
    DISPID_SOTCDefault, DISPID_SLAddPronunciation, DISPID_SVRate,
    DISPID_SPIAudioSizeBytes, DISPID_SPELexicalForm,
    SAFTGSM610_11kHzMono, DISPID_SBSFormat, SPDKL_CurrentConfig,
    DISPID_SVIsUISupported, SpeechPropertyNormalConfidenceThreshold,
    DISPID_SRRAlternates, DISPID_SVGetAudioInputs, SP_VISEME_7,
    ISpeechVoice, SITooQuiet, SAFT8kHz16BitStereo,
    DISPID_SRCEEndStream, SPPHRASEPROPERTY, SPEI_MAX_TTS,
    SPPHRASEREPLACEMENT, SVEWordBoundary, DISPID_SPEPronunciation,
    SREInterference, ISpeechPhraseReplacements,
    SpeechRegistryUserRoot, ISpRecoResult, DISPID_SRGetRecognizers,
    DISPID_SRGState, SRERecognition, SDTProperty,
    DISPID_SVEVoiceChange, DISPID_SWFEBlockAlign,
    DISPID_SOTMatchesAttributes, SRCS_Enabled, ISpeechResourceLoader,
    SVP_7, DISPID_SRGDictationSetState, SECFIgnoreKanaType,
    DISPID_SVSInputWordLength, SREStreamEnd, SPWORDPRONUNCIATION,
    SECFIgnoreCase, DISPID_SWFEAvgBytesPerSec, SREStateChange,
    DISPID_SOTCategory, DISPID_SRGDictationUnload,
    SPWP_UNKNOWN_WORD_UNPRONOUNCEABLE, SECFNoSpecialChars,
    SPSMF_SRGS_SEMANTICINTERPRETATION_W3C, DISPID_SFSClose,
    DISPID_SPPName, SPCT_SUB_DICTATION, SLOStatic, DISPID_SVSVisemeId,
    DISPID_SGRsCommitAndSave, SAFTADPCM_22kHzMono, DISPID_SGRName,
    SpWaveFormatEx, SpeechGrammarTagDictation,
    DISPID_SVSInputSentenceLength, DISPID_SGRInitialState,
    SAFTCCITT_ALaw_44kHzStereo, SAFT32kHz8BitMono, SVP_13,
    SAFT32kHz16BitStereo, SPAS_RUN, DISPID_SRGCommit,
    SpCompressedLexicon, SP_VISEME_19, _LARGE_INTEGER,
    SAFT12kHz8BitMono, SITooLoud, IStream, SRSInactive,
    SSSPTRelativeToStart, DISPID_SRCAudioInInterferenceStatus,
    DISPID_SPIGrammarId, SVP_19, DISPID_SVGetVoices, typelib_path,
    SPRST_INACTIVE_WITH_PURGE, ISpShortcut, SVSFDefault,
    eLEXTYPE_PRIVATE18, DISPID_SOTCreateInstance, SVP_8,
    DISPID_SPRDisplayAttributes, SPPS_Verb, SPEI_SR_PRIVATE,
    SVSFVoiceMask, DISPID_SPILanguageId, SAFTTrueSpeech_8kHz1BitMono,
    ISpeechLexicon, ULONG_PTR, eLEXTYPE_RESERVED8,
    DISPID_SLRemovePronunciationByPhoneIds,
    ISpPhoneticAlphabetConverter, SINoSignal, SPWT_LEXICAL,
    DISPID_SAFGetWaveFormatEx, SPEI_SR_AUDIO_LEVEL,
    DISPID_SPEAudioSizeTime, SpMMAudioIn, ISpObjectWithToken,
    eLEXTYPE_PRIVATE16, STCRemoteServer, DISPID_SDKGetlongValue,
    SpVoice, SP_VISEME_10, DISPID_SPARecoResult,
    DISPID_SVESentenceBoundary, DISPID_SPEAudioSizeBytes,
    ISpeechPhraseElements, DISPID_SOTCEnumerateTokens, SVSFParseSapi,
    DISPID_SLPsCount, SPINTERFERENCE_TOOQUIET, SPPS_Modifier,
    SPEI_RESERVED5, SRTAutopause, ISpeechObjectTokenCategory,
    DISPID_SPEDisplayText, SASClosed, SpAudioFormat,
    SAFTGSM610_44kHzMono, DISPID_SPIStartTime,
    SAFTCCITT_ALaw_22kHzStereo, SVSFParseMask, DISPID_SPPChildren,
    SAFT24kHz8BitMono, DISPID_SRSSupportedLanguages,
    SPFM_CREATE_ALWAYS, DISPID_SRCCreateGrammar,
    DISPID_SRCEStartStream, DISPID_SPIReplacements,
    SpeechPropertyComplexResponseSpeed, SpFileStream,
    SpResourceManager, DISPID_SOTDataKey, DISPID_SCSBaseStream,
    DISPID_SRCERecognizerStateChange, SVP_12,
    SpeechDictationTopicSpelling, IEnumString,
    SpeechTokenIdUserLexicon, SDTPronunciation,
    DISPID_SPAStartElementInResult, DISPID_SRCCmdMaxAlternates,
    DISPID_SGRsCommit, SPBO_TIME_UNITS, DISPID_SGRsFindRule,
    DISPID_SOTCSetId, SPEI_RESERVED2, SPPS_Interjection,
    ISpeechPhraseAlternates, DISPID_SGRsItem, VARIANT_BOOL,
    SpUnCompressedLexicon, ISpLexicon, SLODynamic, SGPronounciation,
    DISPID_SRRGetXMLErrorInfo, SPPROPERTYINFO,
    SpeechGrammarTagUnlimitedDictation, SPEI_START_SR_STREAM,
    SREStreamStart, SAFT32kHz8BitStereo, DISPID_SPRuleName,
    DISPID_SPANumberOfElementsInResult, tagSPTEXTSELECTIONINFO,
    ISpPhrase, SP_VISEME_14, DISPID_SOTs_NewEnum, SPVOICESTATUS,
    _lcid, DISPID_SRCEEnginePrivate, ISpeechRecoGrammar,
    DISPID_SAFSetWaveFormatEx, SPWORDLIST,
    DISPID_SRGCmdLoadFromProprietaryGrammar, SP_VISEME_2,
    SAFTCCITT_ALaw_44kHzMono, SPEI_START_INPUT_STREAM, DISPID_SBSRead,
    SPFM_OPEN_READWRITE, DISPID_SRCSetAdaptationData,
    DISPID_SRRTStreamTime, SDKLDefaultLocation, SVF_None, SPAR_Low,
    SFTInput, SPSHT_NotOverriden, DISPID_SRCEAudioLevel,
    SPGS_DISABLED, ISpeechRecognizer, SPEI_ACTIVE_CATEGORY_CHANGED,
    SREAudioLevel, SAFT48kHz8BitMono, SpNotifyTranslator,
    DISPID_SPRFirstElement, SPRS_ACTIVE, SpMMAudioEnum,
    SpPhoneticAlphabetConverter,
    SPINTERFERENCE_LATENCY_TRUNCATE_BEGIN, SPSHORTCUTPAIR,
    ISpNotifySource, ISpeechCustomStream, SAFTDefault,
    SAFTADPCM_44kHzStereo, DISPID_SVEPhoneme, SPCS_DISABLED,
    SGSDisabled, SRTSMLTimeout, SPEVENTSOURCEINFO,
    DISPID_SPEActualConfidence, SpTextSelectionInformation,
    SAFT11kHz8BitMono, SAFTCCITT_uLaw_11kHzStereo,
    DISPID_SABIBufferSize, DISPMETHOD, wireHWND, SAFT22kHz8BitMono,
    SpObjectToken, SAFT22kHz8BitStereo, DISPID_SMSALineId,
    DISPID_SPISaveToMemory, ISpGrammarBuilder,
    DISPID_SRGCmdLoadFromResource, SECFDefault, SVP_0, SVEVoiceChange,
    DISPID_SRRTLength, SAFT16kHz8BitStereo, eWORDTYPE_ADDED,
    SP_VISEME_12, ISpMMSysAudio, SpeechPropertyResourceUsage,
    DISPID_SVDisplayUI, DISPID_SLPPartOfSpeech, SPEI_MIN_TTS,
    SGSExclusive, SPRST_ACTIVE, SPEI_PHONEME, SSTTTextBuffer,
    ISpRecoContext2, DISPID_SPPParent, eLEXTYPE_RESERVED6,
    DISPID_SASFreeBufferSpace, SPPHRASE, DISPID_SGRAddState,
    SPEI_PROPERTY_NUM_CHANGE, SGLexicalNoSpecialChars, SPEI_SOUND_END,
    SRESoundStart, SPINTERFERENCE_NONE, SAFTCCITT_uLaw_44kHzStereo,
    DISPID_SVPause, SVSFIsFilename, SPSHT_OTHER,
    DISPID_SPEAudioTimeOffset, DISPID_SPRsCount, SAFT16kHz16BitMono,
    SpeechVoiceSkipTypeSentence, DISPID_SAFType,
    DISPID_SPIGetDisplayAttributes,
    DISPID_SRCERecognitionForOtherContext,
    DISPID_SPERetainedSizeBytes, ISpNotifySink, ISpRecoGrammar,
    DISPID_SRCCreateResultFromMemory, DISPID_SDKEnumValues,
    SSFMCreateForWrite, ISpVoice, ISpeechTextSelectionInformation,
    SAFTExtendedAudioFormat, eLEXTYPE_PRIVATE10, SREHypothesis,
    DISPID_SWFEBitsPerSample, SRAORetainAudio, SP_VISEME_21,
    DISPID_SRAudioInput, SVSFPersistXML, SPBINARYGRAMMAR,
    DISPID_SAEventHandle, SAFTADPCM_8kHzStereo, SP_VISEME_20,
    SPINTERFERENCE_TOOSLOW, ISpeechObjectToken, SECNormalConfidence,
    SECFEmulateResult, SAFT11kHz16BitStereo, SAFT48kHz16BitMono,
    IUnknown, DISPID_SRCEPhraseStart, SP_VISEME_9, SP_VISEME_18,
    SPPS_RESERVED4, eLEXTYPE_LETTERTOSOUND, SpeechTokenKeyAttributes,
    SpMMAudioOut, SPSFunction, DISPID_SBSWrite,
    DISPID_SRCreateRecoContext, DISPID_SABIMinNotification, ISpAudio,
    DISPID_SGRSRule, SPSMF_UPS, SPVPRI_NORMAL, SpeechAudioProperties,
    DISPID_SPRuleParent, COMMETHOD, SDTRule, DISPID_SRRSaveToMemory,
    DISPID_SPACommit, DISPID_SRCESoundEnd, SpStream, DISPID_SRProfile,
    STSF_CommonAppData, DISPIDSPTSI_ActiveOffset, SRSActive,
    ISpSerializeState, DISPID_SRGRecoContext, SASRun, SSTTDictation,
    SPEI_SR_RETAINEDAUDIO, DISPID_SRCEPropertyStringChange,
    SpeechPropertyHighConfidenceThreshold, eLEXTYPE_PRIVATE7,
    SpInprocRecognizer, SAFT44kHz8BitStereo, SRSInactiveWithPurge,
    eLEXTYPE_VENDORLEXICON, DISPID_SPCIdToPhone, SPXRO_Alternates_SML,
    DISPID_SRCEAdaptation, SVSFParseAutodetect, SPSHORTCUTPAIRLIST,
    DISPID_SGRSTPropertyName, DISPID_SRRPhraseInfo,
    DISPID_SRSNumberOfActiveRules, DISPID_SRRSetTextFeedback,
    SVSFParseSsml, DISPID_SGRSAddWordTransition, ISpNotifyTranslator,
    DISPID_SVSyncronousSpeakTimeout, ISpeechLexiconPronunciations,
    SP_VISEME_6, DISPID_SRGCmdSetRuleIdState,
    DISPID_SRRTOffsetFromStart, DISPID_SPIAudioStreamPosition,
    SGSEnabled, DISPID_SAStatus, SGRSTTTextBuffer, SLTUser,
    ISpeechRecognizerStatus, SP_VISEME_4, DISPID_SPRules_NewEnum,
    ISpRecoGrammar2, SVP_16, DISPID_SDKSetBinaryValue,
    DISPID_SLGetGenerationChange, SpeechCategoryRecoProfiles,
    DISPID_SGRSTNextState, DISPID_SRDisplayUI, DISPID_SPPsItem,
    eLEXTYPE_RESERVED9, SPAO_RETAIN_AUDIO, SDA_No_Trailing_Space,
    SPLO_STATIC, SPVPRI_ALERT, DISPID_SGRId, DISPID_SVSLastBookmark,
    SLTApp, SAFTCCITT_uLaw_22kHzMono, STSF_AppData, DISPID_SPPId,
    DISPID_SDKDeleteKey, DISPID_SLPType, SPPS_SuppressWord,
    ISpeechLexiconWord, DISPID_SRRSpeakAudio, SPEI_UNDEFINED,
    DISPID_SVSpeak, SDTLexicalForm, SpeechCategoryVoices,
    eLEXTYPE_PRIVATE2, CoClass, DISPID_SVSRunningState, SRTEmulated,
    DISPID_SRCRecognizer, eLEXTYPE_PRIVATE6, DISPID_SRRAudioFormat,
    SPLO_DYNAMIC, SRSActiveAlways, Library, DISPID_SPPsCount,
    DISPID_SVAllowAudioOuputFormatChangesOnNextSet, helpstring,
    DISPID_SPRuleId, ISpeechPhraseAlternate, eLEXTYPE_PRIVATE20,
    SPEI_PHRASE_START, SPWF_INPUT, eLEXTYPE_USER, SVPNormal,
    DISPID_SDKSetStringValue, DISPID_SVSInputWordPosition,
    SPEI_MAX_SR, IServiceProvider, SAFTCCITT_uLaw_22kHzStereo,
    DISPID_SADefaultFormat, DISPID_SPPEngineConfidence,
    SPSMF_SRGS_SAPIPROPERTIES, SPSUnknown, SAFTADPCM_11kHzMono,
    SPEI_RECO_STATE_CHANGE, DISPID_SFSOpen,
    DISPID_SPIRetainedSizeBytes, DISPID_SGRSTType,
    DISPID_SRCRetainedAudio, SPRECORESULTTIMES, SPSSuppressWord,
    DISPID_SDKSetLongValue, ISpeechWaveFormatEx, SGDisplay,
    SDKLLocalMachine, SpCustomStream, DISPID_SRCRetainedAudioFormat,
    SPCT_SLEEP, DISPID_SOTCId, SASStop, SPRST_NUM_STATES,
    SpeechTokenKeyFiles, SAFT24kHz8BitStereo, DISPID_SVEAudioLevel,
    __MIDL_IWinTypes_0009, ISpeechPhraseElement,
    SAFTNonStandardFormat, SDTAlternates, WAVEFORMATEX,
    SAFTADPCM_11kHzStereo, SVP_20, SPAS_STOP,
    SPEI_PROPERTY_STRING_CHANGE, SPDKL_DefaultLocation,
    DISPID_SPEsCount, DISPID_SASCurrentSeekPosition,
    SECHighConfidence, SAFTNoAssignedFormat, SPFM_OPEN_READONLY,
    DISPID_SPRs_NewEnum, DISPID_SRGSetWordSequenceData,
    SVESentenceBoundary, DISPID_SLWPronunciations,
    ISpeechLexiconPronunciation, ISpeechGrammarRule, SVP_15,
    SAFT24kHz16BitMono, SpeechRegistryLocalMachineRoot,
    DISPID_SLPPhoneIds, DISPID_SGRSTText, SPPS_RESERVED3,
    DISPID_SASState, DISPID_SABIEventBias, DISPID_SVAudioOutput,
    SVEPhoneme, DISPID_SOTSetId, SREBookmark, SAFT48kHz8BitStereo,
    SVEBookmark, DISPID_SPIElements, SPRS_INACTIVE, SGRSTTDictation,
    SPFM_NUM_MODES, SRTExtendableParse, DISPID_SPIEnginePrivateData,
    DISPID_SPEAudioStreamOffset, DISPID_SPRNumberOfElements,
    SpeechAllElements, DISPID_SRRGetXMLResult, SpeechTokenKeyUI,
    DISPID_SRSetPropertyNumber, DISPID_SGRsDynamic, SPCT_COMMAND,
    DISPID_SPRuleChildren, SPEI_WORD_BOUNDARY, GUID,
    SAFT48kHz16BitStereo, SPEI_RESERVED1, SRAExport,
    DISPID_SGRSTPropertyValue, SP_VISEME_17, Speech_Max_Pron_Length,
    SWTDeleted, ISpeechPhraseReplacement, ISpStreamFormatConverter,
    DISPID_SLPs_NewEnum, SSFMOpenForRead, ISpProperties, SPSModifier,
    SGDSActiveUserDelimited, eLEXTYPE_PRIVATE1, SAFT22kHz16BitStereo,
    DISPID_SPERequiredConfidence, SAFT11kHz8BitStereo, SP_VISEME_5,
    eLEXTYPE_PRIVATE13, SPPS_LMA, SAFTADPCM_8kHzMono, ISpDataKey,
    SVP_21, SPSHT_Unknown, SINone, DISPID_SGRSTPropertyId,
    _ISpeechVoiceEvents, _RemotableHandle,
    DISPID_SPRuleEngineConfidence, ISpeechPhraseInfoBuilder,
    SpNullPhoneConverter, SP_VISEME_13, SAFT8kHz8BitMono, SPBO_PAUSE,
    SVP_14, DISPID_SLGetPronunciations, SPEI_SR_BOOKMARK,
    SPSMF_SRGS_SEMANTICINTERPRETATION_MS,
    SWPUnknownWordUnpronounceable, SPWT_LEXICAL_NO_SPECIAL_CHARS,
    SPEI_RESERVED6, DISPID_SABufferNotifySize, DISPID_SMSAMMHandle,
    IInternetSecurityManager, SVEEndInputStream, SPPS_NotOverriden,
    SPWP_KNOWN_WORD_PRONOUNCEABLE, DISPID_SOTRemoveStorageFileName,
    DISPID_SOTRemove, SPINTERFERENCE_TOOLOUD, DISPID_SRGetFormat,
    eLEXTYPE_PRIVATE19, SRATopLevel, DISPID_SRCBookmark,
    DISPID_SVEEnginePrivate, SAFT16kHz8BitMono,
    DISPID_SVAudioOutputStream, SVEAudioLevel, DISPID_SLWType,
    SPAR_High, ISpXMLRecoResult, DISPID_SPAsCount, SAFT22kHz16BitMono,
    DISPID_SGRsCount, DISPID_SPPBRestorePhraseFromMemory, SGLexical,
    ISpeechPhraseRules, ISpeechBaseStream,
    ISpeechGrammarRuleStateTransitions, DISPID_SASetState,
    DISPID_SRCESoundStart, dispid, ISpeechAudio, SRADefaultToActive,
    SPEI_RECOGNITION, SP_VISEME_0, eLEXTYPE_USER_SHORTCUT, SRSEDone,
    SGRSTTEpsilon, SPWT_PRONUNCIATION, DISPID_SVGetAudioOutputs,
    DISPID_SLPLangId, SPEI_RECO_OTHER_CONTEXT, DISPID_SGRSTWeight,
    SPEI_VOICE_CHANGE, SPAUDIOSTATUS, DISPID_SRCResume,
    SpeechPropertyAdaptationOn, SpeechTokenValueCLSID,
    SPSNotOverriden, DISPID_SPAPhraseInfo, DISPID_SLWs_NewEnum,
    SPCT_DICTATION, SAFT8kHz8BitStereo,
    DISPID_SVSInputSentencePosition, eLEXTYPE_PRIVATE9,
    SpeechPropertyResponseSpeed, DISPID_SOTGetAttribute,
    SAFTCCITT_uLaw_44kHzMono, DISPID_SVVoice, UINT_PTR,
    SDA_Two_Trailing_Spaces, DISPID_SVEStreamEnd, SVP_3,
    DISPID_SGRSTsItem, SVSFNLPSpeakPunc, DISPID_SDKGetBinaryValue,
    SRERecoOtherContext, SVP_2, DISPID_SVAlertBoundary, SPEI_MIN_SR,
    SAFT24kHz16BitStereo, eLEXTYPE_RESERVED4, SVP_9,
    DISPID_SMSSetData, SPEI_REQUEST_UI, SpShortcut,
    ISpeechAudioStatus, STCInprocServer, Speech_StreamPos_RealTime
)


class SPVPRIORITY(IntFlag):
    SPVPRI_NORMAL = 0
    SPVPRI_ALERT = 1
    SPVPRI_OVER = 2


class SPCATEGORYTYPE(IntFlag):
    SPCT_COMMAND = 0
    SPCT_DICTATION = 1
    SPCT_SLEEP = 2
    SPCT_SUB_COMMAND = 3
    SPCT_SUB_DICTATION = 4


class SPXMLRESULTOPTIONS(IntFlag):
    SPXRO_SML = 0
    SPXRO_Alternates_SML = 1


class SpeechVoiceSpeakFlags(IntFlag):
    SVSFDefault = 0
    SVSFlagsAsync = 1
    SVSFPurgeBeforeSpeak = 2
    SVSFIsFilename = 4
    SVSFIsXML = 8
    SVSFIsNotXML = 16
    SVSFPersistXML = 32
    SVSFNLPSpeakPunc = 64
    SVSFParseSapi = 128
    SVSFParseSsml = 256
    SVSFParseAutodetect = 0
    SVSFNLPMask = 64
    SVSFParseMask = 384
    SVSFVoiceMask = 511
    SVSFUnusedFlags = -512


class SpeechDiscardType(IntFlag):
    SDTProperty = 1
    SDTReplacement = 2
    SDTRule = 4
    SDTDisplayText = 8
    SDTLexicalForm = 16
    SDTPronunciation = 32
    SDTAudio = 64
    SDTAlternates = 128
    SDTAll = 255


class DISPID_SpeechObjectToken(IntFlag):
    DISPID_SOTId = 1
    DISPID_SOTDataKey = 2
    DISPID_SOTCategory = 3
    DISPID_SOTGetDescription = 4
    DISPID_SOTSetId = 5
    DISPID_SOTGetAttribute = 6
    DISPID_SOTCreateInstance = 7
    DISPID_SOTRemove = 8
    DISPID_SOTGetStorageFileName = 9
    DISPID_SOTRemoveStorageFileName = 10
    DISPID_SOTIsUISupported = 11
    DISPID_SOTDisplayUI = 12
    DISPID_SOTMatchesAttributes = 13


class SpeechRecognitionType(IntFlag):
    SRTStandard = 0
    SRTAutopause = 1
    SRTEmulated = 2
    SRTSMLTimeout = 4
    SRTExtendableParse = 8
    SRTReSent = 16


class SPEVENTENUM(IntFlag):
    SPEI_UNDEFINED = 0
    SPEI_START_INPUT_STREAM = 1
    SPEI_END_INPUT_STREAM = 2
    SPEI_VOICE_CHANGE = 3
    SPEI_TTS_BOOKMARK = 4
    SPEI_WORD_BOUNDARY = 5
    SPEI_PHONEME = 6
    SPEI_SENTENCE_BOUNDARY = 7
    SPEI_VISEME = 8
    SPEI_TTS_AUDIO_LEVEL = 9
    SPEI_TTS_PRIVATE = 15
    SPEI_MIN_TTS = 1
    SPEI_MAX_TTS = 15
    SPEI_END_SR_STREAM = 34
    SPEI_SOUND_START = 35
    SPEI_SOUND_END = 36
    SPEI_PHRASE_START = 37
    SPEI_RECOGNITION = 38
    SPEI_HYPOTHESIS = 39
    SPEI_SR_BOOKMARK = 40
    SPEI_PROPERTY_NUM_CHANGE = 41
    SPEI_PROPERTY_STRING_CHANGE = 42
    SPEI_FALSE_RECOGNITION = 43
    SPEI_INTERFERENCE = 44
    SPEI_REQUEST_UI = 45
    SPEI_RECO_STATE_CHANGE = 46
    SPEI_ADAPTATION = 47
    SPEI_START_SR_STREAM = 48
    SPEI_RECO_OTHER_CONTEXT = 49
    SPEI_SR_AUDIO_LEVEL = 50
    SPEI_SR_RETAINEDAUDIO = 51
    SPEI_SR_PRIVATE = 52
    SPEI_ACTIVE_CATEGORY_CHANGED = 53
    SPEI_RESERVED5 = 54
    SPEI_RESERVED6 = 55
    SPEI_MIN_SR = 34
    SPEI_MAX_SR = 55
    SPEI_RESERVED1 = 30
    SPEI_RESERVED2 = 33
    SPEI_RESERVED3 = 63


class SPLOADOPTIONS(IntFlag):
    SPLO_STATIC = 0
    SPLO_DYNAMIC = 1


class SpeechBookmarkOptions(IntFlag):
    SBONone = 0
    SBOPause = 1


class DISPID_SpeechDataKey(IntFlag):
    DISPID_SDKSetBinaryValue = 1
    DISPID_SDKGetBinaryValue = 2
    DISPID_SDKSetStringValue = 3
    DISPID_SDKGetStringValue = 4
    DISPID_SDKSetLongValue = 5
    DISPID_SDKGetlongValue = 6
    DISPID_SDKOpenKey = 7
    DISPID_SDKCreateKey = 8
    DISPID_SDKDeleteKey = 9
    DISPID_SDKDeleteValue = 10
    DISPID_SDKEnumKeys = 11
    DISPID_SDKEnumValues = 12


class SpeechRecognizerState(IntFlag):
    SRSInactive = 0
    SRSActive = 1
    SRSActiveAlways = 2
    SRSInactiveWithPurge = 3


class SpeechLexiconType(IntFlag):
    SLTUser = 1
    SLTApp = 2


class SpeechPartOfSpeech(IntFlag):
    SPSNotOverriden = -1
    SPSUnknown = 0
    SPSNoun = 4096
    SPSVerb = 8192
    SPSModifier = 12288
    SPSFunction = 16384
    SPSInterjection = 20480
    SPSLMA = 28672
    SPSSuppressWord = 61440


class DISPID_SpeechLexiconPronunciation(IntFlag):
    DISPID_SLPType = 1
    DISPID_SLPLangId = 2
    DISPID_SLPPartOfSpeech = 3
    DISPID_SLPPhoneIds = 4
    DISPID_SLPSymbolic = 5


class SPPARTOFSPEECH(IntFlag):
    SPPS_NotOverriden = -1
    SPPS_Unknown = 0
    SPPS_Noun = 4096
    SPPS_Verb = 8192
    SPPS_Modifier = 12288
    SPPS_Function = 16384
    SPPS_Interjection = 20480
    SPPS_Noncontent = 24576
    SPPS_LMA = 28672
    SPPS_SuppressWord = 61440


class SPDATAKEYLOCATION(IntFlag):
    SPDKL_DefaultLocation = 0
    SPDKL_CurrentUser = 1
    SPDKL_LocalMachine = 2
    SPDKL_CurrentConfig = 5


class SpeechStreamSeekPositionType(IntFlag):
    SSSPTRelativeToStart = 0
    SSSPTRelativeToCurrentPosition = 1
    SSSPTRelativeToEnd = 2


class SpeechRecoEvents(IntFlag):
    SREStreamEnd = 1
    SRESoundStart = 2
    SRESoundEnd = 4
    SREPhraseStart = 8
    SRERecognition = 16
    SREHypothesis = 32
    SREBookmark = 64
    SREPropertyNumChange = 128
    SREPropertyStringChange = 256
    SREFalseRecognition = 512
    SREInterference = 1024
    SRERequestUI = 2048
    SREStateChange = 4096
    SREAdaptation = 8192
    SREStreamStart = 16384
    SRERecoOtherContext = 32768
    SREAudioLevel = 65536
    SREPrivate = 262144
    SREAllEvents = 393215


class DISPID_SpeechMemoryStream(IntFlag):
    DISPID_SMSSetData = 100
    DISPID_SMSGetData = 101


class SpeechAudioFormatType(IntFlag):
    SAFTDefault = -1
    SAFTNoAssignedFormat = 0
    SAFTText = 1
    SAFTNonStandardFormat = 2
    SAFTExtendedAudioFormat = 3
    SAFT8kHz8BitMono = 4
    SAFT8kHz8BitStereo = 5
    SAFT8kHz16BitMono = 6
    SAFT8kHz16BitStereo = 7
    SAFT11kHz8BitMono = 8
    SAFT11kHz8BitStereo = 9
    SAFT11kHz16BitMono = 10
    SAFT11kHz16BitStereo = 11
    SAFT12kHz8BitMono = 12
    SAFT12kHz8BitStereo = 13
    SAFT12kHz16BitMono = 14
    SAFT12kHz16BitStereo = 15
    SAFT16kHz8BitMono = 16
    SAFT16kHz8BitStereo = 17
    SAFT16kHz16BitMono = 18
    SAFT16kHz16BitStereo = 19
    SAFT22kHz8BitMono = 20
    SAFT22kHz8BitStereo = 21
    SAFT22kHz16BitMono = 22
    SAFT22kHz16BitStereo = 23
    SAFT24kHz8BitMono = 24
    SAFT24kHz8BitStereo = 25
    SAFT24kHz16BitMono = 26
    SAFT24kHz16BitStereo = 27
    SAFT32kHz8BitMono = 28
    SAFT32kHz8BitStereo = 29
    SAFT32kHz16BitMono = 30
    SAFT32kHz16BitStereo = 31
    SAFT44kHz8BitMono = 32
    SAFT44kHz8BitStereo = 33
    SAFT44kHz16BitMono = 34
    SAFT44kHz16BitStereo = 35
    SAFT48kHz8BitMono = 36
    SAFT48kHz8BitStereo = 37
    SAFT48kHz16BitMono = 38
    SAFT48kHz16BitStereo = 39
    SAFTTrueSpeech_8kHz1BitMono = 40
    SAFTCCITT_ALaw_8kHzMono = 41
    SAFTCCITT_ALaw_8kHzStereo = 42
    SAFTCCITT_ALaw_11kHzMono = 43
    SAFTCCITT_ALaw_11kHzStereo = 44
    SAFTCCITT_ALaw_22kHzMono = 45
    SAFTCCITT_ALaw_22kHzStereo = 46
    SAFTCCITT_ALaw_44kHzMono = 47
    SAFTCCITT_ALaw_44kHzStereo = 48
    SAFTCCITT_uLaw_8kHzMono = 49
    SAFTCCITT_uLaw_8kHzStereo = 50
    SAFTCCITT_uLaw_11kHzMono = 51
    SAFTCCITT_uLaw_11kHzStereo = 52
    SAFTCCITT_uLaw_22kHzMono = 53
    SAFTCCITT_uLaw_22kHzStereo = 54
    SAFTCCITT_uLaw_44kHzMono = 55
    SAFTCCITT_uLaw_44kHzStereo = 56
    SAFTADPCM_8kHzMono = 57
    SAFTADPCM_8kHzStereo = 58
    SAFTADPCM_11kHzMono = 59
    SAFTADPCM_11kHzStereo = 60
    SAFTADPCM_22kHzMono = 61
    SAFTADPCM_22kHzStereo = 62
    SAFTADPCM_44kHzMono = 63
    SAFTADPCM_44kHzStereo = 64
    SAFTGSM610_8kHzMono = 65
    SAFTGSM610_11kHzMono = 66
    SAFTGSM610_22kHzMono = 67
    SAFTGSM610_44kHzMono = 68


class SpeechDataKeyLocation(IntFlag):
    SDKLDefaultLocation = 0
    SDKLCurrentUser = 1
    SDKLLocalMachine = 2
    SDKLCurrentConfig = 5


class DISPID_SpeechPhraseProperties(IntFlag):
    DISPID_SPPsCount = 1
    DISPID_SPPsItem = 0
    DISPID_SPPs_NewEnum = -4


class DISPID_SpeechAudioStatus(IntFlag):
    DISPID_SASFreeBufferSpace = 1
    DISPID_SASNonBlockingIO = 2
    DISPID_SASState = 3
    DISPID_SASCurrentSeekPosition = 4
    DISPID_SASCurrentDevicePosition = 5


class DISPID_SpeechPhraseRule(IntFlag):
    DISPID_SPRuleName = 1
    DISPID_SPRuleId = 2
    DISPID_SPRuleFirstElement = 3
    DISPID_SPRuleNumberOfElements = 4
    DISPID_SPRuleParent = 5
    DISPID_SPRuleChildren = 6
    DISPID_SPRuleConfidence = 7
    DISPID_SPRuleEngineConfidence = 8


class DISPID_SpeechAudioBufferInfo(IntFlag):
    DISPID_SABIMinNotification = 1
    DISPID_SABIBufferSize = 2
    DISPID_SABIEventBias = 3


class DISPID_SpeechRecoResult2(IntFlag):
    DISPID_SRRSetTextFeedback = 12


class SpeechEngineConfidence(IntFlag):
    SECLowConfidence = -1
    SECNormalConfidence = 0
    SECHighConfidence = 1


class DISPID_SpeechWaveFormatEx(IntFlag):
    DISPID_SWFEFormatTag = 1
    DISPID_SWFEChannels = 2
    DISPID_SWFESamplesPerSec = 3
    DISPID_SWFEAvgBytesPerSec = 4
    DISPID_SWFEBlockAlign = 5
    DISPID_SWFEBitsPerSample = 6
    DISPID_SWFEExtraData = 7


class DISPID_SpeechPhraseRules(IntFlag):
    DISPID_SPRulesCount = 1
    DISPID_SPRulesItem = 0
    DISPID_SPRules_NewEnum = -4


class DISPID_SpeechLexicon(IntFlag):
    DISPID_SLGenerationId = 1
    DISPID_SLGetWords = 2
    DISPID_SLAddPronunciation = 3
    DISPID_SLAddPronunciationByPhoneIds = 4
    DISPID_SLRemovePronunciation = 5
    DISPID_SLRemovePronunciationByPhoneIds = 6
    DISPID_SLGetPronunciations = 7
    DISPID_SLGetGenerationChange = 8


class DISPID_SpeechVoice(IntFlag):
    DISPID_SVStatus = 1
    DISPID_SVVoice = 2
    DISPID_SVAudioOutput = 3
    DISPID_SVAudioOutputStream = 4
    DISPID_SVRate = 5
    DISPID_SVVolume = 6
    DISPID_SVAllowAudioOuputFormatChangesOnNextSet = 7
    DISPID_SVEventInterests = 8
    DISPID_SVPriority = 9
    DISPID_SVAlertBoundary = 10
    DISPID_SVSyncronousSpeakTimeout = 11
    DISPID_SVSpeak = 12
    DISPID_SVSpeakStream = 13
    DISPID_SVPause = 14
    DISPID_SVResume = 15
    DISPID_SVSkip = 16
    DISPID_SVGetVoices = 17
    DISPID_SVGetAudioOutputs = 18
    DISPID_SVWaitUntilDone = 19
    DISPID_SVSpeakCompleteEvent = 20
    DISPID_SVIsUISupported = 21
    DISPID_SVDisplayUI = 22


class SpeechRecoContextState(IntFlag):
    SRCS_Disabled = 0
    SRCS_Enabled = 1


class _SPAUDIOSTATE(IntFlag):
    SPAS_CLOSED = 0
    SPAS_STOP = 1
    SPAS_PAUSE = 2
    SPAS_RUN = 3


class SPRECOSTATE(IntFlag):
    SPRST_INACTIVE = 0
    SPRST_ACTIVE = 1
    SPRST_ACTIVE_ALWAYS = 2
    SPRST_INACTIVE_WITH_PURGE = 3
    SPRST_NUM_STATES = 4


class SpeechTokenContext(IntFlag):
    STCInprocServer = 1
    STCInprocHandler = 2
    STCLocalServer = 4
    STCRemoteServer = 16
    STCAll = 23


class DISPID_SpeechLexiconWords(IntFlag):
    DISPID_SLWsCount = 1
    DISPID_SLWsItem = 0
    DISPID_SLWs_NewEnum = -4


class SpeechRetainedAudioOptions(IntFlag):
    SRAONone = 0
    SRAORetainAudio = 1


class DISPID_SpeechLexiconWord(IntFlag):
    DISPID_SLWLangId = 1
    DISPID_SLWType = 2
    DISPID_SLWWord = 3
    DISPID_SLWPronunciations = 4


class SPWORDTYPE(IntFlag):
    eWORDTYPE_ADDED = 1
    eWORDTYPE_DELETED = 2


class DISPID_SpeechLexiconProns(IntFlag):
    DISPID_SLPsCount = 1
    DISPID_SLPsItem = 0
    DISPID_SLPs_NewEnum = -4


class SpeechGrammarState(IntFlag):
    SGSEnabled = 1
    SGSDisabled = 0
    SGSExclusive = 3


class SpeechLoadOption(IntFlag):
    SLOStatic = 0
    SLODynamic = 1


class SpeechRuleState(IntFlag):
    SGDSInactive = 0
    SGDSActive = 1
    SGDSActiveWithAutoPause = 3
    SGDSActiveUserDelimited = 4


class SpeechWordPronounceable(IntFlag):
    SWPUnknownWordUnpronounceable = 0
    SWPUnknownWordPronounceable = 1
    SWPKnownWordPronounceable = 2


class DISPID_SpeechPhraseBuilder(IntFlag):
    DISPID_SPPBRestorePhraseFromMemory = 1


class SpeechWordType(IntFlag):
    SWTAdded = 1
    SWTDeleted = 2


class DISPID_SpeechRecoResultTimes(IntFlag):
    DISPID_SRRTStreamTime = 1
    DISPID_SRRTLength = 2
    DISPID_SRRTTickCount = 3
    DISPID_SRRTOffsetFromStart = 4


class DISPID_SpeechRecoContextEvents(IntFlag):
    DISPID_SRCEStartStream = 1
    DISPID_SRCEEndStream = 2
    DISPID_SRCEBookmark = 3
    DISPID_SRCESoundStart = 4
    DISPID_SRCESoundEnd = 5
    DISPID_SRCEPhraseStart = 6
    DISPID_SRCERecognition = 7
    DISPID_SRCEHypothesis = 8
    DISPID_SRCEPropertyNumberChange = 9
    DISPID_SRCEPropertyStringChange = 10
    DISPID_SRCEFalseRecognition = 11
    DISPID_SRCEInterference = 12
    DISPID_SRCERequestUI = 13
    DISPID_SRCERecognizerStateChange = 14
    DISPID_SRCEAdaptation = 15
    DISPID_SRCERecognitionForOtherContext = 16
    DISPID_SRCEAudioLevel = 17
    DISPID_SRCEEnginePrivate = 18


class DISPID_SpeechPhraseAlternate(IntFlag):
    DISPID_SPARecoResult = 1
    DISPID_SPAStartElementInResult = 2
    DISPID_SPANumberOfElementsInResult = 3
    DISPID_SPAPhraseInfo = 4
    DISPID_SPACommit = 5


class DISPID_SpeechPhraseAlternates(IntFlag):
    DISPID_SPAsCount = 1
    DISPID_SPAsItem = 0
    DISPID_SPAs_NewEnum = -4


class SPAUDIOOPTIONS(IntFlag):
    SPAO_NONE = 0
    SPAO_RETAIN_AUDIO = 1


class SPBOOKMARKOPTIONS(IntFlag):
    SPBO_NONE = 0
    SPBO_PAUSE = 1
    SPBO_AHEAD = 2
    SPBO_TIME_UNITS = 4


class SPCONTEXTSTATE(IntFlag):
    SPCS_DISABLED = 0
    SPCS_ENABLED = 1


class DISPID_SpeechPhraseInfo(IntFlag):
    DISPID_SPILanguageId = 1
    DISPID_SPIGrammarId = 2
    DISPID_SPIStartTime = 3
    DISPID_SPIAudioStreamPosition = 4
    DISPID_SPIAudioSizeBytes = 5
    DISPID_SPIRetainedSizeBytes = 6
    DISPID_SPIAudioSizeTime = 7
    DISPID_SPIRule = 8
    DISPID_SPIProperties = 9
    DISPID_SPIElements = 10
    DISPID_SPIReplacements = 11
    DISPID_SPIEngineId = 12
    DISPID_SPIEnginePrivateData = 13
    DISPID_SPISaveToMemory = 14
    DISPID_SPIGetText = 15
    DISPID_SPIGetDisplayAttributes = 16


class DISPID_SpeechGrammarRule(IntFlag):
    DISPID_SGRAttributes = 1
    DISPID_SGRInitialState = 2
    DISPID_SGRName = 3
    DISPID_SGRId = 4
    DISPID_SGRClear = 5
    DISPID_SGRAddResource = 6
    DISPID_SGRAddState = 7


class SPWAVEFORMATTYPE(IntFlag):
    SPWF_INPUT = 0
    SPWF_SRENGINE = 1


class SpeechTokenShellFolder(IntFlag):
    STSF_AppData = 26
    STSF_LocalAppData = 28
    STSF_CommonAppData = 35
    STSF_FlagCreate = 32768


class SpeechDisplayAttributes(IntFlag):
    SDA_No_Trailing_Space = 0
    SDA_One_Trailing_Space = 2
    SDA_Two_Trailing_Spaces = 4
    SDA_Consume_Leading_Spaces = 8


class SPWORDPRONOUNCEABLE(IntFlag):
    SPWP_UNKNOWN_WORD_UNPRONOUNCEABLE = 0
    SPWP_UNKNOWN_WORD_PRONOUNCEABLE = 1
    SPWP_KNOWN_WORD_PRONOUNCEABLE = 2


class DISPID_SpeechPhoneConverter(IntFlag):
    DISPID_SPCLangId = 1
    DISPID_SPCPhoneToId = 2
    DISPID_SPCIdToPhone = 3


class SpeechVoicePriority(IntFlag):
    SVPNormal = 0
    SVPAlert = 1
    SVPOver = 2


class SPADAPTATIONRELEVANCE(IntFlag):
    SPAR_Unknown = 0
    SPAR_Low = 1
    SPAR_Medium = 2
    SPAR_High = 3


class SPGRAMMARSTATE(IntFlag):
    SPGS_DISABLED = 0
    SPGS_ENABLED = 1
    SPGS_EXCLUSIVE = 3


class SpeechAudioState(IntFlag):
    SASClosed = 0
    SASStop = 1
    SASPause = 2
    SASRun = 3


class SPINTERFERENCE(IntFlag):
    SPINTERFERENCE_NONE = 0
    SPINTERFERENCE_NOISE = 1
    SPINTERFERENCE_NOSIGNAL = 2
    SPINTERFERENCE_TOOLOUD = 3
    SPINTERFERENCE_TOOQUIET = 4
    SPINTERFERENCE_TOOFAST = 5
    SPINTERFERENCE_TOOSLOW = 6
    SPINTERFERENCE_LATENCY_WARNING = 7
    SPINTERFERENCE_LATENCY_TRUNCATE_BEGIN = 8
    SPINTERFERENCE_LATENCY_TRUNCATE_END = 9


class DISPID_SpeechGrammarRules(IntFlag):
    DISPID_SGRsCount = 1
    DISPID_SGRsDynamic = 2
    DISPID_SGRsAdd = 3
    DISPID_SGRsCommit = 4
    DISPID_SGRsCommitAndSave = 5
    DISPID_SGRsFindRule = 6
    DISPID_SGRsItem = 0
    DISPID_SGRs_NewEnum = -4


class DISPID_SpeechVoiceStatus(IntFlag):
    DISPID_SVSCurrentStreamNumber = 1
    DISPID_SVSLastStreamNumberQueued = 2
    DISPID_SVSLastResult = 3
    DISPID_SVSRunningState = 4
    DISPID_SVSInputWordPosition = 5
    DISPID_SVSInputWordLength = 6
    DISPID_SVSInputSentencePosition = 7
    DISPID_SVSInputSentenceLength = 8
    DISPID_SVSLastBookmark = 9
    DISPID_SVSLastBookmarkId = 10
    DISPID_SVSPhonemeId = 11
    DISPID_SVSVisemeId = 12


class DISPID_SpeechGrammarRuleState(IntFlag):
    DISPID_SGRSRule = 1
    DISPID_SGRSTransitions = 2
    DISPID_SGRSAddWordTransition = 3
    DISPID_SGRSAddRuleTransition = 4
    DISPID_SGRSAddSpecialTransition = 5


class DISPID_SpeechVoiceEvent(IntFlag):
    DISPID_SVEStreamStart = 1
    DISPID_SVEStreamEnd = 2
    DISPID_SVEVoiceChange = 3
    DISPID_SVEBookmark = 4
    DISPID_SVEWord = 5
    DISPID_SVEPhoneme = 6
    DISPID_SVESentenceBoundary = 7
    DISPID_SVEViseme = 8
    DISPID_SVEAudioLevel = 9
    DISPID_SVEEnginePrivate = 10


class DISPID_SpeechGrammarRuleStateTransitions(IntFlag):
    DISPID_SGRSTsCount = 1
    DISPID_SGRSTsItem = 0
    DISPID_SGRSTs_NewEnum = -4


class SPGRAMMARWORDTYPE(IntFlag):
    SPWT_DISPLAY = 0
    SPWT_LEXICAL = 1
    SPWT_PRONUNCIATION = 2
    SPWT_LEXICAL_NO_SPECIAL_CHARS = 3


class SPRULESTATE(IntFlag):
    SPRS_INACTIVE = 0
    SPRS_ACTIVE = 1
    SPRS_ACTIVE_WITH_AUTO_PAUSE = 3
    SPRS_ACTIVE_USER_DELIMITED = 4


class SPSEMANTICFORMAT(IntFlag):
    SPSMF_SAPI_PROPERTIES = 0
    SPSMF_SRGS_SEMANTICINTERPRETATION_MS = 1
    SPSMF_SRGS_SAPIPROPERTIES = 2
    SPSMF_UPS = 4
    SPSMF_SRGS_SEMANTICINTERPRETATION_W3C = 8


class DISPIDSPTSI(IntFlag):
    DISPIDSPTSI_ActiveOffset = 1
    DISPIDSPTSI_ActiveLength = 2
    DISPIDSPTSI_SelectionOffset = 3
    DISPIDSPTSI_SelectionLength = 4


class DISPID_SpeechRecognizer(IntFlag):
    DISPID_SRRecognizer = 1
    DISPID_SRAllowAudioInputFormatChangesOnNextSet = 2
    DISPID_SRAudioInput = 3
    DISPID_SRAudioInputStream = 4
    DISPID_SRIsShared = 5
    DISPID_SRState = 6
    DISPID_SRStatus = 7
    DISPID_SRProfile = 8
    DISPID_SREmulateRecognition = 9
    DISPID_SRCreateRecoContext = 10
    DISPID_SRGetFormat = 11
    DISPID_SRSetPropertyNumber = 12
    DISPID_SRGetPropertyNumber = 13
    DISPID_SRSetPropertyString = 14
    DISPID_SRGetPropertyString = 15
    DISPID_SRIsUISupported = 16
    DISPID_SRDisplayUI = 17
    DISPID_SRGetRecognizers = 18
    DISPID_SVGetAudioInputs = 19
    DISPID_SVGetProfiles = 20


class DISPID_SpeechRecoResult(IntFlag):
    DISPID_SRRRecoContext = 1
    DISPID_SRRTimes = 2
    DISPID_SRRAudioFormat = 3
    DISPID_SRRPhraseInfo = 4
    DISPID_SRRAlternates = 5
    DISPID_SRRAudio = 6
    DISPID_SRRSpeakAudio = 7
    DISPID_SRRSaveToMemory = 8
    DISPID_SRRDiscardResultInfo = 9


class SpeechStreamFileMode(IntFlag):
    SSFMOpenForRead = 0
    SSFMOpenReadWrite = 1
    SSFMCreate = 2
    SSFMCreateForWrite = 3


class DISPID_SpeechXMLRecoResult(IntFlag):
    DISPID_SRRGetXMLResult = 10
    DISPID_SRRGetXMLErrorInfo = 11


class SpeechRunState(IntFlag):
    SRSEDone = 1
    SRSEIsSpeaking = 2


class SpeechGrammarWordType(IntFlag):
    SGDisplay = 0
    SGLexical = 1
    SGPronounciation = 2
    SGLexicalNoSpecialChars = 3


class SpeechSpecialTransitionType(IntFlag):
    SSTTWildcard = 1
    SSTTDictation = 2
    SSTTTextBuffer = 3


class SPVISEMES(IntFlag):
    SP_VISEME_0 = 0
    SP_VISEME_1 = 1
    SP_VISEME_2 = 2
    SP_VISEME_3 = 3
    SP_VISEME_4 = 4
    SP_VISEME_5 = 5
    SP_VISEME_6 = 6
    SP_VISEME_7 = 7
    SP_VISEME_8 = 8
    SP_VISEME_9 = 9
    SP_VISEME_10 = 10
    SP_VISEME_11 = 11
    SP_VISEME_12 = 12
    SP_VISEME_13 = 13
    SP_VISEME_14 = 14
    SP_VISEME_15 = 15
    SP_VISEME_16 = 16
    SP_VISEME_17 = 17
    SP_VISEME_18 = 18
    SP_VISEME_19 = 19
    SP_VISEME_20 = 20
    SP_VISEME_21 = 21


class SPSHORTCUTTYPE(IntFlag):
    SPSHT_NotOverriden = -1
    SPSHT_Unknown = 0
    SPSHT_EMAIL = 4096
    SPSHT_OTHER = 8192
    SPPS_RESERVED1 = 12288
    SPPS_RESERVED2 = 16384
    SPPS_RESERVED3 = 20480
    SPPS_RESERVED4 = 61440


class SpeechInterference(IntFlag):
    SINone = 0
    SINoise = 1
    SINoSignal = 2
    SITooLoud = 3
    SITooQuiet = 4
    SITooFast = 5
    SITooSlow = 6


class SpeechGrammarRuleStateTransitionType(IntFlag):
    SGRSTTEpsilon = 0
    SGRSTTWord = 1
    SGRSTTRule = 2
    SGRSTTDictation = 3
    SGRSTTWildcard = 4
    SGRSTTTextBuffer = 5


class SpeechVoiceEvents(IntFlag):
    SVEStartInputStream = 2
    SVEEndInputStream = 4
    SVEVoiceChange = 8
    SVEBookmark = 16
    SVEWordBoundary = 32
    SVEPhoneme = 64
    SVESentenceBoundary = 128
    SVEViseme = 256
    SVEAudioLevel = 512
    SVEPrivate = 32768
    SVEAllEvents = 33790


class SpeechFormatType(IntFlag):
    SFTInput = 0
    SFTSREngine = 1


class DISPID_SpeechObjectTokens(IntFlag):
    DISPID_SOTsCount = 1
    DISPID_SOTsItem = 0
    DISPID_SOTs_NewEnum = -4


class SPFILEMODE(IntFlag):
    SPFM_OPEN_READONLY = 0
    SPFM_OPEN_READWRITE = 1
    SPFM_CREATE = 2
    SPFM_CREATE_ALWAYS = 3
    SPFM_NUM_MODES = 4


class DISPID_SpeechPhraseElement(IntFlag):
    DISPID_SPEAudioTimeOffset = 1
    DISPID_SPEAudioSizeTime = 2
    DISPID_SPEAudioStreamOffset = 3
    DISPID_SPEAudioSizeBytes = 4
    DISPID_SPERetainedStreamOffset = 5
    DISPID_SPERetainedSizeBytes = 6
    DISPID_SPEDisplayText = 7
    DISPID_SPELexicalForm = 8
    DISPID_SPEPronunciation = 9
    DISPID_SPEDisplayAttributes = 10
    DISPID_SPERequiredConfidence = 11
    DISPID_SPEActualConfidence = 12
    DISPID_SPEEngineConfidence = 13


class DISPID_SpeechObjectTokenCategory(IntFlag):
    DISPID_SOTCId = 1
    DISPID_SOTCDefault = 2
    DISPID_SOTCSetId = 3
    DISPID_SOTCGetDataKey = 4
    DISPID_SOTCEnumerateTokens = 5


class SpeechVisemeType(IntFlag):
    SVP_0 = 0
    SVP_1 = 1
    SVP_2 = 2
    SVP_3 = 3
    SVP_4 = 4
    SVP_5 = 5
    SVP_6 = 6
    SVP_7 = 7
    SVP_8 = 8
    SVP_9 = 9
    SVP_10 = 10
    SVP_11 = 11
    SVP_12 = 12
    SVP_13 = 13
    SVP_14 = 14
    SVP_15 = 15
    SVP_16 = 16
    SVP_17 = 17
    SVP_18 = 18
    SVP_19 = 19
    SVP_20 = 20
    SVP_21 = 21


class SpeechVisemeFeature(IntFlag):
    SVF_None = 0
    SVF_Stressed = 1
    SVF_Emphasis = 2


class DISPID_SpeechAudioFormat(IntFlag):
    DISPID_SAFType = 1
    DISPID_SAFGuid = 2
    DISPID_SAFGetWaveFormatEx = 3
    DISPID_SAFSetWaveFormatEx = 4


class SpeechRuleAttributes(IntFlag):
    SRATopLevel = 1
    SRADefaultToActive = 2
    SRAExport = 4
    SRAImport = 8
    SRAInterpreter = 16
    SRADynamic = 32
    SRARoot = 64


class DISPID_SpeechBaseStream(IntFlag):
    DISPID_SBSFormat = 1
    DISPID_SBSRead = 2
    DISPID_SBSWrite = 3
    DISPID_SBSSeek = 4


class DISPID_SpeechPhraseElements(IntFlag):
    DISPID_SPEsCount = 1
    DISPID_SPEsItem = 0
    DISPID_SPEs_NewEnum = -4


class DISPID_SpeechAudio(IntFlag):
    DISPID_SAStatus = 200
    DISPID_SABufferInfo = 201
    DISPID_SADefaultFormat = 202
    DISPID_SAVolume = 203
    DISPID_SABufferNotifySize = 204
    DISPID_SAEventHandle = 205
    DISPID_SASetState = 206


class DISPID_SpeechPhraseReplacement(IntFlag):
    DISPID_SPRDisplayAttributes = 1
    DISPID_SPRText = 2
    DISPID_SPRFirstElement = 3
    DISPID_SPRNumberOfElements = 4


class DISPID_SpeechPhraseReplacements(IntFlag):
    DISPID_SPRsCount = 1
    DISPID_SPRsItem = 0
    DISPID_SPRs_NewEnum = -4


class DISPID_SpeechMMSysAudio(IntFlag):
    DISPID_SMSADeviceId = 300
    DISPID_SMSALineId = 301
    DISPID_SMSAMMHandle = 302


class DISPID_SpeechPhraseProperty(IntFlag):
    DISPID_SPPName = 1
    DISPID_SPPId = 2
    DISPID_SPPValue = 3
    DISPID_SPPFirstElement = 4
    DISPID_SPPNumberOfElements = 5
    DISPID_SPPEngineConfidence = 6
    DISPID_SPPConfidence = 7
    DISPID_SPPParent = 8
    DISPID_SPPChildren = 9


class DISPID_SpeechCustomStream(IntFlag):
    DISPID_SCSBaseStream = 100


class DISPID_SpeechFileStream(IntFlag):
    DISPID_SFSOpen = 100
    DISPID_SFSClose = 101


class DISPID_SpeechGrammarRuleStateTransition(IntFlag):
    DISPID_SGRSTType = 1
    DISPID_SGRSTText = 2
    DISPID_SGRSTRule = 3
    DISPID_SGRSTWeight = 4
    DISPID_SGRSTPropertyName = 5
    DISPID_SGRSTPropertyId = 6
    DISPID_SGRSTPropertyValue = 7
    DISPID_SGRSTNextState = 8


class SpeechEmulationCompareFlags(IntFlag):
    SECFIgnoreCase = 1
    SECFIgnoreKanaType = 65536
    SECFIgnoreWidth = 131072
    SECFNoSpecialChars = 536870912
    SECFEmulateResult = 1073741824
    SECFDefault = 196609


class SPLEXICONTYPE(IntFlag):
    eLEXTYPE_USER = 1
    eLEXTYPE_APP = 2
    eLEXTYPE_VENDORLEXICON = 4
    eLEXTYPE_LETTERTOSOUND = 8
    eLEXTYPE_MORPHOLOGY = 16
    eLEXTYPE_RESERVED4 = 32
    eLEXTYPE_USER_SHORTCUT = 64
    eLEXTYPE_RESERVED6 = 128
    eLEXTYPE_RESERVED7 = 256
    eLEXTYPE_RESERVED8 = 512
    eLEXTYPE_RESERVED9 = 1024
    eLEXTYPE_RESERVED10 = 2048
    eLEXTYPE_PRIVATE1 = 4096
    eLEXTYPE_PRIVATE2 = 8192
    eLEXTYPE_PRIVATE3 = 16384
    eLEXTYPE_PRIVATE4 = 32768
    eLEXTYPE_PRIVATE5 = 65536
    eLEXTYPE_PRIVATE6 = 131072
    eLEXTYPE_PRIVATE7 = 262144
    eLEXTYPE_PRIVATE8 = 524288
    eLEXTYPE_PRIVATE9 = 1048576
    eLEXTYPE_PRIVATE10 = 2097152
    eLEXTYPE_PRIVATE11 = 4194304
    eLEXTYPE_PRIVATE12 = 8388608
    eLEXTYPE_PRIVATE13 = 16777216
    eLEXTYPE_PRIVATE14 = 33554432
    eLEXTYPE_PRIVATE15 = 67108864
    eLEXTYPE_PRIVATE16 = 134217728
    eLEXTYPE_PRIVATE17 = 268435456
    eLEXTYPE_PRIVATE18 = 536870912
    eLEXTYPE_PRIVATE19 = 1073741824
    eLEXTYPE_PRIVATE20 = -2147483648


class DISPID_SpeechRecognizerStatus(IntFlag):
    DISPID_SRSAudioStatus = 1
    DISPID_SRSCurrentStreamPosition = 2
    DISPID_SRSCurrentStreamNumber = 3
    DISPID_SRSNumberOfActiveRules = 4
    DISPID_SRSClsidEngine = 5
    DISPID_SRSSupportedLanguages = 6


class DISPID_SpeechRecoContext(IntFlag):
    DISPID_SRCRecognizer = 1
    DISPID_SRCAudioInInterferenceStatus = 2
    DISPID_SRCRequestedUIType = 3
    DISPID_SRCVoice = 4
    DISPID_SRAllowVoiceFormatMatchingOnNextSet = 5
    DISPID_SRCVoicePurgeEvent = 6
    DISPID_SRCEventInterests = 7
    DISPID_SRCCmdMaxAlternates = 8
    DISPID_SRCState = 9
    DISPID_SRCRetainedAudio = 10
    DISPID_SRCRetainedAudioFormat = 11
    DISPID_SRCPause = 12
    DISPID_SRCResume = 13
    DISPID_SRCCreateGrammar = 14
    DISPID_SRCCreateResultFromMemory = 15
    DISPID_SRCBookmark = 16
    DISPID_SRCSetAdaptationData = 17


class DISPIDSPRG(IntFlag):
    DISPID_SRGId = 1
    DISPID_SRGRecoContext = 2
    DISPID_SRGState = 3
    DISPID_SRGRules = 4
    DISPID_SRGReset = 5
    DISPID_SRGCommit = 6
    DISPID_SRGCmdLoadFromFile = 7
    DISPID_SRGCmdLoadFromObject = 8
    DISPID_SRGCmdLoadFromResource = 9
    DISPID_SRGCmdLoadFromMemory = 10
    DISPID_SRGCmdLoadFromProprietaryGrammar = 11
    DISPID_SRGCmdSetRuleState = 12
    DISPID_SRGCmdSetRuleIdState = 13
    DISPID_SRGDictationLoad = 14
    DISPID_SRGDictationUnload = 15
    DISPID_SRGDictationSetState = 16
    DISPID_SRGSetWordSequenceData = 17
    DISPID_SRGSetTextSelection = 18
    DISPID_SRGIsPronounceable = 19


SPAUDIOSTATE = _SPAUDIOSTATE
SPSTREAMFORMATTYPE = SPWAVEFORMATTYPE


__all__ = [
    'DISPID_SVGetVoices', 'typelib_path', 'SPRST_INACTIVE_WITH_PURGE',
    'SVEStartInputStream', 'DISPID_SRCPause',
    'SAFTCCITT_ALaw_22kHzMono', 'SPWORDPRONUNCIATIONLIST',
    'DISPID_SOTIsUISupported', 'ISpShortcut', 'SVEViseme',
    'SVSFDefault', 'eLEXTYPE_PRIVATE18', 'DISPID_SRGReset',
    'eWORDTYPE_DELETED', 'SAFTCCITT_ALaw_11kHzStereo', 'SWTAdded',
    'eLEXTYPE_MORPHOLOGY', 'DISPID_SOTCreateInstance', 'SVP_8',
    'SPWF_SRENGINE', 'DISPID_SPRDisplayAttributes',
    'DISPID_SPIProperties', 'SSTTWildcard', 'SPPS_Verb',
    'SPEI_SR_PRIVATE', 'SVSFVoiceMask', 'SpeechWordType',
    'DISPID_SPILanguageId', 'SAFTTrueSpeech_8kHz1BitMono',
    'ISpeechLexicon', 'SVEPrivate', 'DISPID_SpeechPhraseProperties',
    'eLEXTYPE_RESERVED8', 'SpeechTokenShellFolder',
    'DISPID_SLRemovePronunciationByPhoneIds',
    'ISpPhoneticAlphabetConverter', 'SINoSignal', 'SPWT_LEXICAL',
    'DISPID_SAFGetWaveFormatEx', 'eLEXTYPE_RESERVED10',
    'ISpeechRecoContext', 'SDTAll', 'SPEI_SR_AUDIO_LEVEL',
    'DISPID_SPEAudioSizeTime', 'SpMMAudioIn', 'ISpObjectWithToken',
    'SpeechMicTraining', 'STCRemoteServer', 'eLEXTYPE_APP',
    'DISPID_SPIAudioSizeTime', 'SpeechPartOfSpeech',
    'eLEXTYPE_PRIVATE16', 'SPAUDIOSTATE', 'SAFT44kHz8BitMono',
    'DISPID_SDKGetlongValue', 'ISpRecoContext', 'SBONone',
    'SP_VISEME_10', 'SpeechAudioFormatType', 'SpVoice',
    'DISPID_SPARecoResult', 'DISPID_SVESentenceBoundary',
    'DISPID_SpeechFileStream', 'SECFIgnoreWidth',
    'SAFT12kHz16BitStereo', 'DISPID_SLWWord',
    'DISPID_SPEAudioSizeBytes', 'SpeechLexiconType',
    'ISpeechPhraseElements', 'DISPID_SOTCEnumerateTokens',
    'SVSFParseSapi', 'DISPID_SDKOpenKey', 'DISPID_SLPsCount',
    'ISpeechPhraseInfo', 'SRTStandard', 'SPINTERFERENCE_TOOQUIET',
    'SGDSActiveWithAutoPause', 'SPPS_Modifier', 'SPEI_RESERVED5',
    'SpeechAudioFormatGUIDWave', 'SRTAutopause', 'ISpeechFileStream',
    'SVPOver', 'ISpeechObjectTokenCategory',
    'SAFTCCITT_ALaw_8kHzStereo', 'SBOPause', 'DISPID_SPEDisplayText',
    'DISPID_SpeechPhraseReplacement', 'SpeechBookmarkOptions',
    'eLEXTYPE_PRIVATE11', 'SASClosed', 'SpeechRecognitionType',
    'DISPID_SRSAudioStatus', 'SpAudioFormat', 'DISPID_SLGetWords',
    'SPEI_RESERVED3', 'SAFTGSM610_44kHzMono', 'ISpeechXMLRecoResult',
    'SpeechRuleState', 'DISPID_SpeechObjectToken',
    'SAFTCCITT_ALaw_22kHzStereo', 'DISPID_SPIStartTime',
    'DISPID_SpeechPhoneConverter', 'DISPID_SpeechAudioFormat',
    'SVSFParseMask', 'SPDKL_LocalMachine',
    'DISPID_SpeechRecognizerStatus', 'DISPID_SPPChildren',
    'SAFT24kHz8BitMono', 'ISpeechRecoResult',
    'DISPID_SGRSTransitions', 'ISpRecognizer3',
    'DISPID_SRSSupportedLanguages', 'DISPID_SLWsCount',
    'SPFM_CREATE_ALWAYS', 'DISPID_SRCEStartStream',
    'DISPID_SPIReplacements', 'SpeechPropertyComplexResponseSpeed',
    'SPGS_EXCLUSIVE', 'DISPID_SRCCreateGrammar', 'SRCS_Disabled',
    'SpFileStream', 'SpResourceManager', 'SRARoot',
    'DISPID_SpeechGrammarRuleStateTransitions', 'DISPID_SOTDataKey',
    'SpeechDisplayAttributes', 'DISPID_SpeechGrammarRuleState',
    'DISPID_SOTId', 'SpeechEngineConfidence',
    'DISPID_SRCERecognizerStateChange', 'DISPID_SPEsItem',
    'ISpObjectToken', 'SP_VISEME_1', 'SVP_12', 'DISPID_SCSBaseStream',
    'SpeechDictationTopicSpelling', 'IEnumString', 'SpMemoryStream',
    'SpeechTokenIdUserLexicon', 'SDTPronunciation',
    'DISPID_SPAStartElementInResult', 'SRADynamic',
    'DISPID_SGRsCommit', 'DISPID_SRCCmdMaxAlternates',
    'SPBO_TIME_UNITS', 'DISPID_SGRsFindRule', 'DISPID_SOTCSetId',
    'DISPID_SPPNumberOfElements', 'SpeechCategoryAppLexicons',
    'SP_VISEME_8', 'DISPID_SpeechObjectTokenCategory',
    'SPEI_RESERVED2', 'SpeechVisemeFeature', 'SPPS_Interjection',
    'SPEI_INTERFERENCE', 'ISpeechPhraseAlternates', 'DISPID_SGRsItem',
    'SAFT32kHz16BitMono', 'ISpeechRecoResultDispatch', 'SPVISEMES',
    'SpUnCompressedLexicon', 'DISPID_SRIsShared', 'ISpLexicon',
    'LONG_PTR', 'ISpEventSink', 'SRESoundEnd', 'SREAdaptation',
    'SLODynamic', 'DISPID_SRRGetXMLErrorInfo', 'SGPronounciation',
    'DISPID_SOTCGetDataKey', 'SPPROPERTYINFO',
    'SpeechGrammarTagUnlimitedDictation', 'DISPID_SRGRules',
    'DISPID_SLPsItem', 'SAFT44kHz16BitStereo', 'SPEI_START_SR_STREAM',
    'SREStreamStart', 'SREFalseRecognition', 'SAFT32kHz8BitStereo',
    'DISPID_SPRuleName', 'DISPID_SPANumberOfElementsInResult',
    'SPCS_ENABLED', 'SPADAPTATIONRELEVANCE', 'tagSPTEXTSELECTIONINFO',
    'DISPIDSPTSI_SelectionLength', 'ISpPhrase',
    'DISPID_SRCVoicePurgeEvent', 'DISPID_SpeechPhraseAlternate',
    'SPINTERFERENCE', 'SP_VISEME_14', 'DISPID_SOTs_NewEnum',
    'SPVOICESTATUS', 'SVP_1', 'SVP_6', 'DISPID_SRCEEnginePrivate',
    'ISpeechRecoGrammar', 'DISPID_SAFSetWaveFormatEx',
    'SpeechAddRemoveWord', 'DISPID_SWFESamplesPerSec',
    'SPRECOGNIZERSTATUS', 'SPPARTOFSPEECH', 'SPWORDPRONOUNCEABLE',
    'DISPID_SRGCmdLoadFromObject', 'SpPhoneConverter', 'SPSHT_EMAIL',
    'SPEI_VISEME', 'DISPID_SVSLastStreamNumberQueued',
    'DISPID_SRSetPropertyString', 'SGRSTTRule', 'IEnumSpObjectTokens',
    'DISPID_SGRClear', 'DISPID_SPRuleFirstElement',
    'SPSERIALIZEDPHRASE', 'SPWORDLIST', 'SAFTADPCM_44kHzMono',
    'DISPID_SRGCmdLoadFromProprietaryGrammar', 'SpeechVoicePriority',
    'SPPHRASERULE', 'SP_VISEME_2', 'SAFTCCITT_ALaw_44kHzMono',
    'SPINTERFERENCE_NOISE', 'SPEI_START_INPUT_STREAM',
    'DISPID_SBSRead', 'SPFM_OPEN_READWRITE',
    'DISPID_SRCSetAdaptationData', 'SPEI_ADAPTATION',
    'DISPID_SRRTStreamTime', 'SDKLDefaultLocation',
    'SpeechEmulationCompareFlags', 'SPRST_INACTIVE', 'STCAll',
    'ISpeechGrammarRuleState', 'SP_VISEME_3', 'SVF_None',
    'DISPID_SOTDisplayUI', 'DISPID_SWFEExtraData',
    'SAFT11kHz16BitMono', 'SPAR_Low', 'SFTInput', 'SPFM_CREATE',
    'DISPIDSPRG', 'SPPS_Noncontent', 'SPSHT_NotOverriden',
    'DISPID_SRCEAudioLevel', 'SPPS_Function', 'SVPAlert',
    'SPEI_ACTIVE_CATEGORY_CHANGED', 'SREAudioLevel', 'SPGS_DISABLED',
    'ISpeechRecognizer', 'SVP_18', 'DISPID_SMSADeviceId',
    'DISPID_SRGIsPronounceable', 'SAFT48kHz8BitMono', 'SRERequestUI',
    'eLEXTYPE_PRIVATE8', 'SpNotifyTranslator',
    'SpeechSpecialTransitionType',
    'DISPID_SLAddPronunciationByPhoneIds', 'SVP_17',
    'SSSPTRelativeToCurrentPosition', 'SAFTCCITT_uLaw_8kHzMono',
    'DISPID_SpeechVoiceEvent', 'SPRS_ACTIVE',
    'DISPID_SpeechPhraseRule', 'DISPID_SPRFirstElement',
    'DISPID_SRCState', 'ISpeechMemoryStream', 'SpMMAudioEnum',
    'SpPhoneticAlphabetConverter', 'DISPID_SRRRecoContext',
    'SPAS_PAUSE', 'ISpeechLexiconWords',
    'SPINTERFERENCE_LATENCY_TRUNCATE_BEGIN', 'SpeechGrammarWordType',
    'SPSHORTCUTPAIR', 'SVP_4', 'DISPID_SPAsItem', 'ISpNotifySource',
    'SPAO_NONE', 'SpObjectTokenCategory', 'SPEI_SENTENCE_BOUNDARY',
    'SPINTERFERENCE_LATENCY_TRUNCATE_END', 'eLEXTYPE_PRIVATE3',
    'ISpeechCustomStream', 'SAFTDefault', 'DISPID_SVEBookmark',
    'SAFTADPCM_44kHzStereo', 'SPAS_CLOSED', 'eLEXTYPE_PRIVATE15',
    'SPPS_RESERVED1', 'DISPID_SVEPhoneme', 'SPCS_DISABLED',
    'SGSDisabled', 'SPSTREAMFORMATTYPE', 'SPBOOKMARKOPTIONS',
    'SPGS_ENABLED', 'DISPID_SMSGetData', 'SRTSMLTimeout',
    'SPEVENTSOURCEINFO', 'DISPID_SPEs_NewEnum', 'DISPID_SVPriority',
    'DISPID_SPEActualConfidence', 'DISPID_SVStatus',
    'SpTextSelectionInformation', 'SAFT11kHz8BitMono',
    'ISpeechPhraseProperties', 'ISpeechAudioBufferInfo',
    'DISPID_SRRTTickCount', 'SAFTCCITT_uLaw_11kHzStereo',
    'DISPID_SVEViseme', 'DISPID_SVSpeakCompleteEvent',
    'DISPID_SABIBufferSize', 'SpeechWordPronounceable',
    'SPWT_DISPLAY', 'SpeechCategoryAudioIn', 'SPFILEMODE',
    'SpeechAudioState', 'SAFT22kHz8BitMono', 'SpObjectToken',
    'SAFT22kHz8BitStereo', 'DISPID_SMSALineId', 'SPSLMA',
    'DISPID_SVEWord', 'DISPID_SPISaveToMemory', 'ISpGrammarBuilder',
    'DISPID_SRGCmdLoadFromResource', 'SECFDefault', 'SP_VISEME_16',
    'SVP_0', 'SPWP_UNKNOWN_WORD_PRONOUNCEABLE', 'SVEVoiceChange',
    'DISPID_SGRSAddRuleTransition', 'DISPID_SVSkip',
    'DISPID_SLGenerationId', 'ISpObjectTokenCategory',
    'eLEXTYPE_PRIVATE12', 'DISPID_SRCEPropertyNumberChange',
    'DISPID_SRRTLength', 'SpSharedRecognizer', 'SAFT16kHz8BitStereo',
    'eWORDTYPE_ADDED', 'SP_VISEME_12', 'ISpMMSysAudio',
    'SpeechRecoProfileProperties', 'SAFTCCITT_uLaw_11kHzMono',
    'DISPID_SpeechBaseStream', 'DISPID_SRGSetTextSelection',
    'SpeechPropertyResourceUsage', 'DISPID_SVDisplayUI',
    'ISpRecognizer2', 'SpeechRecoEvents',
    'DISPIDSPTSI_SelectionOffset', 'DISPID_SLPPartOfSpeech',
    'SAFTCCITT_uLaw_8kHzStereo', 'SVSFPurgeBeforeSpeak',
    'SPEI_MIN_TTS', 'DISPID_SpeechAudioBufferInfo', 'SGSExclusive',
    'tagSTATSTG', 'SPRST_ACTIVE', 'SPEI_PHONEME',
    'DISPID_SpeechLexiconPronunciation', 'ISpRecoContext2',
    'SSTTTextBuffer', 'DISPID_SPPParent', 'eLEXTYPE_RESERVED6',
    'DISPID_SASFreeBufferSpace', 'DISPID_SGRAddResource', 'SPPHRASE',
    'DISPID_SGRAddState', 'SPEI_PROPERTY_NUM_CHANGE',
    'SGLexicalNoSpecialChars', 'DISPID_SASNonBlockingIO',
    'SPEI_SOUND_END', 'SPRULE', 'SRESoundStart',
    'SPINTERFERENCE_NONE', 'SAFTCCITT_uLaw_44kHzStereo',
    'DISPID_SVSpeakStream', 'DISPID_SVPause', 'DISPID_SVResume',
    'DISPID_SOTsCount', 'ISpeechAudioFormat', 'SVSFIsFilename',
    'SPEI_HYPOTHESIS', 'SPSHT_OTHER', 'DISPID_SPEAudioTimeOffset',
    'DISPID_SPRsCount', 'SAFT16kHz16BitMono',
    'SpeechVoiceSkipTypeSentence', 'DISPID_SAFType',
    'DISPID_SPIGetDisplayAttributes',
    'DISPID_SRCERecognitionForOtherContext',
    'DISPID_SPERetainedSizeBytes', 'ISpNotifySink',
    'DISPID_SRCEInterference', 'ISpRecoGrammar',
    'DISPID_SRCCreateResultFromMemory', 'SPPHRASEELEMENT',
    'DISPID_SDKEnumValues', 'SSFMCreateForWrite', 'ISpVoice',
    'SpeechRecoContextState', 'DISPID_SPEEngineConfidence',
    'SpSharedRecoContext', 'ISpeechTextSelectionInformation',
    'SAFTExtendedAudioFormat', 'eLEXTYPE_PRIVATE10', 'SREHypothesis',
    'DISPID_SWFEBitsPerSample', 'SPCATEGORYTYPE', 'SASPause',
    'SITooSlow', 'DISPID_SpeechCustomStream', 'SAFT12kHz8BitStereo',
    'SRAORetainAudio', 'DISPID_SWFEChannels', 'SP_VISEME_21',
    'DISPID_SRAudioInput', 'DISPID_SPEDisplayAttributes',
    'SRAInterpreter', 'SVSFPersistXML', 'SPBINARYGRAMMAR',
    'SAFT12kHz16BitMono', 'SPRECOSTATE', 'DISPID_SAEventHandle',
    'SAFTADPCM_8kHzStereo', 'DISPID_SpeechPhraseRules',
    'DISPID_SpeechLexiconProns', 'DISPID_SGRSTs_NewEnum',
    'SPWAVEFORMATTYPE', 'SP_VISEME_20', 'SPINTERFERENCE_TOOSLOW',
    'SPWORD', 'DISPID_SPRuleConfidence', 'SECNormalConfidence',
    'ISpeechObjectToken', 'SECFEmulateResult', 'DISPID_SRState',
    'DISPID_SRCEventInterests', 'DISPID_SpeechLexicon',
    'DISPID_SpeechLexiconWords', 'SpeechGrammarTagWildcard',
    'SpeechTokenContext', 'SRTReSent', 'SAFT11kHz16BitStereo',
    'SAFT48kHz16BitMono', 'SpeechCategoryPhoneConverters',
    'SPEI_SOUND_START', 'DISPID_SRCEPhraseStart', 'SP_VISEME_9',
    'SP_VISEME_18', 'DISPID_SRAllowAudioInputFormatChangesOnNextSet',
    'SPPS_RESERVED4', 'SVP_11', 'eLEXTYPE_LETTERTOSOUND',
    'SREAllEvents', 'SAFTCCITT_ALaw_8kHzMono',
    'DISPID_SRIsUISupported', 'SpeechTokenKeyAttributes',
    'DISPID_SRRDiscardResultInfo', 'SpeechRecognizerState',
    'SPSFunction', 'DISPID_SBSWrite', 'SpMMAudioOut', 'SP_VISEME_11',
    'SpLexicon', 'Speech_Default_Weight', 'DISPID_SVVolume',
    'DISPID_SRCreateRecoContext', 'DISPID_SABIMinNotification',
    'SREPrivate', 'ISpAudio', 'SGDSInactive', 'ISpeechPhoneConverter',
    'SRAImport', 'DISPID_SGRSRule', 'DISPID_SLWLangId', 'SPSMF_UPS',
    'SPVPRI_NORMAL', 'SpeechAudioProperties', 'DISPID_SPRuleParent',
    'SGRSTTWildcard', 'SDTRule', 'DISPID_SRRSaveToMemory',
    'SAFTCCITT_ALaw_11kHzMono', 'SPRST_ACTIVE_ALWAYS',
    'DISPID_SPACommit', 'DISPID_SRCESoundEnd', 'SpStream',
    'DISPID_SPIGetText', 'DISPID_SRProfile', 'STSF_CommonAppData',
    'DISPIDSPTSI_ActiveOffset', 'DISPID_SPRText', 'SRSActive',
    'ISpSerializeState', 'DISPID_SRGRecoContext',
    'DISPID_SGRs_NewEnum', 'SASRun', 'SSTTDictation',
    'SPEI_SR_RETAINEDAUDIO', 'DISPID_SRCEPropertyStringChange',
    'ISpeechDataKey', 'SpeechPropertyHighConfidenceThreshold',
    'DISPID_SPPFirstElement', 'DISPID_SAFGuid', 'eLEXTYPE_PRIVATE7',
    'SpeechUserTraining', 'SpInprocRecognizer', 'SAFTText',
    'DISPID_SRGCmdLoadFromMemory', 'SPDKL_CurrentUser',
    'SAFT44kHz8BitStereo', 'DISPID_SPRulesItem', 'SVSFNLPMask',
    'SpeechRuleAttributes', 'SRSInactiveWithPurge', 'ISpRecoCategory',
    'ISpeechPhraseRule', 'eLEXTYPE_VENDORLEXICON',
    'DISPID_SPCIdToPhone', 'SPXRO_Alternates_SML',
    'eLEXTYPE_RESERVED7', 'DISPID_SRCEAdaptation',
    'SVSFParseAutodetect', 'SVF_Emphasis',
    'SpeechCategoryRecognizers', 'SPLEXICONTYPE',
    'SPSHORTCUTPAIRLIST', 'SP_VISEME_15', 'DISPID_SGRSTPropertyName',
    'DISPID_SVEStreamStart', 'DISPID_SRRPhraseInfo',
    'DISPID_SRSNumberOfActiveRules', 'DISPID_SRRSetTextFeedback',
    'DISPID_SpeechRecognizer', 'DISPID_SRStatus',
    'ISpResourceManager', 'SVP_14', 'DISPID_SPCPhoneToId',
    'SVSFParseSsml', 'DISPID_SGRSAddWordTransition',
    'DISPID_SpeechMemoryStream', 'DISPID_SWFEFormatTag',
    'DISPID_SVSyncronousSpeakTimeout', 'ISpeechLexiconPronunciations',
    'ISpNotifyTranslator', 'SP_VISEME_6',
    'DISPID_SRGCmdSetRuleIdState', 'DISPID_SRRTOffsetFromStart',
    'STSF_FlagCreate', 'DISPID_SPIAudioStreamPosition',
    'SPPS_RESERVED2', 'ISpStream', 'SDTReplacement', 'SGSEnabled',
    'DISPID_SAStatus', 'SGRSTTTextBuffer', 'ISpPhoneConverter',
    'SLTUser', 'ISpeechRecognizerStatus',
    'DISPID_SpeechPhraseAlternates', 'SPPS_Unknown',
    'DISPID_SRGetPropertyString', 'SP_VISEME_4', 'SFTSREngine',
    'ISpeechRecoResultTimes', 'SPEI_TTS_PRIVATE',
    'DISPID_SRCERequestUI', 'DISPID_SpeechWaveFormatEx',
    'SpeechDataKeyLocation', 'DISPID_SRGCmdSetRuleState',
    'SVSFlagsAsync', 'DISPID_SRCRequestedUIType',
    'DISPID_SPRules_NewEnum', 'DISPID_SpeechRecoContextEvents',
    'ISpRecoGrammar2', 'SPRS_ACTIVE_WITH_AUTO_PAUSE',
    'DISPID_SDKSetBinaryValue', 'SVP_16',
    'DISPID_SLGetGenerationChange', 'DISPID_SRGId',
    'SWPKnownWordPronounceable', 'SpeechCategoryRecoProfiles',
    'DISPID_SPRulesCount', 'DISPID_SpeechPhraseInfo',
    'SpeechCategoryAudioOut', 'DISPID_SRRTimes', 'DISPID_SDKEnumKeys',
    'SPSERIALIZEDRESULT', 'SPSMF_SAPI_PROPERTIES', 'SVP_5',
    'DISPID_SGRSTNextState', 'SAFT44kHz16BitMono', 'STCInprocHandler',
    'SDTDisplayText', 'DISPID_SRCVoice', 'DISPID_SRDisplayUI',
    'IInternetSecurityMgrSite', 'SPDATAKEYLOCATION',
    'SVSFUnusedFlags', 'DISPID_SPPsItem', 'SITooFast', 'SVP_10',
    'DISPID_SGRSTsCount', 'eLEXTYPE_RESERVED9', 'SPAO_RETAIN_AUDIO',
    'SPEVENT', 'DISPID_SPAs_NewEnum', 'SDA_No_Trailing_Space',
    'DISPID_SRAudioInputStream', 'SAFTGSM610_22kHzMono',
    'SPLO_STATIC', 'DISPID_SRRecognizer', 'SPVPRI_ALERT',
    'SAFTGSM610_8kHzMono', 'SPEI_END_INPUT_STREAM', 'DISPID_SGRId',
    'SAFTADPCM_22kHzStereo', 'eLEXTYPE_PRIVATE4',
    'DISPID_SVSLastBookmark', 'SLTApp', 'SAFTCCITT_uLaw_22kHzMono',
    'STSF_AppData', 'SVF_Stressed', 'SPGRAMMARSTATE', 'DISPID_SPPId',
    'DISPID_SPPValue', 'DISPID_SDKDeleteKey', 'DISPID_SLPType',
    'ISpeechVoiceStatus', 'DISPID_SpeechObjectTokens',
    'SPRECOCONTEXTSTATUS', 'SPPS_SuppressWord', 'STSF_LocalAppData',
    'SRSEIsSpeaking', 'DISPID_SABufferInfo', 'ISpeechLexiconWord',
    'SPEI_TTS_AUDIO_LEVEL', 'DISPID_SRRSpeakAudio', 'SPEI_UNDEFINED',
    'DISPID_SVSpeak', 'SPXRO_SML', 'SDTLexicalForm',
    'SpeechCategoryVoices', 'DISPID_SPPConfidence', 'SGRSTTWord',
    'SVEAllEvents', 'DISPID_SpeechPhraseProperty',
    'eLEXTYPE_PRIVATE2', 'SPSInterjection', 'SRTEmulated',
    'DISPID_SASCurrentDevicePosition', 'SpPhraseInfoBuilder',
    'DISPID_SVSRunningState', 'ISpeechPhraseProperty',
    'SpeechPropertyLowConfidenceThreshold', 'DISPID_SVSPhonemeId',
    'SREPropertyStringChange', 'DISPID_SRCRecognizer',
    'DISPID_SpeechVoice', 'DISPID_SDKCreateKey',
    '_ISpeechRecoContextEvents', 'eLEXTYPE_PRIVATE6',
    'ISpeechMMSysAudio', 'DISPID_SLRemovePronunciation',
    'DISPID_SRCEHypothesis', 'DISPID_SRRAudioFormat',
    'SPCT_SUB_COMMAND', 'SPLO_DYNAMIC', 'SRSActiveAlways', 'Library',
    'DISPID_SPPsCount',
    'DISPID_SVAllowAudioOuputFormatChangesOnNextSet',
    'DISPID_SpeechAudioStatus', 'SpStreamFormatConverter',
    'SPPS_Noun', 'DISPID_SPRuleId', 'ISpeechPhraseAlternate',
    'eLEXTYPE_PRIVATE20', 'DISPID_SpeechGrammarRule',
    'DISPID_SpeechAudio', 'SPBO_NONE', 'SPEI_PHRASE_START',
    'SpeechAudioVolume', 'SPWF_INPUT', 'SPAR_Unknown',
    'DISPID_SRGetPropertyNumber', 'eLEXTYPE_USER', 'ISpRecognizer',
    'SVPNormal', 'DISPID_SDKSetStringValue',
    'DISPID_SVSInputWordPosition', 'DISPID_SVWaitUntilDone',
    'SPEI_MAX_SR', 'SPINTERFERENCE_NOSIGNAL', 'ISpPhraseAlt',
    'SpeechGrammarState', 'DISPID_SDKGetStringValue',
    'STCLocalServer', 'DISPID_SpeechRecoResult',
    'SAFTCCITT_uLaw_22kHzStereo', 'SDA_Consume_Leading_Spaces',
    'SPLOADOPTIONS', 'DISPID_SpeechRecoResultTimes',
    'DISPID_SADefaultFormat', 'DISPID_SPPEngineConfidence',
    'SPSMF_SRGS_SAPIPROPERTIES', 'SPSUnknown', 'SAFTADPCM_11kHzMono',
    'SPEI_RECO_STATE_CHANGE', 'DISPID_SFSOpen',
    'Speech_Max_Word_Length', 'SpeechAudioFormatGUIDText',
    'DISPID_SPIRetainedSizeBytes', 'DISPID_SGRSTType',
    'DISPID_SRCRetainedAudio', 'SPRECORESULTTIMES', 'SPSSuppressWord',
    'DISPID_SDKSetLongValue', 'SAFT8kHz16BitMono', 'DISPID_SBSSeek',
    'DISPID_SpeechGrammarRuleStateTransition', 'SREPropertyNumChange',
    'SDKLCurrentConfig', 'SDKLLocalMachine', 'ISpeechWaveFormatEx',
    'SGDisplay', 'SPRS_ACTIVE_USER_DELIMITED', 'SpCustomStream',
    'DISPID_SRCRetainedAudioFormat', 'SPSVerb', 'DISPID_SLPSymbolic',
    'SPCT_SLEEP', 'SPEI_FALSE_RECOGNITION',
    'DISPID_SpeechRecoResult2', 'DISPID_SOTCId',
    'SWPUnknownWordPronounceable', 'SPRST_NUM_STATES',
    'DISPID_SLWsItem', 'SASStop', 'DISPID_SVSLastResult',
    'SpeechTokenKeyFiles', 'DISPID_SGRsAdd',
    'DISPID_SRGCmdLoadFromFile', 'ISpeechRecoResult2',
    'SAFT24kHz8BitStereo', 'DISPID_SVEAudioLevel',
    'ISpeechGrammarRuleStateTransition', '__MIDL_IWinTypes_0009',
    'ISpeechPhraseElement', 'DISPID_SpeechPhraseElements',
    'ISpeechGrammarRules', 'DISPID_SPRuleNumberOfElements',
    'SPINTERFERENCE_TOOFAST', 'DISPIDSPTSI', 'SAFTNonStandardFormat',
    'SGDSActive', 'DISPID_SPIEngineId', 'SDTAlternates',
    'DISPID_SPPs_NewEnum', 'WAVEFORMATEX', 'Speech_StreamPos_Asap',
    'SPEI_END_SR_STREAM', 'SAFTADPCM_11kHzStereo', 'SVP_20',
    'DISPID_SRSClsidEngine', 'SPAS_STOP', 'SpeechEngineProperties',
    'SpeechVoiceCategoryTTSRate', 'SVSFIsNotXML',
    'SPEI_PROPERTY_STRING_CHANGE',
    '__MIDL___MIDL_itf_sapi_0000_0020_0002', 'SPDKL_DefaultLocation',
    'DISPID_SRCEBookmark', 'DISPID_SPEsCount',
    'DISPID_SASCurrentSeekPosition', 'SECHighConfidence',
    'SAFTNoAssignedFormat', 'SPFM_OPEN_READONLY', 'SPEI_TTS_BOOKMARK',
    'SSFMCreate', 'DISPID_SPRs_NewEnum',
    'DISPID_SRGSetWordSequenceData', 'SVESentenceBoundary',
    'DISPID_SPIRule', 'DISPID_SLWPronunciations',
    'ISpeechLexiconPronunciation', 'DISPID_SGRSAddSpecialTransition',
    'ISpeechGrammarRule', 'DISPID_SGRAttributes', 'SVP_15',
    'DISPID_SRAllowVoiceFormatMatchingOnNextSet',
    'SPTEXTSELECTIONINFO', 'SAFT24kHz16BitMono',
    'SpeechRetainedAudioOptions', 'SpeechRegistryLocalMachineRoot',
    'ISpeechObjectTokens', 'DISPID_SLPPhoneIds',
    'SpInProcRecoContext', 'DISPID_SAVolume',
    'DISPID_SOTGetStorageFileName', 'SPPS_RESERVED3',
    'DISPID_SASState', 'DISPID_SPRsItem', 'SPRULESTATE',
    'DISPID_SGRSTText', 'DISPID_SABIEventBias',
    'DISPID_SVAudioOutput', 'DISPID_SpeechPhraseBuilder',
    'SVEPhoneme', '__MIDL___MIDL_itf_sapi_0000_0020_0001', 'SDTAudio',
    'DISPID_SpeechVoiceStatus', 'DISPID_SRRAudio', 'ISpStreamFormat',
    'DISPID_SOTSetId', 'SREBookmark', 'SpeechDiscardType',
    'SSSPTRelativeToEnd', 'DISPID_SDKDeleteValue',
    'SAFT48kHz8BitStereo', 'SVEBookmark', 'DISPID_SPIElements',
    'eLEXTYPE_PRIVATE14', 'SPRS_INACTIVE',
    'DISPID_SpeechXMLRecoResult', 'SGRSTTDictation', 'SPFM_NUM_MODES',
    'SRTExtendableParse', 'eLEXTYPE_PRIVATE5',
    'DISPID_SRSCurrentStreamPosition', 'DISPID_SPIEnginePrivateData',
    'DISPID_SRGDictationLoad', '_SPAUDIOSTATE', 'SSFMOpenReadWrite',
    'DISPID_SPEAudioStreamOffset', 'SPSHORTCUTTYPE',
    'DISPID_SPCLangId', 'DISPIDSPTSI_ActiveLength',
    'SpeechGrammarRuleStateTransitionType', 'eLEXTYPE_PRIVATE17',
    'DISPID_SPRNumberOfElements', 'SpeechAllElements',
    'DISPID_SRRGetXMLResult', 'SpeechTokenKeyUI', 'tagSPPROPERTYINFO',
    'SVSFIsXML', 'DISPID_SGRSTRule', 'DISPID_SVSLastBookmarkId',
    'DISPID_SGRsDynamic', 'DISPID_SRSetPropertyNumber',
    'SPCT_COMMAND', 'SREPhraseStart', 'SPAUDIOBUFFERINFO',
    'DISPID_SPRuleChildren', 'SPEI_WORD_BOUNDARY',
    'DISPID_SREmulateRecognition', 'DISPID_SRCERecognition',
    'SPVPRIORITY', 'SPVPRI_OVER', 'SAFT48kHz16BitStereo',
    'SPEI_RESERVED1', 'SRAExport', 'DISPID_SGRSTPropertyValue',
    'SP_VISEME_17', 'Speech_Max_Pron_Length', 'SWTDeleted',
    'ISpeechPhraseReplacement', 'SPINTERFERENCE_LATENCY_WARNING',
    'SRAONone', 'ISpStreamFormatConverter', 'DISPID_SLPs_NewEnum',
    'ISpPhoneticAlphabetSelection', 'SSFMOpenForRead',
    'SDA_One_Trailing_Space', 'SDKLCurrentUser', 'ISpProperties',
    'SpeechRunState', 'ISpEventSource', 'SPSModifier',
    'SGDSActiveUserDelimited', 'eLEXTYPE_PRIVATE1',
    'SAFT22kHz16BitStereo', 'DISPID_SPERequiredConfidence',
    'DISPID_SpeechRecoContext', 'SPAR_Medium', 'SAFT11kHz8BitStereo',
    'SP_VISEME_5', 'eLEXTYPE_PRIVATE13', 'SPPS_LMA',
    'SAFTADPCM_8kHzMono', 'SpeechFormatType', 'ISpDataKey',
    'DISPID_SOTGetDescription', 'SPSHT_Unknown', 'SINone', 'SVP_21',
    'DISPID_SGRSTPropertyId', '_RemotableHandle',
    'DISPID_SPRuleEngineConfidence', 'ISpeechPhraseInfoBuilder',
    'SpNullPhoneConverter', 'DISPID_SOTsItem', '_ISpeechVoiceEvents',
    'SP_VISEME_13', 'SECLowConfidence',
    'DISPID_SRSCurrentStreamNumber', 'DISPID_SVGetProfiles',
    'SAFT8kHz8BitMono', 'SPBO_AHEAD', 'SPSEMANTICFORMAT',
    'SPBO_PAUSE', 'SPSEMANTICERRORINFO', 'DISPID_SLGetPronunciations',
    'SPEI_SR_BOOKMARK', 'SPSNoun', 'DISPID_SRCEFalseRecognition',
    'DISPID_SVEventInterests', 'SAFT16kHz16BitStereo',
    'DISPID_SpeechLexiconWord', 'SWPUnknownWordUnpronounceable',
    'DISPID_SVSCurrentStreamNumber',
    'SPSMF_SRGS_SEMANTICINTERPRETATION_MS',
    'SPWT_LEXICAL_NO_SPECIAL_CHARS', 'SINoise', 'SPEI_RESERVED6',
    'DISPID_SPERetainedStreamOffset', 'DISPID_SABufferNotifySize',
    'DISPID_SOTCDefault', 'DISPID_SLAddPronunciation',
    'DISPID_SMSAMMHandle', 'IInternetSecurityManager',
    'DISPID_SVRate', 'DISPID_SPIAudioSizeBytes', 'SVEEndInputStream',
    'SAFTGSM610_11kHzMono', 'DISPID_SPELexicalForm',
    'DISPID_SpeechMMSysAudio', 'SpeechVoiceEvents',
    'SPDKL_CurrentConfig', 'DISPID_SBSFormat',
    'DISPID_SVIsUISupported', 'SPPS_NotOverriden',
    'SpeechPropertyNormalConfidenceThreshold', 'DISPID_SRRAlternates',
    'DISPID_SVGetAudioInputs', 'SPWP_KNOWN_WORD_PRONOUNCEABLE',
    'DISPID_SOTRemoveStorageFileName', 'DISPID_SOTRemove',
    'SPINTERFERENCE_TOOLOUD', 'SP_VISEME_7', 'ISpeechVoice',
    'DISPID_SRGetFormat', 'eLEXTYPE_PRIVATE19', 'SITooQuiet',
    'SAFT8kHz16BitStereo', 'SRATopLevel', 'DISPID_SRCBookmark',
    'DISPID_SRCEEndStream', 'DISPID_SpeechGrammarRules',
    'DISPID_SVEEnginePrivate', 'SPPHRASEPROPERTY', 'SPEI_MAX_TTS',
    'SPEVENTENUM', 'SAFT16kHz8BitMono', 'SPPHRASEREPLACEMENT',
    'SVEWordBoundary', 'DISPID_SVAudioOutputStream', 'SVEAudioLevel',
    'DISPID_SLWType', 'SPAR_High', 'DISPID_SPEPronunciation',
    'ISpXMLRecoResult', 'SREInterference',
    'ISpeechPhraseReplacements', 'DISPID_SPAsCount',
    'SpeechRegistryUserRoot', 'ISpRecoResult',
    'DISPID_SRGetRecognizers', 'SpeechInterference',
    'SAFT22kHz16BitMono', 'DISPID_SGRsCount',
    'DISPID_SPPBRestorePhraseFromMemory', 'DISPID_SRGState',
    'SRERecognition', 'SGLexical', 'ISpeechPhraseRules',
    'ISpeechBaseStream', 'SDTProperty', 'DISPID_SVEVoiceChange',
    'ISpeechGrammarRuleStateTransitions', 'DISPID_SWFEBlockAlign',
    'DISPID_SOTMatchesAttributes', 'DISPID_SRCESoundStart',
    'DISPID_SASetState', 'SRCS_Enabled', 'ISpeechAudio',
    'SPCONTEXTSTATE', 'ISpeechResourceLoader', 'SpeechStreamFileMode',
    'SVP_7', 'SpeechVoiceSpeakFlags', 'DISPID_SRGDictationSetState',
    'SECFIgnoreKanaType', 'DISPID_SVSInputWordLength', 'SREStreamEnd',
    'SPWORDPRONUNCIATION', 'SPEI_RECOGNITION', 'SRADefaultToActive',
    'SECFIgnoreCase', 'SP_VISEME_0', 'DISPID_SWFEAvgBytesPerSec',
    'SREStateChange', 'SRSEDone', 'eLEXTYPE_USER_SHORTCUT',
    'DISPID_SOTCategory', 'DISPID_SRGDictationUnload',
    'SpeechLoadOption', 'SPWP_UNKNOWN_WORD_UNPRONOUNCEABLE',
    'SPXMLRESULTOPTIONS', 'SGRSTTEpsilon', 'SECFNoSpecialChars',
    'SPSMF_SRGS_SEMANTICINTERPRETATION_W3C', 'SPWT_PRONUNCIATION',
    'DISPID_SFSClose', 'DISPID_SVGetAudioOutputs', 'DISPID_SLPLangId',
    'SPEI_RECO_OTHER_CONTEXT', 'DISPID_SGRSTWeight',
    'SPEI_VOICE_CHANGE', 'SPGRAMMARWORDTYPE', 'SPAUDIOSTATUS',
    'DISPID_SPPName', 'DISPID_SpeechPhraseReplacements',
    'DISPID_SRCResume', 'SpeechVisemeType', 'SPCT_SUB_DICTATION',
    'SpeechPropertyAdaptationOn', 'SLOStatic',
    'SpeechTokenValueCLSID', 'DISPID_SVSVisemeId',
    'DISPID_SGRsCommitAndSave', 'DISPID_SpeechDataKey',
    'SAFTADPCM_22kHzMono', 'SPSNotOverriden', 'DISPID_SPAPhraseInfo',
    'DISPID_SLWs_NewEnum', 'SPCT_DICTATION', 'SAFT8kHz8BitStereo',
    'DISPID_SVSInputSentencePosition', 'eLEXTYPE_PRIVATE9',
    'DISPID_SGRName', 'SpeechPropertyResponseSpeed',
    'DISPID_SOTGetAttribute', 'SAFTCCITT_uLaw_44kHzMono',
    'DISPID_SVVoice', 'UINT_PTR', 'SDA_Two_Trailing_Spaces',
    'SpWaveFormatEx', 'SpeechGrammarTagDictation',
    'DISPID_SVSInputSentenceLength', 'DISPID_SVEStreamEnd', 'SVP_3',
    'DISPID_SGRInitialState', 'SVSFNLPSpeakPunc',
    'SAFTCCITT_ALaw_44kHzStereo', 'SAFT32kHz8BitMono',
    'DISPID_SDKGetBinaryValue', 'DISPID_SGRSTsItem',
    'SRERecoOtherContext', 'SVP_2', 'SAFT32kHz16BitStereo',
    'SPAS_RUN', 'SVP_13', 'DISPID_SRGCommit',
    'DISPID_SVAlertBoundary', 'SPEI_MIN_SR', 'SpCompressedLexicon',
    'SAFT24kHz16BitStereo', 'SP_VISEME_19', 'SAFT12kHz8BitMono',
    'SITooLoud', 'SVP_9', 'eLEXTYPE_RESERVED4', 'DISPID_SMSSetData',
    'SPEI_REQUEST_UI', 'SpeechStreamSeekPositionType', 'IStream',
    'SpShortcut', 'ISpeechAudioStatus', 'STCInprocServer',
    'SPWORDTYPE', 'SPAUDIOOPTIONS', 'SRSInactive',
    'SSSPTRelativeToStart', 'DISPID_SRCAudioInInterferenceStatus',
    'Speech_StreamPos_RealTime', 'DISPID_SPIGrammarId',
    'DISPID_SpeechPhraseElement', 'SVP_19'
]

