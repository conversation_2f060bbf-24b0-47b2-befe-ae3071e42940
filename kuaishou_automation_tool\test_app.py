"""
快手无人带货工具 - 应用程序测试
Kuaishou Automation Tool - Application Test

本模块用于测试应用程序的基本功能，包括：
- 界面显示测试
- 数据库连接测试
- 基础功能验证
"""

import sys
import os
import unittest
from PyQt6.QtWidgets import QApplication
from PyQt6.QtTest import QTest
from PyQt6.QtCore import Qt

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from ui.main_window import MainWindow
from ui.components.navigation import NavigationWidget
from ui.components.account_widget import AccountWidget
from data.models.base import DatabaseManager, ConfigManager
from data.models.account import Account, AccountStatus, AccountType
from data.repositories.account_repository import AccountRepository


class TestMainWindow(unittest.TestCase):
    """主窗口测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        if not QApplication.instance():
            cls.app = QApplication(sys.argv)
        else:
            cls.app = QApplication.instance()
            
    def setUp(self):
        """每个测试前的准备"""
        self.main_window = MainWindow()
        
    def tearDown(self):
        """每个测试后的清理"""
        if self.main_window:
            self.main_window.close()
            
    def test_window_creation(self):
        """测试窗口创建"""
        self.assertIsNotNone(self.main_window)
        self.assertEqual(self.main_window.windowTitle(), "快手无人带货工具")
        
    def test_window_size(self):
        """测试窗口尺寸"""
        self.main_window.show()
        size = self.main_window.size()
        self.assertEqual(size.width(), 1200)
        self.assertEqual(size.height(), 800)
        
    def test_navigation_widget(self):
        """测试导航栏组件"""
        nav_widget = self.main_window.findChild(NavigationWidget)
        if nav_widget:
            self.assertIsNotNone(nav_widget)
            self.assertEqual(nav_widget.get_current_page(), "account_management")


class TestNavigationWidget(unittest.TestCase):
    """导航栏组件测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        if not QApplication.instance():
            cls.app = QApplication(sys.argv)
        else:
            cls.app = QApplication.instance()
            
    def setUp(self):
        """每个测试前的准备"""
        self.nav_widget = NavigationWidget()
        
    def tearDown(self):
        """每个测试后的清理"""
        if self.nav_widget:
            self.nav_widget.close()
            
    def test_navigation_creation(self):
        """测试导航栏创建"""
        self.assertIsNotNone(self.nav_widget)
        self.assertEqual(self.nav_widget.get_current_page(), "account_management")
        
    def test_page_switching(self):
        """测试页面切换"""
        # 测试切换到商品管理页面
        self.nav_widget.set_current_page("product_management")
        self.assertEqual(self.nav_widget.get_current_page(), "product_management")
        
        # 测试切换到系统设置页面
        self.nav_widget.set_current_page("system_settings")
        self.assertEqual(self.nav_widget.get_current_page(), "system_settings")


class TestAccountWidget(unittest.TestCase):
    """账号管理组件测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        if not QApplication.instance():
            cls.app = QApplication(sys.argv)
        else:
            cls.app = QApplication.instance()
            
    def setUp(self):
        """每个测试前的准备"""
        self.account_widget = AccountWidget()
        
    def tearDown(self):
        """每个测试后的清理"""
        if self.account_widget:
            self.account_widget.close()
            
    def test_account_widget_creation(self):
        """测试账号管理组件创建"""
        self.assertIsNotNone(self.account_widget)
        self.assertIsNotNone(self.account_widget.account_table)
        
    def test_sample_data_loading(self):
        """测试示例数据加载"""
        # 检查是否有示例数据
        self.assertGreater(len(self.account_widget.accounts_data), 0)
        
        # 检查表格行数
        self.assertEqual(
            self.account_widget.account_table.rowCount(),
            len(self.account_widget.accounts_data)
        )


class TestDatabaseConnection(unittest.TestCase):
    """数据库连接测试类"""
    
    def setUp(self):
        """每个测试前的准备"""
        self.db_manager = DatabaseManager()
        
    def test_database_initialization(self):
        """测试数据库初始化"""
        try:
            self.db_manager.initialize()
            self.assertIsNotNone(self.db_manager.engine)
            self.assertIsNotNone(self.db_manager.SessionLocal)
        except Exception as e:
            self.fail(f"数据库初始化失败: {e}")
            
    def test_database_session(self):
        """测试数据库会话"""
        try:
            self.db_manager.initialize()
            session = self.db_manager.get_session()
            self.assertIsNotNone(session)
            session.close()
        except Exception as e:
            self.fail(f"数据库会话创建失败: {e}")


class TestAccountModel(unittest.TestCase):
    """账号模型测试类"""
    
    def test_account_creation(self):
        """测试账号对象创建"""
        account = Account()
        account.username = "test_user"
        account.nickname = "测试用户"
        account.status = AccountStatus.OFFLINE
        account.account_type = AccountType.TEST
        
        self.assertEqual(account.username, "test_user")
        self.assertEqual(account.nickname, "测试用户")
        self.assertEqual(account.status, AccountStatus.OFFLINE)
        self.assertEqual(account.account_type, AccountType.TEST)
        
    def test_account_validation(self):
        """测试账号数据验证"""
        account = Account()
        
        # 测试用户名验证
        with self.assertRaises(ValueError):
            account.username = ""  # 空用户名应该抛出异常
            
        with self.assertRaises(ValueError):
            account.username = "ab"  # 太短的用户名应该抛出异常
            
        # 测试昵称验证
        with self.assertRaises(ValueError):
            account.nickname = ""  # 空昵称应该抛出异常


class TestAccountRepository(unittest.TestCase):
    """账号仓库测试类"""
    
    def setUp(self):
        """每个测试前的准备"""
        self.db_manager = DatabaseManager()
        self.db_manager.initialize()
        self.repository = AccountRepository()
        
    def test_repository_creation(self):
        """测试仓库创建"""
        self.assertIsNotNone(self.repository)
        self.assertIsNotNone(self.repository.db_manager)


class TestConfigManager(unittest.TestCase):
    """配置管理器测试类"""
    
    def setUp(self):
        """每个测试前的准备"""
        self.config_manager = ConfigManager()
        
    def test_config_loading(self):
        """测试配置加载"""
        self.assertIsNotNone(self.config_manager)
        
        # 测试获取默认配置
        app_name = self.config_manager.get('app.name')
        self.assertIsNotNone(app_name)
        
    def test_config_operations(self):
        """测试配置操作"""
        # 测试设置配置
        self.config_manager.set('test.key', 'test_value')
        value = self.config_manager.get('test.key')
        self.assertEqual(value, 'test_value')
        
        # 测试默认值
        default_value = self.config_manager.get('non.existent.key', 'default')
        self.assertEqual(default_value, 'default')


def run_basic_tests():
    """运行基础测试"""
    print("=" * 60)
    print("快手无人带货工具 - 基础功能测试")
    print("=" * 60)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_suite.addTest(TestMainWindow('test_window_creation'))
    test_suite.addTest(TestNavigationWidget('test_navigation_creation'))
    test_suite.addTest(TestAccountWidget('test_account_widget_creation'))
    test_suite.addTest(TestDatabaseConnection('test_database_initialization'))
    test_suite.addTest(TestAccountModel('test_account_creation'))
    test_suite.addTest(TestAccountRepository('test_repository_creation'))
    test_suite.addTest(TestConfigManager('test_config_loading'))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✅ 所有基础测试通过！")
    else:
        print("❌ 部分测试失败！")
        print(f"失败数量: {len(result.failures)}")
        print(f"错误数量: {len(result.errors)}")
    print("=" * 60)
    
    return result.wasSuccessful()


def run_ui_test():
    """运行界面测试"""
    print("\n" + "=" * 60)
    print("快手无人带货工具 - 界面显示测试")
    print("=" * 60)
    
    try:
        # 创建Qt应用程序
        app = QApplication(sys.argv) if not QApplication.instance() else QApplication.instance()
        
        # 创建主窗口
        main_window = MainWindow()
        main_window.show()
        
        print("✅ 主窗口创建成功")
        print(f"窗口标题: {main_window.windowTitle()}")
        print(f"窗口尺寸: {main_window.size().width()} x {main_window.size().height()}")
        
        # 简单的界面交互测试
        QTest.qWait(1000)  # 等待1秒
        
        print("✅ 界面显示正常")
        
        # 关闭窗口
        main_window.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 界面测试失败: {e}")
        return False


if __name__ == "__main__":
    # 运行基础功能测试
    basic_test_passed = run_basic_tests()
    
    # 运行界面测试
    ui_test_passed = run_ui_test()
    
    # 输出总体结果
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"基础功能测试: {'✅ 通过' if basic_test_passed else '❌ 失败'}")
    print(f"界面显示测试: {'✅ 通过' if ui_test_passed else '❌ 失败'}")
    
    if basic_test_passed and ui_test_passed:
        print("\n🎉 所有测试通过！应用程序可以正常运行。")
        exit_code = 0
    else:
        print("\n⚠️ 部分测试失败，请检查相关问题。")
        exit_code = 1
        
    print("=" * 60)
    sys.exit(exit_code)
