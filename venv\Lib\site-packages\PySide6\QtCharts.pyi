# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations
"""
This file contains the exact signatures for all functions in module
PySide6.QtCharts, except for defaults which are replaced by "...".

# mypy: disable-error-code="override, overload-overlap"
"""

# Module `PySide6.QtCharts`

import PySide6.QtCharts
import PySide6.QtCore
import PySide6.QtGui
import PySide6.QtWidgets

import os
import enum
import typing
import collections.abc
from PySide6.QtCore import Signal


class QAbstractAxis(PySide6.QtCore.QObject):

    colorChanged             : typing.ClassVar[Signal] = ... # colorChanged(QColor)
    gridLineColorChanged     : typing.ClassVar[Signal] = ... # gridLineColorChanged(QColor)
    gridLinePenChanged       : typing.ClassVar[Signal] = ... # gridLinePenChanged(QPen)
    gridVisibleChanged       : typing.ClassVar[Signal] = ... # gridVisibleChanged(bool)
    labelsAngleChanged       : typing.ClassVar[Signal] = ... # labelsAngleChanged(int)
    labelsBrushChanged       : typing.ClassVar[Signal] = ... # labelsBrushChanged(QBrush)
    labelsColorChanged       : typing.ClassVar[Signal] = ... # labelsColorChanged(QColor)
    labelsEditableChanged    : typing.ClassVar[Signal] = ... # labelsEditableChanged(bool)
    labelsFontChanged        : typing.ClassVar[Signal] = ... # labelsFontChanged(QFont)
    labelsTruncatedChanged   : typing.ClassVar[Signal] = ... # labelsTruncatedChanged(bool)
    labelsVisibleChanged     : typing.ClassVar[Signal] = ... # labelsVisibleChanged(bool)
    linePenChanged           : typing.ClassVar[Signal] = ... # linePenChanged(QPen)
    lineVisibleChanged       : typing.ClassVar[Signal] = ... # lineVisibleChanged(bool)
    minorGridLineColorChanged: typing.ClassVar[Signal] = ... # minorGridLineColorChanged(QColor)
    minorGridLinePenChanged  : typing.ClassVar[Signal] = ... # minorGridLinePenChanged(QPen)
    minorGridVisibleChanged  : typing.ClassVar[Signal] = ... # minorGridVisibleChanged(bool)
    reverseChanged           : typing.ClassVar[Signal] = ... # reverseChanged(bool)
    shadesBorderColorChanged : typing.ClassVar[Signal] = ... # shadesBorderColorChanged(QColor)
    shadesBrushChanged       : typing.ClassVar[Signal] = ... # shadesBrushChanged(QBrush)
    shadesColorChanged       : typing.ClassVar[Signal] = ... # shadesColorChanged(QColor)
    shadesPenChanged         : typing.ClassVar[Signal] = ... # shadesPenChanged(QPen)
    shadesVisibleChanged     : typing.ClassVar[Signal] = ... # shadesVisibleChanged(bool)
    titleBrushChanged        : typing.ClassVar[Signal] = ... # titleBrushChanged(QBrush)
    titleFontChanged         : typing.ClassVar[Signal] = ... # titleFontChanged(QFont)
    titleTextChanged         : typing.ClassVar[Signal] = ... # titleTextChanged(QString)
    titleVisibleChanged      : typing.ClassVar[Signal] = ... # titleVisibleChanged(bool)
    truncateLabelsChanged    : typing.ClassVar[Signal] = ... # truncateLabelsChanged(bool)
    visibleChanged           : typing.ClassVar[Signal] = ... # visibleChanged(bool)

    class AxisType(enum.Enum):

        AxisTypeNoAxis            = ...  # 0x0
        AxisTypeValue             = ...  # 0x1
        AxisTypeBarCategory       = ...  # 0x2
        AxisTypeCategory          = ...  # 0x4
        AxisTypeDateTime          = ...  # 0x8
        AxisTypeLogValue          = ...  # 0x10
        AxisTypeColor             = ...  # 0x20


    def alignment(self, /) -> PySide6.QtCore.Qt.AlignmentFlag: ...
    def gridLineColor(self, /) -> PySide6.QtGui.QColor: ...
    def gridLinePen(self, /) -> PySide6.QtGui.QPen: ...
    def hide(self, /) -> None: ...
    def isGridLineVisible(self, /) -> bool: ...
    def isLineVisible(self, /) -> bool: ...
    def isMinorGridLineVisible(self, /) -> bool: ...
    def isReverse(self, /) -> bool: ...
    def isTitleVisible(self, /) -> bool: ...
    def isVisible(self, /) -> bool: ...
    def labelsAngle(self, /) -> int: ...
    def labelsBrush(self, /) -> PySide6.QtGui.QBrush: ...
    def labelsColor(self, /) -> PySide6.QtGui.QColor: ...
    def labelsEditable(self, /) -> bool: ...
    def labelsFont(self, /) -> PySide6.QtGui.QFont: ...
    def labelsTruncated(self, /) -> bool: ...
    def labelsVisible(self, /) -> bool: ...
    def linePen(self, /) -> PySide6.QtGui.QPen: ...
    def linePenColor(self, /) -> PySide6.QtGui.QColor: ...
    def minorGridLineColor(self, /) -> PySide6.QtGui.QColor: ...
    def minorGridLinePen(self, /) -> PySide6.QtGui.QPen: ...
    def orientation(self, /) -> PySide6.QtCore.Qt.Orientation: ...
    def setGridLineColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setGridLinePen(self, pen: PySide6.QtGui.QPen | PySide6.QtCore.Qt.PenStyle | PySide6.QtGui.QColor, /) -> None: ...
    def setGridLineVisible(self, /, visible: bool = ...) -> None: ...
    def setLabelsAngle(self, angle: int, /) -> None: ...
    def setLabelsBrush(self, brush: PySide6.QtGui.QBrush | PySide6.QtCore.Qt.BrushStyle | PySide6.QtCore.Qt.GlobalColor | PySide6.QtGui.QColor | PySide6.QtGui.QGradient | PySide6.QtGui.QImage | PySide6.QtGui.QPixmap, /) -> None: ...
    def setLabelsColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setLabelsEditable(self, /, editable: bool = ...) -> None: ...
    def setLabelsFont(self, font: PySide6.QtGui.QFont | str | collections.abc.Sequence[str], /) -> None: ...
    def setLabelsVisible(self, /, visible: bool = ...) -> None: ...
    def setLinePen(self, pen: PySide6.QtGui.QPen | PySide6.QtCore.Qt.PenStyle | PySide6.QtGui.QColor, /) -> None: ...
    def setLinePenColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setLineVisible(self, /, visible: bool = ...) -> None: ...
    def setMax(self, max: typing.Any, /) -> None: ...
    def setMin(self, min: typing.Any, /) -> None: ...
    def setMinorGridLineColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setMinorGridLinePen(self, pen: PySide6.QtGui.QPen | PySide6.QtCore.Qt.PenStyle | PySide6.QtGui.QColor, /) -> None: ...
    def setMinorGridLineVisible(self, /, visible: bool = ...) -> None: ...
    def setRange(self, min: typing.Any, max: typing.Any, /) -> None: ...
    def setReverse(self, /, reverse: bool = ...) -> None: ...
    def setShadesBorderColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setShadesBrush(self, brush: PySide6.QtGui.QBrush | PySide6.QtCore.Qt.BrushStyle | PySide6.QtCore.Qt.GlobalColor | PySide6.QtGui.QColor | PySide6.QtGui.QGradient | PySide6.QtGui.QImage | PySide6.QtGui.QPixmap, /) -> None: ...
    def setShadesColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setShadesPen(self, pen: PySide6.QtGui.QPen | PySide6.QtCore.Qt.PenStyle | PySide6.QtGui.QColor, /) -> None: ...
    def setShadesVisible(self, /, visible: bool = ...) -> None: ...
    def setTitleBrush(self, brush: PySide6.QtGui.QBrush | PySide6.QtCore.Qt.BrushStyle | PySide6.QtCore.Qt.GlobalColor | PySide6.QtGui.QColor | PySide6.QtGui.QGradient | PySide6.QtGui.QImage | PySide6.QtGui.QPixmap, /) -> None: ...
    def setTitleFont(self, font: PySide6.QtGui.QFont | str | collections.abc.Sequence[str], /) -> None: ...
    def setTitleText(self, title: str, /) -> None: ...
    def setTitleVisible(self, /, visible: bool = ...) -> None: ...
    def setTruncateLabels(self, /, truncateLabels: bool = ...) -> None: ...
    def setVisible(self, /, visible: bool = ...) -> None: ...
    def shadesBorderColor(self, /) -> PySide6.QtGui.QColor: ...
    def shadesBrush(self, /) -> PySide6.QtGui.QBrush: ...
    def shadesColor(self, /) -> PySide6.QtGui.QColor: ...
    def shadesPen(self, /) -> PySide6.QtGui.QPen: ...
    def shadesVisible(self, /) -> bool: ...
    def show(self, /) -> None: ...
    def titleBrush(self, /) -> PySide6.QtGui.QBrush: ...
    def titleFont(self, /) -> PySide6.QtGui.QFont: ...
    def titleText(self, /) -> str: ...
    def truncateLabels(self, /) -> bool: ...
    def type(self, /) -> PySide6.QtCharts.QAbstractAxis.AxisType: ...


class QAbstractBarSeries(PySide6.QtCharts.QAbstractSeries):

    barsetsAdded             : typing.ClassVar[Signal] = ... # barsetsAdded(QList<QBarSet*>)
    barsetsRemoved           : typing.ClassVar[Signal] = ... # barsetsRemoved(QList<QBarSet*>)
    clicked                  : typing.ClassVar[Signal] = ... # clicked(int,QBarSet*)
    countChanged             : typing.ClassVar[Signal] = ... # countChanged()
    doubleClicked            : typing.ClassVar[Signal] = ... # doubleClicked(int,QBarSet*)
    hovered                  : typing.ClassVar[Signal] = ... # hovered(bool,int,QBarSet*)
    labelsAngleChanged       : typing.ClassVar[Signal] = ... # labelsAngleChanged(double)
    labelsFormatChanged      : typing.ClassVar[Signal] = ... # labelsFormatChanged(QString)
    labelsPositionChanged    : typing.ClassVar[Signal] = ... # labelsPositionChanged(QAbstractBarSeries::LabelsPosition)
    labelsPrecisionChanged   : typing.ClassVar[Signal] = ... # labelsPrecisionChanged(int)
    labelsVisibleChanged     : typing.ClassVar[Signal] = ... # labelsVisibleChanged()
    pressed                  : typing.ClassVar[Signal] = ... # pressed(int,QBarSet*)
    released                 : typing.ClassVar[Signal] = ... # released(int,QBarSet*)

    class LabelsPosition(enum.Enum):

        LabelsCenter              = ...  # 0x0
        LabelsInsideEnd           = ...  # 0x1
        LabelsInsideBase          = ...  # 0x2
        LabelsOutsideEnd          = ...  # 0x3


    @typing.overload
    def append(self, set: PySide6.QtCharts.QBarSet, /) -> bool: ...
    @typing.overload
    def append(self, sets: collections.abc.Sequence[PySide6.QtCharts.QBarSet], /) -> bool: ...
    def barSets(self, /) -> typing.List[PySide6.QtCharts.QBarSet]: ...
    def barWidth(self, /) -> float: ...
    def clear(self, /) -> None: ...
    def count(self, /) -> int: ...
    def insert(self, index: int, set: PySide6.QtCharts.QBarSet, /) -> bool: ...
    def isLabelsVisible(self, /) -> bool: ...
    def labelsAngle(self, /) -> float: ...
    def labelsFormat(self, /) -> str: ...
    def labelsPosition(self, /) -> PySide6.QtCharts.QAbstractBarSeries.LabelsPosition: ...
    def labelsPrecision(self, /) -> int: ...
    def remove(self, set: PySide6.QtCharts.QBarSet, /) -> bool: ...
    def setBarWidth(self, width: float, /) -> None: ...
    def setLabelsAngle(self, angle: float, /) -> None: ...
    def setLabelsFormat(self, format: str, /) -> None: ...
    def setLabelsPosition(self, position: PySide6.QtCharts.QAbstractBarSeries.LabelsPosition, /) -> None: ...
    def setLabelsPrecision(self, precision: int, /) -> None: ...
    def setLabelsVisible(self, /, visible: bool = ...) -> None: ...
    def take(self, set: PySide6.QtCharts.QBarSet, /) -> bool: ...


class QAbstractSeries(PySide6.QtCore.QObject):

    nameChanged              : typing.ClassVar[Signal] = ... # nameChanged()
    opacityChanged           : typing.ClassVar[Signal] = ... # opacityChanged()
    useOpenGLChanged         : typing.ClassVar[Signal] = ... # useOpenGLChanged()
    visibleChanged           : typing.ClassVar[Signal] = ... # visibleChanged()

    class SeriesType(enum.Enum):

        SeriesTypeLine            = ...  # 0x0
        SeriesTypeArea            = ...  # 0x1
        SeriesTypeBar             = ...  # 0x2
        SeriesTypeStackedBar      = ...  # 0x3
        SeriesTypePercentBar      = ...  # 0x4
        SeriesTypePie             = ...  # 0x5
        SeriesTypeScatter         = ...  # 0x6
        SeriesTypeSpline          = ...  # 0x7
        SeriesTypeHorizontalBar   = ...  # 0x8
        SeriesTypeHorizontalStackedBar = ...  # 0x9
        SeriesTypeHorizontalPercentBar = ...  # 0xa
        SeriesTypeBoxPlot         = ...  # 0xb
        SeriesTypeCandlestick     = ...  # 0xc


    def attachAxis(self, axis: PySide6.QtCharts.QAbstractAxis, /) -> bool: ...
    def attachedAxes(self, /) -> typing.List[PySide6.QtCharts.QAbstractAxis]: ...
    def chart(self, /) -> PySide6.QtCharts.QChart: ...
    def detachAxis(self, axis: PySide6.QtCharts.QAbstractAxis, /) -> bool: ...
    def hide(self, /) -> None: ...
    def isVisible(self, /) -> bool: ...
    def name(self, /) -> str: ...
    def opacity(self, /) -> float: ...
    def setName(self, name: str, /) -> None: ...
    def setOpacity(self, opacity: float, /) -> None: ...
    def setUseOpenGL(self, /, enable: bool = ...) -> None: ...
    def setVisible(self, /, visible: bool = ...) -> None: ...
    def show(self, /) -> None: ...
    def type(self, /) -> PySide6.QtCharts.QAbstractSeries.SeriesType: ...
    def useOpenGL(self, /) -> bool: ...


class QAreaLegendMarker(PySide6.QtCharts.QLegendMarker):

    def __init__(self, series: PySide6.QtCharts.QAreaSeries, legend: PySide6.QtCharts.QLegend, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def series(self, /) -> PySide6.QtCharts.QAreaSeries: ...
    def type(self, /) -> PySide6.QtCharts.QLegendMarker.LegendMarkerType: ...


class QAreaSeries(PySide6.QtCharts.QAbstractSeries):

    borderColorChanged       : typing.ClassVar[Signal] = ... # borderColorChanged(QColor)
    clicked                  : typing.ClassVar[Signal] = ... # clicked(QPointF)
    colorChanged             : typing.ClassVar[Signal] = ... # colorChanged(QColor)
    doubleClicked            : typing.ClassVar[Signal] = ... # doubleClicked(QPointF)
    hovered                  : typing.ClassVar[Signal] = ... # hovered(QPointF,bool)
    pointLabelsClippingChanged: typing.ClassVar[Signal] = ... # pointLabelsClippingChanged(bool)
    pointLabelsColorChanged  : typing.ClassVar[Signal] = ... # pointLabelsColorChanged(QColor)
    pointLabelsFontChanged   : typing.ClassVar[Signal] = ... # pointLabelsFontChanged(QFont)
    pointLabelsFormatChanged : typing.ClassVar[Signal] = ... # pointLabelsFormatChanged(QString)
    pointLabelsVisibilityChanged: typing.ClassVar[Signal] = ... # pointLabelsVisibilityChanged(bool)
    pressed                  : typing.ClassVar[Signal] = ... # pressed(QPointF)
    released                 : typing.ClassVar[Signal] = ... # released(QPointF)
    selected                 : typing.ClassVar[Signal] = ... # selected()

    @typing.overload
    def __init__(self, upperSeries: PySide6.QtCharts.QLineSeries, /, lowerSeries: PySide6.QtCharts.QLineSeries | None = ..., *, color: PySide6.QtGui.QColor | None = ..., borderColor: PySide6.QtGui.QColor | None = ..., pointLabelsFormat: str | None = ..., pointLabelsVisible: bool | None = ..., pointLabelsFont: PySide6.QtGui.QFont | None = ..., pointLabelsColor: PySide6.QtGui.QColor | None = ..., pointLabelsClipping: bool | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, upperSeries: PySide6.QtCharts.QLineSeries | None = ..., lowerSeries: PySide6.QtCharts.QLineSeries | None = ..., color: PySide6.QtGui.QColor | None = ..., borderColor: PySide6.QtGui.QColor | None = ..., pointLabelsFormat: str | None = ..., pointLabelsVisible: bool | None = ..., pointLabelsFont: PySide6.QtGui.QFont | None = ..., pointLabelsColor: PySide6.QtGui.QColor | None = ..., pointLabelsClipping: bool | None = ...) -> None: ...

    def borderColor(self, /) -> PySide6.QtGui.QColor: ...
    def brush(self, /) -> PySide6.QtGui.QBrush: ...
    def color(self, /) -> PySide6.QtGui.QColor: ...
    def lowerSeries(self, /) -> PySide6.QtCharts.QLineSeries: ...
    def pen(self, /) -> PySide6.QtGui.QPen: ...
    def pointLabelsClipping(self, /) -> bool: ...
    def pointLabelsColor(self, /) -> PySide6.QtGui.QColor: ...
    def pointLabelsFont(self, /) -> PySide6.QtGui.QFont: ...
    def pointLabelsFormat(self, /) -> str: ...
    def pointLabelsVisible(self, /) -> bool: ...
    def pointsVisible(self, /) -> bool: ...
    def setBorderColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setBrush(self, brush: PySide6.QtGui.QBrush | PySide6.QtCore.Qt.BrushStyle | PySide6.QtCore.Qt.GlobalColor | PySide6.QtGui.QColor | PySide6.QtGui.QGradient | PySide6.QtGui.QImage | PySide6.QtGui.QPixmap, /) -> None: ...
    def setColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setLowerSeries(self, series: PySide6.QtCharts.QLineSeries, /) -> None: ...
    def setPen(self, pen: PySide6.QtGui.QPen | PySide6.QtCore.Qt.PenStyle | PySide6.QtGui.QColor, /) -> None: ...
    def setPointLabelsClipping(self, /, enabled: bool = ...) -> None: ...
    def setPointLabelsColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setPointLabelsFont(self, font: PySide6.QtGui.QFont | str | collections.abc.Sequence[str], /) -> None: ...
    def setPointLabelsFormat(self, format: str, /) -> None: ...
    def setPointLabelsVisible(self, /, visible: bool = ...) -> None: ...
    def setPointsVisible(self, /, visible: bool = ...) -> None: ...
    def setUpperSeries(self, series: PySide6.QtCharts.QLineSeries, /) -> None: ...
    def type(self, /) -> PySide6.QtCharts.QAbstractSeries.SeriesType: ...
    def upperSeries(self, /) -> PySide6.QtCharts.QLineSeries: ...


class QBarCategoryAxis(PySide6.QtCharts.QAbstractAxis):

    categoriesChanged        : typing.ClassVar[Signal] = ... # categoriesChanged()
    countChanged             : typing.ClassVar[Signal] = ... # countChanged()
    maxChanged               : typing.ClassVar[Signal] = ... # maxChanged(QString)
    minChanged               : typing.ClassVar[Signal] = ... # minChanged(QString)
    rangeChanged             : typing.ClassVar[Signal] = ... # rangeChanged(QString,QString)

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, categories: collections.abc.Sequence[str] | None = ..., min: str | None = ..., max: str | None = ..., count: int | None = ...) -> None: ...

    @typing.overload
    def append(self, category: str, /) -> None: ...
    @typing.overload
    def append(self, categories: collections.abc.Sequence[str], /) -> None: ...
    def at(self, index: int, /) -> str: ...
    def categories(self, /) -> typing.List[str]: ...
    def clear(self, /) -> None: ...
    def count(self, /) -> int: ...
    def insert(self, index: int, category: str, /) -> None: ...
    def max(self, /) -> str: ...
    def min(self, /) -> str: ...
    def remove(self, category: str, /) -> None: ...
    def replace(self, oldCategory: str, newCategory: str, /) -> None: ...
    def setCategories(self, categories: collections.abc.Sequence[str], /) -> None: ...
    def setMax(self, maxCategory: str, /) -> None: ...
    def setMin(self, minCategory: str, /) -> None: ...
    def setRange(self, minCategory: str, maxCategory: str, /) -> None: ...
    def type(self, /) -> PySide6.QtCharts.QAbstractAxis.AxisType: ...


class QBarLegendMarker(PySide6.QtCharts.QLegendMarker):

    def __init__(self, series: PySide6.QtCharts.QAbstractBarSeries, barset: PySide6.QtCharts.QBarSet, legend: PySide6.QtCharts.QLegend, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def barset(self, /) -> PySide6.QtCharts.QBarSet: ...
    def series(self, /) -> PySide6.QtCharts.QAbstractBarSeries: ...
    def type(self, /) -> PySide6.QtCharts.QLegendMarker.LegendMarkerType: ...


class QBarModelMapper(PySide6.QtCore.QObject):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def count(self, /) -> int: ...
    def first(self, /) -> int: ...
    def firstBarSetSection(self, /) -> int: ...
    def lastBarSetSection(self, /) -> int: ...
    def model(self, /) -> PySide6.QtCore.QAbstractItemModel: ...
    def orientation(self, /) -> PySide6.QtCore.Qt.Orientation: ...
    def series(self, /) -> PySide6.QtCharts.QAbstractBarSeries: ...
    def setCount(self, count: int, /) -> None: ...
    def setFirst(self, first: int, /) -> None: ...
    def setFirstBarSetSection(self, firstBarSetSection: int, /) -> None: ...
    def setLastBarSetSection(self, lastBarSetSection: int, /) -> None: ...
    def setModel(self, model: PySide6.QtCore.QAbstractItemModel, /) -> None: ...
    def setOrientation(self, orientation: PySide6.QtCore.Qt.Orientation, /) -> None: ...
    def setSeries(self, series: PySide6.QtCharts.QAbstractBarSeries, /) -> None: ...


class QBarSeries(PySide6.QtCharts.QAbstractBarSeries):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def type(self, /) -> PySide6.QtCharts.QAbstractSeries.SeriesType: ...


class QBarSet(PySide6.QtCore.QObject):

    borderColorChanged       : typing.ClassVar[Signal] = ... # borderColorChanged(QColor)
    brushChanged             : typing.ClassVar[Signal] = ... # brushChanged()
    clicked                  : typing.ClassVar[Signal] = ... # clicked(int)
    colorChanged             : typing.ClassVar[Signal] = ... # colorChanged(QColor)
    doubleClicked            : typing.ClassVar[Signal] = ... # doubleClicked(int)
    hovered                  : typing.ClassVar[Signal] = ... # hovered(bool,int)
    labelBrushChanged        : typing.ClassVar[Signal] = ... # labelBrushChanged()
    labelChanged             : typing.ClassVar[Signal] = ... # labelChanged()
    labelColorChanged        : typing.ClassVar[Signal] = ... # labelColorChanged(QColor)
    labelFontChanged         : typing.ClassVar[Signal] = ... # labelFontChanged()
    penChanged               : typing.ClassVar[Signal] = ... # penChanged()
    pressed                  : typing.ClassVar[Signal] = ... # pressed(int)
    released                 : typing.ClassVar[Signal] = ... # released(int)
    selectedBarsChanged      : typing.ClassVar[Signal] = ... # selectedBarsChanged(QList<int>)
    selectedColorChanged     : typing.ClassVar[Signal] = ... # selectedColorChanged(QColor)
    valueChanged             : typing.ClassVar[Signal] = ... # valueChanged(int)
    valuesAdded              : typing.ClassVar[Signal] = ... # valuesAdded(int,int)
    valuesRemoved            : typing.ClassVar[Signal] = ... # valuesRemoved(int,int)

    def __init__(self, label: str, /, parent: PySide6.QtCore.QObject | None = ..., *, pen: PySide6.QtGui.QPen | None = ..., brush: PySide6.QtGui.QBrush | None = ..., labelBrush: PySide6.QtGui.QBrush | None = ..., labelFont: PySide6.QtGui.QFont | None = ..., color: PySide6.QtGui.QColor | None = ..., borderColor: PySide6.QtGui.QColor | None = ..., labelColor: PySide6.QtGui.QColor | None = ...) -> None: ...

    def __lshift__(self, value: float, /) -> PySide6.QtCharts.QBarSet: ...
    @typing.overload
    def append(self, values: collections.abc.Sequence[float], /) -> None: ...
    @typing.overload
    def append(self, value: float, /) -> None: ...
    def at(self, index: int, /) -> float: ...
    def borderColor(self, /) -> PySide6.QtGui.QColor: ...
    def brush(self, /) -> PySide6.QtGui.QBrush: ...
    def color(self, /) -> PySide6.QtGui.QColor: ...
    def count(self, /) -> int: ...
    def deselectAllBars(self, /) -> None: ...
    def deselectBar(self, index: int, /) -> None: ...
    def deselectBars(self, indexes: collections.abc.Sequence[int], /) -> None: ...
    def insert(self, index: int, value: float, /) -> None: ...
    def isBarSelected(self, index: int, /) -> bool: ...
    def label(self, /) -> str: ...
    def labelBrush(self, /) -> PySide6.QtGui.QBrush: ...
    def labelColor(self, /) -> PySide6.QtGui.QColor: ...
    def labelFont(self, /) -> PySide6.QtGui.QFont: ...
    def pen(self, /) -> PySide6.QtGui.QPen: ...
    def remove(self, index: int, /, count: int = ...) -> None: ...
    def replace(self, index: int, value: float, /) -> None: ...
    def selectAllBars(self, /) -> None: ...
    def selectBar(self, index: int, /) -> None: ...
    def selectBars(self, indexes: collections.abc.Sequence[int], /) -> None: ...
    def selectedBars(self, /) -> typing.List[int]: ...
    def selectedColor(self, /) -> PySide6.QtGui.QColor: ...
    def setBarSelected(self, index: int, selected: bool, /) -> None: ...
    def setBorderColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setBrush(self, brush: PySide6.QtGui.QBrush | PySide6.QtCore.Qt.BrushStyle | PySide6.QtCore.Qt.GlobalColor | PySide6.QtGui.QColor | PySide6.QtGui.QGradient | PySide6.QtGui.QImage | PySide6.QtGui.QPixmap, /) -> None: ...
    def setColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setLabel(self, label: str, /) -> None: ...
    def setLabelBrush(self, brush: PySide6.QtGui.QBrush | PySide6.QtCore.Qt.BrushStyle | PySide6.QtCore.Qt.GlobalColor | PySide6.QtGui.QColor | PySide6.QtGui.QGradient | PySide6.QtGui.QImage | PySide6.QtGui.QPixmap, /) -> None: ...
    def setLabelColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setLabelFont(self, font: PySide6.QtGui.QFont | str | collections.abc.Sequence[str], /) -> None: ...
    def setPen(self, pen: PySide6.QtGui.QPen | PySide6.QtCore.Qt.PenStyle | PySide6.QtGui.QColor, /) -> None: ...
    def setSelectedColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def sum(self, /) -> float: ...
    def toggleSelection(self, indexes: collections.abc.Sequence[int], /) -> None: ...


class QBoxPlotLegendMarker(PySide6.QtCharts.QLegendMarker):

    def __init__(self, series: PySide6.QtCharts.QBoxPlotSeries, legend: PySide6.QtCharts.QLegend, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def series(self, /) -> PySide6.QtCharts.QBoxPlotSeries: ...
    def type(self, /) -> PySide6.QtCharts.QLegendMarker.LegendMarkerType: ...


class QBoxPlotModelMapper(PySide6.QtCore.QObject):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def count(self, /) -> int: ...
    def first(self, /) -> int: ...
    def firstBoxSetSection(self, /) -> int: ...
    def lastBoxSetSection(self, /) -> int: ...
    def model(self, /) -> PySide6.QtCore.QAbstractItemModel: ...
    def orientation(self, /) -> PySide6.QtCore.Qt.Orientation: ...
    def series(self, /) -> PySide6.QtCharts.QBoxPlotSeries: ...
    def setCount(self, count: int, /) -> None: ...
    def setFirst(self, first: int, /) -> None: ...
    def setFirstBoxSetSection(self, firstBoxSetSection: int, /) -> None: ...
    def setLastBoxSetSection(self, lastBoxSetSection: int, /) -> None: ...
    def setModel(self, model: PySide6.QtCore.QAbstractItemModel, /) -> None: ...
    def setOrientation(self, orientation: PySide6.QtCore.Qt.Orientation, /) -> None: ...
    def setSeries(self, series: PySide6.QtCharts.QBoxPlotSeries, /) -> None: ...


class QBoxPlotSeries(PySide6.QtCharts.QAbstractSeries):

    boxOutlineVisibilityChanged: typing.ClassVar[Signal] = ... # boxOutlineVisibilityChanged()
    boxWidthChanged          : typing.ClassVar[Signal] = ... # boxWidthChanged()
    boxsetsAdded             : typing.ClassVar[Signal] = ... # boxsetsAdded(QList<QBoxSet*>)
    boxsetsRemoved           : typing.ClassVar[Signal] = ... # boxsetsRemoved(QList<QBoxSet*>)
    brushChanged             : typing.ClassVar[Signal] = ... # brushChanged()
    clicked                  : typing.ClassVar[Signal] = ... # clicked(QBoxSet*)
    countChanged             : typing.ClassVar[Signal] = ... # countChanged()
    doubleClicked            : typing.ClassVar[Signal] = ... # doubleClicked(QBoxSet*)
    hovered                  : typing.ClassVar[Signal] = ... # hovered(bool,QBoxSet*)
    penChanged               : typing.ClassVar[Signal] = ... # penChanged()
    pressed                  : typing.ClassVar[Signal] = ... # pressed(QBoxSet*)
    released                 : typing.ClassVar[Signal] = ... # released(QBoxSet*)

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, boxOutlineVisible: bool | None = ..., boxWidth: float | None = ..., pen: PySide6.QtGui.QPen | None = ..., brush: PySide6.QtGui.QBrush | None = ..., count: int | None = ...) -> None: ...

    @typing.overload
    def append(self, box: PySide6.QtCharts.QBoxSet, /) -> bool: ...
    @typing.overload
    def append(self, boxes: collections.abc.Sequence[PySide6.QtCharts.QBoxSet], /) -> bool: ...
    def boxOutlineVisible(self, /) -> bool: ...
    def boxSets(self, /) -> typing.List[PySide6.QtCharts.QBoxSet]: ...
    def boxWidth(self, /) -> float: ...
    def brush(self, /) -> PySide6.QtGui.QBrush: ...
    def clear(self, /) -> None: ...
    def count(self, /) -> int: ...
    def insert(self, index: int, box: PySide6.QtCharts.QBoxSet, /) -> bool: ...
    def pen(self, /) -> PySide6.QtGui.QPen: ...
    def remove(self, box: PySide6.QtCharts.QBoxSet, /) -> bool: ...
    def setBoxOutlineVisible(self, visible: bool, /) -> None: ...
    def setBoxWidth(self, width: float, /) -> None: ...
    def setBrush(self, brush: PySide6.QtGui.QBrush | PySide6.QtCore.Qt.BrushStyle | PySide6.QtCore.Qt.GlobalColor | PySide6.QtGui.QColor | PySide6.QtGui.QGradient | PySide6.QtGui.QImage | PySide6.QtGui.QPixmap, /) -> None: ...
    def setPen(self, pen: PySide6.QtGui.QPen | PySide6.QtCore.Qt.PenStyle | PySide6.QtGui.QColor, /) -> None: ...
    def take(self, box: PySide6.QtCharts.QBoxSet, /) -> bool: ...
    def type(self, /) -> PySide6.QtCharts.QAbstractSeries.SeriesType: ...


class QBoxSet(PySide6.QtCore.QObject):

    brushChanged             : typing.ClassVar[Signal] = ... # brushChanged()
    cleared                  : typing.ClassVar[Signal] = ... # cleared()
    clicked                  : typing.ClassVar[Signal] = ... # clicked()
    doubleClicked            : typing.ClassVar[Signal] = ... # doubleClicked()
    hovered                  : typing.ClassVar[Signal] = ... # hovered(bool)
    penChanged               : typing.ClassVar[Signal] = ... # penChanged()
    pressed                  : typing.ClassVar[Signal] = ... # pressed()
    released                 : typing.ClassVar[Signal] = ... # released()
    valueChanged             : typing.ClassVar[Signal] = ... # valueChanged(int)
    valuesChanged            : typing.ClassVar[Signal] = ... # valuesChanged()

    class ValuePositions(enum.IntEnum):

        LowerExtreme              = ...  # 0x0
        LowerQuartile             = ...  # 0x1
        Median                    = ...  # 0x2
        UpperQuartile             = ...  # 0x3
        UpperExtreme              = ...  # 0x4


    @typing.overload
    def __init__(self, /, label: str = ..., parent: PySide6.QtCore.QObject | None = ..., *, pen: PySide6.QtGui.QPen | None = ..., brush: PySide6.QtGui.QBrush | None = ...) -> None: ...
    @typing.overload
    def __init__(self, le: float, lq: float, m: float, uq: float, ue: float, /, label: str = ..., parent: PySide6.QtCore.QObject | None = ..., *, pen: PySide6.QtGui.QPen | None = ..., brush: PySide6.QtGui.QBrush | None = ...) -> None: ...

    def __lshift__(self, value: float, /) -> PySide6.QtCharts.QBoxSet: ...
    @typing.overload
    def append(self, values: collections.abc.Sequence[float], /) -> None: ...
    @typing.overload
    def append(self, value: float, /) -> None: ...
    def at(self, index: int, /) -> float: ...
    def brush(self, /) -> PySide6.QtGui.QBrush: ...
    def clear(self, /) -> None: ...
    def count(self, /) -> int: ...
    def label(self, /) -> str: ...
    def pen(self, /) -> PySide6.QtGui.QPen: ...
    def setBrush(self, brush: PySide6.QtGui.QBrush | PySide6.QtCore.Qt.BrushStyle | PySide6.QtCore.Qt.GlobalColor | PySide6.QtGui.QColor | PySide6.QtGui.QGradient | PySide6.QtGui.QImage | PySide6.QtGui.QPixmap, /) -> None: ...
    def setLabel(self, label: str, /) -> None: ...
    def setPen(self, pen: PySide6.QtGui.QPen | PySide6.QtCore.Qt.PenStyle | PySide6.QtGui.QColor, /) -> None: ...
    def setValue(self, index: int, value: float, /) -> None: ...


class QCandlestickLegendMarker(PySide6.QtCharts.QLegendMarker):

    def __init__(self, series: PySide6.QtCharts.QCandlestickSeries, legend: PySide6.QtCharts.QLegend, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def series(self, /) -> PySide6.QtCharts.QCandlestickSeries: ...
    def type(self, /) -> PySide6.QtCharts.QLegendMarker.LegendMarkerType: ...


class QCandlestickModelMapper(PySide6.QtCore.QObject):

    modelReplaced            : typing.ClassVar[Signal] = ... # modelReplaced()
    seriesReplaced           : typing.ClassVar[Signal] = ... # seriesReplaced()

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, model: PySide6.QtCore.QAbstractItemModel | None = ..., series: PySide6.QtCharts.QCandlestickSeries | None = ...) -> None: ...

    def close(self, /) -> int: ...
    def firstSetSection(self, /) -> int: ...
    def high(self, /) -> int: ...
    def lastSetSection(self, /) -> int: ...
    def low(self, /) -> int: ...
    def model(self, /) -> PySide6.QtCore.QAbstractItemModel: ...
    def open(self, /) -> int: ...
    def orientation(self, /) -> PySide6.QtCore.Qt.Orientation: ...
    def series(self, /) -> PySide6.QtCharts.QCandlestickSeries: ...
    def setClose(self, close: int, /) -> None: ...
    def setFirstSetSection(self, firstSetSection: int, /) -> None: ...
    def setHigh(self, high: int, /) -> None: ...
    def setLastSetSection(self, lastSetSection: int, /) -> None: ...
    def setLow(self, low: int, /) -> None: ...
    def setModel(self, model: PySide6.QtCore.QAbstractItemModel, /) -> None: ...
    def setOpen(self, open: int, /) -> None: ...
    def setSeries(self, series: PySide6.QtCharts.QCandlestickSeries, /) -> None: ...
    def setTimestamp(self, timestamp: int, /) -> None: ...
    def timestamp(self, /) -> int: ...


class QCandlestickSeries(PySide6.QtCharts.QAbstractSeries):

    bodyOutlineVisibilityChanged: typing.ClassVar[Signal] = ... # bodyOutlineVisibilityChanged()
    bodyWidthChanged         : typing.ClassVar[Signal] = ... # bodyWidthChanged()
    brushChanged             : typing.ClassVar[Signal] = ... # brushChanged()
    candlestickSetsAdded     : typing.ClassVar[Signal] = ... # candlestickSetsAdded(QList<QCandlestickSet*>)
    candlestickSetsRemoved   : typing.ClassVar[Signal] = ... # candlestickSetsRemoved(QList<QCandlestickSet*>)
    capsVisibilityChanged    : typing.ClassVar[Signal] = ... # capsVisibilityChanged()
    capsWidthChanged         : typing.ClassVar[Signal] = ... # capsWidthChanged()
    clicked                  : typing.ClassVar[Signal] = ... # clicked(QCandlestickSet*)
    countChanged             : typing.ClassVar[Signal] = ... # countChanged()
    decreasingColorChanged   : typing.ClassVar[Signal] = ... # decreasingColorChanged()
    doubleClicked            : typing.ClassVar[Signal] = ... # doubleClicked(QCandlestickSet*)
    hovered                  : typing.ClassVar[Signal] = ... # hovered(bool,QCandlestickSet*)
    increasingColorChanged   : typing.ClassVar[Signal] = ... # increasingColorChanged()
    maximumColumnWidthChanged: typing.ClassVar[Signal] = ... # maximumColumnWidthChanged()
    minimumColumnWidthChanged: typing.ClassVar[Signal] = ... # minimumColumnWidthChanged()
    penChanged               : typing.ClassVar[Signal] = ... # penChanged()
    pressed                  : typing.ClassVar[Signal] = ... # pressed(QCandlestickSet*)
    released                 : typing.ClassVar[Signal] = ... # released(QCandlestickSet*)

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, count: int | None = ..., maximumColumnWidth: float | None = ..., minimumColumnWidth: float | None = ..., bodyWidth: float | None = ..., bodyOutlineVisible: bool | None = ..., capsWidth: float | None = ..., capsVisible: bool | None = ..., increasingColor: PySide6.QtGui.QColor | None = ..., decreasingColor: PySide6.QtGui.QColor | None = ..., brush: PySide6.QtGui.QBrush | None = ..., pen: PySide6.QtGui.QPen | None = ...) -> None: ...

    @typing.overload
    def append(self, set: PySide6.QtCharts.QCandlestickSet, /) -> bool: ...
    @typing.overload
    def append(self, sets: collections.abc.Sequence[PySide6.QtCharts.QCandlestickSet], /) -> bool: ...
    def bodyOutlineVisible(self, /) -> bool: ...
    def bodyWidth(self, /) -> float: ...
    def brush(self, /) -> PySide6.QtGui.QBrush: ...
    def capsVisible(self, /) -> bool: ...
    def capsWidth(self, /) -> float: ...
    def clear(self, /) -> None: ...
    def count(self, /) -> int: ...
    def decreasingColor(self, /) -> PySide6.QtGui.QColor: ...
    def increasingColor(self, /) -> PySide6.QtGui.QColor: ...
    def insert(self, index: int, set: PySide6.QtCharts.QCandlestickSet, /) -> bool: ...
    def maximumColumnWidth(self, /) -> float: ...
    def minimumColumnWidth(self, /) -> float: ...
    def pen(self, /) -> PySide6.QtGui.QPen: ...
    @typing.overload
    def remove(self, set: PySide6.QtCharts.QCandlestickSet, /) -> bool: ...
    @typing.overload
    def remove(self, sets: collections.abc.Sequence[PySide6.QtCharts.QCandlestickSet], /) -> bool: ...
    def setBodyOutlineVisible(self, bodyOutlineVisible: bool, /) -> None: ...
    def setBodyWidth(self, bodyWidth: float, /) -> None: ...
    def setBrush(self, brush: PySide6.QtGui.QBrush | PySide6.QtCore.Qt.BrushStyle | PySide6.QtCore.Qt.GlobalColor | PySide6.QtGui.QColor | PySide6.QtGui.QGradient | PySide6.QtGui.QImage | PySide6.QtGui.QPixmap, /) -> None: ...
    def setCapsVisible(self, capsVisible: bool, /) -> None: ...
    def setCapsWidth(self, capsWidth: float, /) -> None: ...
    def setDecreasingColor(self, decreasingColor: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setIncreasingColor(self, increasingColor: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setMaximumColumnWidth(self, maximumColumnWidth: float, /) -> None: ...
    def setMinimumColumnWidth(self, minimumColumnWidth: float, /) -> None: ...
    def setPen(self, pen: PySide6.QtGui.QPen | PySide6.QtCore.Qt.PenStyle | PySide6.QtGui.QColor, /) -> None: ...
    def sets(self, /) -> typing.List[PySide6.QtCharts.QCandlestickSet]: ...
    def take(self, set: PySide6.QtCharts.QCandlestickSet, /) -> bool: ...
    def type(self, /) -> PySide6.QtCharts.QAbstractSeries.SeriesType: ...


class QCandlestickSet(PySide6.QtCore.QObject):

    brushChanged             : typing.ClassVar[Signal] = ... # brushChanged()
    clicked                  : typing.ClassVar[Signal] = ... # clicked()
    closeChanged             : typing.ClassVar[Signal] = ... # closeChanged()
    doubleClicked            : typing.ClassVar[Signal] = ... # doubleClicked()
    highChanged              : typing.ClassVar[Signal] = ... # highChanged()
    hovered                  : typing.ClassVar[Signal] = ... # hovered(bool)
    lowChanged               : typing.ClassVar[Signal] = ... # lowChanged()
    openChanged              : typing.ClassVar[Signal] = ... # openChanged()
    penChanged               : typing.ClassVar[Signal] = ... # penChanged()
    pressed                  : typing.ClassVar[Signal] = ... # pressed()
    released                 : typing.ClassVar[Signal] = ... # released()
    timestampChanged         : typing.ClassVar[Signal] = ... # timestampChanged()

    @typing.overload
    def __init__(self, /, timestamp: float = ..., parent: PySide6.QtCore.QObject | None = ..., *, open: float | None = ..., high: float | None = ..., low: float | None = ..., close: float | None = ..., brush: PySide6.QtGui.QBrush | None = ..., pen: PySide6.QtGui.QPen | None = ...) -> None: ...
    @typing.overload
    def __init__(self, open: float, high: float, low: float, close: float, /, timestamp: float = ..., parent: PySide6.QtCore.QObject | None = ..., *, brush: PySide6.QtGui.QBrush | None = ..., pen: PySide6.QtGui.QPen | None = ...) -> None: ...

    def brush(self, /) -> PySide6.QtGui.QBrush: ...
    def close(self, /) -> float: ...
    def high(self, /) -> float: ...
    def low(self, /) -> float: ...
    def open(self, /) -> float: ...
    def pen(self, /) -> PySide6.QtGui.QPen: ...
    def setBrush(self, brush: PySide6.QtGui.QBrush | PySide6.QtCore.Qt.BrushStyle | PySide6.QtCore.Qt.GlobalColor | PySide6.QtGui.QColor | PySide6.QtGui.QGradient | PySide6.QtGui.QImage | PySide6.QtGui.QPixmap, /) -> None: ...
    def setClose(self, close: float, /) -> None: ...
    def setHigh(self, high: float, /) -> None: ...
    def setLow(self, low: float, /) -> None: ...
    def setOpen(self, open: float, /) -> None: ...
    def setPen(self, pen: PySide6.QtGui.QPen | PySide6.QtCore.Qt.PenStyle | PySide6.QtGui.QColor, /) -> None: ...
    def setTimestamp(self, timestamp: float, /) -> None: ...
    def timestamp(self, /) -> float: ...


class QCategoryAxis(PySide6.QtCharts.QValueAxis):

    categoriesChanged        : typing.ClassVar[Signal] = ... # categoriesChanged()
    labelsPositionChanged    : typing.ClassVar[Signal] = ... # labelsPositionChanged(QCategoryAxis::AxisLabelsPosition)

    class AxisLabelsPosition(enum.Enum):

        AxisLabelsPositionCenter  = ...  # 0x0
        AxisLabelsPositionOnValue = ...  # 0x1


    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, startValue: float | None = ..., count: int | None = ..., categoriesLabels: collections.abc.Sequence[str] | None = ..., labelsPosition: PySide6.QtCharts.QCategoryAxis.AxisLabelsPosition | None = ...) -> None: ...

    def append(self, label: str, categoryEndValue: float, /) -> None: ...
    def categoriesLabels(self, /) -> typing.List[str]: ...
    def count(self, /) -> int: ...
    def endValue(self, categoryLabel: str, /) -> float: ...
    def labelsPosition(self, /) -> PySide6.QtCharts.QCategoryAxis.AxisLabelsPosition: ...
    def remove(self, label: str, /) -> None: ...
    def replaceLabel(self, oldLabel: str, newLabel: str, /) -> None: ...
    def setLabelsPosition(self, position: PySide6.QtCharts.QCategoryAxis.AxisLabelsPosition, /) -> None: ...
    def setStartValue(self, min: float, /) -> None: ...
    def startValue(self, /, categoryLabel: str = ...) -> float: ...
    def type(self, /) -> PySide6.QtCharts.QAbstractAxis.AxisType: ...


class QChart(PySide6.QtWidgets.QGraphicsWidget):

    plotAreaChanged          : typing.ClassVar[Signal] = ... # plotAreaChanged(QRectF)

    class AnimationOption(enum.Flag):

        NoAnimation               = ...  # 0x0
        GridAxisAnimations        = ...  # 0x1
        SeriesAnimations          = ...  # 0x2
        AllAnimations             = ...  # 0x3

    class ChartTheme(enum.Enum):

        ChartThemeLight           = ...  # 0x0
        ChartThemeBlueCerulean    = ...  # 0x1
        ChartThemeDark            = ...  # 0x2
        ChartThemeBrownSand       = ...  # 0x3
        ChartThemeBlueNcs         = ...  # 0x4
        ChartThemeHighContrast    = ...  # 0x5
        ChartThemeBlueIcy         = ...  # 0x6
        ChartThemeQt              = ...  # 0x7

    class ChartType(enum.Enum):

        ChartTypeUndefined        = ...  # 0x0
        ChartTypeCartesian        = ...  # 0x1
        ChartTypePolar            = ...  # 0x2


    @typing.overload
    def __init__(self, type: PySide6.QtCharts.QChart.ChartType, parent: PySide6.QtWidgets.QGraphicsItem, wFlags: PySide6.QtCore.Qt.WindowType, /, *, theme: PySide6.QtCharts.QChart.ChartTheme | None = ..., title: str | None = ..., backgroundVisible: bool | None = ..., dropShadowEnabled: bool | None = ..., backgroundRoundness: float | None = ..., animationOptions: PySide6.QtCharts.QChart.AnimationOption | None = ..., animationDuration: int | None = ..., animationEasingCurve: PySide6.QtCore.QEasingCurve | None = ..., margins: PySide6.QtCore.QMargins | None = ..., chartType: PySide6.QtCharts.QChart.ChartType | None = ..., plotAreaBackgroundVisible: bool | None = ..., localizeNumbers: bool | None = ..., locale: PySide6.QtCore.QLocale | None = ..., plotArea: PySide6.QtCore.QRectF | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtWidgets.QGraphicsItem | None = ..., wFlags: PySide6.QtCore.Qt.WindowType = ..., *, theme: PySide6.QtCharts.QChart.ChartTheme | None = ..., title: str | None = ..., backgroundVisible: bool | None = ..., dropShadowEnabled: bool | None = ..., backgroundRoundness: float | None = ..., animationOptions: PySide6.QtCharts.QChart.AnimationOption | None = ..., animationDuration: int | None = ..., animationEasingCurve: PySide6.QtCore.QEasingCurve | None = ..., margins: PySide6.QtCore.QMargins | None = ..., chartType: PySide6.QtCharts.QChart.ChartType | None = ..., plotAreaBackgroundVisible: bool | None = ..., localizeNumbers: bool | None = ..., locale: PySide6.QtCore.QLocale | None = ..., plotArea: PySide6.QtCore.QRectF | None = ...) -> None: ...

    def addAxis(self, axis: PySide6.QtCharts.QAbstractAxis, alignment: PySide6.QtCore.Qt.AlignmentFlag, /) -> None: ...
    def addSeries(self, series: PySide6.QtCharts.QAbstractSeries, /) -> None: ...
    def animationDuration(self, /) -> int: ...
    def animationEasingCurve(self, /) -> PySide6.QtCore.QEasingCurve: ...
    def animationOptions(self, /) -> PySide6.QtCharts.QChart.AnimationOption: ...
    def axes(self, /, orientation: PySide6.QtCore.Qt.Orientation = ..., series: PySide6.QtCharts.QAbstractSeries | None = ...) -> typing.List[PySide6.QtCharts.QAbstractAxis]: ...
    def axisX(self, /, series: PySide6.QtCharts.QAbstractSeries | None = ...) -> PySide6.QtCharts.QAbstractAxis: ...
    def axisY(self, /, series: PySide6.QtCharts.QAbstractSeries | None = ...) -> PySide6.QtCharts.QAbstractAxis: ...
    def backgroundBrush(self, /) -> PySide6.QtGui.QBrush: ...
    def backgroundPen(self, /) -> PySide6.QtGui.QPen: ...
    def backgroundRoundness(self, /) -> float: ...
    def chartType(self, /) -> PySide6.QtCharts.QChart.ChartType: ...
    def createDefaultAxes(self, /) -> None: ...
    def isBackgroundVisible(self, /) -> bool: ...
    def isDropShadowEnabled(self, /) -> bool: ...
    def isPlotAreaBackgroundVisible(self, /) -> bool: ...
    def isZoomed(self, /) -> bool: ...
    def legend(self, /) -> PySide6.QtCharts.QLegend: ...
    def locale(self, /) -> PySide6.QtCore.QLocale: ...
    def localizeNumbers(self, /) -> bool: ...
    def mapToPosition(self, value: PySide6.QtCore.QPointF | PySide6.QtCore.QPoint | PySide6.QtGui.QPainterPath.Element, /, series: PySide6.QtCharts.QAbstractSeries | None = ...) -> PySide6.QtCore.QPointF: ...
    def mapToValue(self, position: PySide6.QtCore.QPointF | PySide6.QtCore.QPoint | PySide6.QtGui.QPainterPath.Element, /, series: PySide6.QtCharts.QAbstractSeries | None = ...) -> PySide6.QtCore.QPointF: ...
    def margins(self, /) -> PySide6.QtCore.QMargins: ...
    def plotArea(self, /) -> PySide6.QtCore.QRectF: ...
    def plotAreaBackgroundBrush(self, /) -> PySide6.QtGui.QBrush: ...
    def plotAreaBackgroundPen(self, /) -> PySide6.QtGui.QPen: ...
    def removeAllSeries(self, /) -> None: ...
    def removeAxis(self, axis: PySide6.QtCharts.QAbstractAxis, /) -> None: ...
    def removeSeries(self, series: PySide6.QtCharts.QAbstractSeries, /) -> None: ...
    def scroll(self, dx: float, dy: float, /) -> None: ...
    def series(self, /) -> typing.List[PySide6.QtCharts.QAbstractSeries]: ...
    def setAnimationDuration(self, msecs: int, /) -> None: ...
    def setAnimationEasingCurve(self, curve: PySide6.QtCore.QEasingCurve | PySide6.QtCore.QEasingCurve.Type, /) -> None: ...
    def setAnimationOptions(self, options: PySide6.QtCharts.QChart.AnimationOption, /) -> None: ...
    def setAxisX(self, axis: PySide6.QtCharts.QAbstractAxis, /, series: PySide6.QtCharts.QAbstractSeries | None = ...) -> None: ...
    def setAxisY(self, axis: PySide6.QtCharts.QAbstractAxis, /, series: PySide6.QtCharts.QAbstractSeries | None = ...) -> None: ...
    def setBackgroundBrush(self, brush: PySide6.QtGui.QBrush | PySide6.QtCore.Qt.BrushStyle | PySide6.QtCore.Qt.GlobalColor | PySide6.QtGui.QColor | PySide6.QtGui.QGradient | PySide6.QtGui.QImage | PySide6.QtGui.QPixmap, /) -> None: ...
    def setBackgroundPen(self, pen: PySide6.QtGui.QPen | PySide6.QtCore.Qt.PenStyle | PySide6.QtGui.QColor, /) -> None: ...
    def setBackgroundRoundness(self, diameter: float, /) -> None: ...
    def setBackgroundVisible(self, /, visible: bool = ...) -> None: ...
    def setDropShadowEnabled(self, /, enabled: bool = ...) -> None: ...
    def setLocale(self, locale: PySide6.QtCore.QLocale | PySide6.QtCore.QLocale.Language, /) -> None: ...
    def setLocalizeNumbers(self, localize: bool, /) -> None: ...
    def setMargins(self, margins: PySide6.QtCore.QMargins, /) -> None: ...
    def setPlotArea(self, rect: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, /) -> None: ...
    def setPlotAreaBackgroundBrush(self, brush: PySide6.QtGui.QBrush | PySide6.QtCore.Qt.BrushStyle | PySide6.QtCore.Qt.GlobalColor | PySide6.QtGui.QColor | PySide6.QtGui.QGradient | PySide6.QtGui.QImage | PySide6.QtGui.QPixmap, /) -> None: ...
    def setPlotAreaBackgroundPen(self, pen: PySide6.QtGui.QPen | PySide6.QtCore.Qt.PenStyle | PySide6.QtGui.QColor, /) -> None: ...
    def setPlotAreaBackgroundVisible(self, /, visible: bool = ...) -> None: ...
    def setTheme(self, theme: PySide6.QtCharts.QChart.ChartTheme, /) -> None: ...
    def setTitle(self, title: str, /) -> None: ...
    def setTitleBrush(self, brush: PySide6.QtGui.QBrush | PySide6.QtCore.Qt.BrushStyle | PySide6.QtCore.Qt.GlobalColor | PySide6.QtGui.QColor | PySide6.QtGui.QGradient | PySide6.QtGui.QImage | PySide6.QtGui.QPixmap, /) -> None: ...
    def setTitleFont(self, font: PySide6.QtGui.QFont | str | collections.abc.Sequence[str], /) -> None: ...
    def theme(self, /) -> PySide6.QtCharts.QChart.ChartTheme: ...
    def title(self, /) -> str: ...
    def titleBrush(self, /) -> PySide6.QtGui.QBrush: ...
    def titleFont(self, /) -> PySide6.QtGui.QFont: ...
    def zoom(self, factor: float, /) -> None: ...
    @typing.overload
    def zoomIn(self, /) -> None: ...
    @typing.overload
    def zoomIn(self, rect: PySide6.QtCore.QRectF | PySide6.QtCore.QRect, /) -> None: ...
    def zoomOut(self, /) -> None: ...
    def zoomReset(self, /) -> None: ...


class QChartView(PySide6.QtWidgets.QGraphicsView):

    class RubberBand(enum.Flag):

        NoRubberBand              = ...  # 0x0
        VerticalRubberBand        = ...  # 0x1
        HorizontalRubberBand      = ...  # 0x2
        RectangleRubberBand       = ...  # 0x3
        ClickThroughRubberBand    = ...  # 0x80


    @typing.overload
    def __init__(self, chart: PySide6.QtCharts.QChart, /, parent: PySide6.QtWidgets.QWidget | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtWidgets.QWidget | None = ...) -> None: ...

    def chart(self, /) -> PySide6.QtCharts.QChart: ...
    def mouseMoveEvent(self, event: PySide6.QtGui.QMouseEvent, /) -> None: ...
    def mousePressEvent(self, event: PySide6.QtGui.QMouseEvent, /) -> None: ...
    def mouseReleaseEvent(self, event: PySide6.QtGui.QMouseEvent, /) -> None: ...
    def resizeEvent(self, event: PySide6.QtGui.QResizeEvent, /) -> None: ...
    def rubberBand(self, /) -> PySide6.QtCharts.QChartView.RubberBand: ...
    def setChart(self, chart: PySide6.QtCharts.QChart, /) -> None: ...
    def setRubberBand(self, rubberBands: PySide6.QtCharts.QChartView.RubberBand, /) -> None: ...


class QColorAxis(PySide6.QtCharts.QAbstractAxis):

    autoRangeChanged         : typing.ClassVar[Signal] = ... # autoRangeChanged(bool)
    gradientChanged          : typing.ClassVar[Signal] = ... # gradientChanged(QLinearGradient)
    maxChanged               : typing.ClassVar[Signal] = ... # maxChanged(double)
    minChanged               : typing.ClassVar[Signal] = ... # minChanged(double)
    rangeChanged             : typing.ClassVar[Signal] = ... # rangeChanged(double,double)
    sizeChanged              : typing.ClassVar[Signal] = ... # sizeChanged(double)
    tickCountChanged         : typing.ClassVar[Signal] = ... # tickCountChanged(int)

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, tickCount: int | None = ..., min: float | None = ..., max: float | None = ..., size: float | None = ..., autoRange: bool | None = ...) -> None: ...

    def autoRange(self, /) -> bool: ...
    def gradient(self, /) -> PySide6.QtGui.QLinearGradient: ...
    def max(self, /) -> float: ...
    def min(self, /) -> float: ...
    def setAutoRange(self, autoRange: bool, /) -> None: ...
    def setGradient(self, gradient: PySide6.QtGui.QLinearGradient, /) -> None: ...
    def setMax(self, max: float, /) -> None: ...
    def setMin(self, min: float, /) -> None: ...
    def setRange(self, min: float, max: float, /) -> None: ...
    def setSize(self, size: float, /) -> None: ...
    def setTickCount(self, count: int, /) -> None: ...
    def size(self, /) -> float: ...
    def tickCount(self, /) -> int: ...
    def type(self, /) -> PySide6.QtCharts.QAbstractAxis.AxisType: ...


class QDateTimeAxis(PySide6.QtCharts.QAbstractAxis):

    formatChanged            : typing.ClassVar[Signal] = ... # formatChanged(QString)
    maxChanged               : typing.ClassVar[Signal] = ... # maxChanged(QDateTime)
    minChanged               : typing.ClassVar[Signal] = ... # minChanged(QDateTime)
    rangeChanged             : typing.ClassVar[Signal] = ... # rangeChanged(QDateTime,QDateTime)
    tickCountChanged         : typing.ClassVar[Signal] = ... # tickCountChanged(int)

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, tickCount: int | None = ..., min: PySide6.QtCore.QDateTime | None = ..., max: PySide6.QtCore.QDateTime | None = ..., format: str | None = ...) -> None: ...

    def format(self, /) -> str: ...
    def max(self, /) -> PySide6.QtCore.QDateTime: ...
    def min(self, /) -> PySide6.QtCore.QDateTime: ...
    def setFormat(self, format: str, /) -> None: ...
    def setMax(self, max: PySide6.QtCore.QDateTime, /) -> None: ...
    def setMin(self, min: PySide6.QtCore.QDateTime, /) -> None: ...
    def setRange(self, min: PySide6.QtCore.QDateTime, max: PySide6.QtCore.QDateTime, /) -> None: ...
    def setTickCount(self, count: int, /) -> None: ...
    def tickCount(self, /) -> int: ...
    def type(self, /) -> PySide6.QtCharts.QAbstractAxis.AxisType: ...


class QHBarModelMapper(PySide6.QtCharts.QBarModelMapper):

    columnCountChanged       : typing.ClassVar[Signal] = ... # columnCountChanged()
    firstBarSetRowChanged    : typing.ClassVar[Signal] = ... # firstBarSetRowChanged()
    firstColumnChanged       : typing.ClassVar[Signal] = ... # firstColumnChanged()
    lastBarSetRowChanged     : typing.ClassVar[Signal] = ... # lastBarSetRowChanged()
    modelReplaced            : typing.ClassVar[Signal] = ... # modelReplaced()
    seriesReplaced           : typing.ClassVar[Signal] = ... # seriesReplaced()

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, series: PySide6.QtCharts.QAbstractBarSeries | None = ..., model: PySide6.QtCore.QAbstractItemModel | None = ..., firstBarSetRow: int | None = ..., lastBarSetRow: int | None = ..., firstColumn: int | None = ..., columnCount: int | None = ...) -> None: ...

    def columnCount(self, /) -> int: ...
    def firstBarSetRow(self, /) -> int: ...
    def firstColumn(self, /) -> int: ...
    def lastBarSetRow(self, /) -> int: ...
    def model(self, /) -> PySide6.QtCore.QAbstractItemModel: ...
    def series(self, /) -> PySide6.QtCharts.QAbstractBarSeries: ...
    def setColumnCount(self, columnCount: int, /) -> None: ...
    def setFirstBarSetRow(self, firstBarSetRow: int, /) -> None: ...
    def setFirstColumn(self, firstColumn: int, /) -> None: ...
    def setLastBarSetRow(self, lastBarSetRow: int, /) -> None: ...
    def setModel(self, model: PySide6.QtCore.QAbstractItemModel, /) -> None: ...
    def setSeries(self, series: PySide6.QtCharts.QAbstractBarSeries, /) -> None: ...


class QHBoxPlotModelMapper(PySide6.QtCharts.QBoxPlotModelMapper):

    columnCountChanged       : typing.ClassVar[Signal] = ... # columnCountChanged()
    firstBoxSetRowChanged    : typing.ClassVar[Signal] = ... # firstBoxSetRowChanged()
    firstColumnChanged       : typing.ClassVar[Signal] = ... # firstColumnChanged()
    lastBoxSetRowChanged     : typing.ClassVar[Signal] = ... # lastBoxSetRowChanged()
    modelReplaced            : typing.ClassVar[Signal] = ... # modelReplaced()
    seriesReplaced           : typing.ClassVar[Signal] = ... # seriesReplaced()

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, series: PySide6.QtCharts.QBoxPlotSeries | None = ..., model: PySide6.QtCore.QAbstractItemModel | None = ..., firstBoxSetRow: int | None = ..., lastBoxSetRow: int | None = ..., firstColumn: int | None = ..., columnCount: int | None = ...) -> None: ...

    def columnCount(self, /) -> int: ...
    def firstBoxSetRow(self, /) -> int: ...
    def firstColumn(self, /) -> int: ...
    def lastBoxSetRow(self, /) -> int: ...
    def model(self, /) -> PySide6.QtCore.QAbstractItemModel: ...
    def series(self, /) -> PySide6.QtCharts.QBoxPlotSeries: ...
    def setColumnCount(self, rowCount: int, /) -> None: ...
    def setFirstBoxSetRow(self, firstBoxSetRow: int, /) -> None: ...
    def setFirstColumn(self, firstColumn: int, /) -> None: ...
    def setLastBoxSetRow(self, lastBoxSetRow: int, /) -> None: ...
    def setModel(self, model: PySide6.QtCore.QAbstractItemModel, /) -> None: ...
    def setSeries(self, series: PySide6.QtCharts.QBoxPlotSeries, /) -> None: ...


class QHCandlestickModelMapper(PySide6.QtCharts.QCandlestickModelMapper):

    closeColumnChanged       : typing.ClassVar[Signal] = ... # closeColumnChanged()
    firstSetRowChanged       : typing.ClassVar[Signal] = ... # firstSetRowChanged()
    highColumnChanged        : typing.ClassVar[Signal] = ... # highColumnChanged()
    lastSetRowChanged        : typing.ClassVar[Signal] = ... # lastSetRowChanged()
    lowColumnChanged         : typing.ClassVar[Signal] = ... # lowColumnChanged()
    openColumnChanged        : typing.ClassVar[Signal] = ... # openColumnChanged()
    timestampColumnChanged   : typing.ClassVar[Signal] = ... # timestampColumnChanged()

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, timestampColumn: int | None = ..., openColumn: int | None = ..., highColumn: int | None = ..., lowColumn: int | None = ..., closeColumn: int | None = ..., firstSetRow: int | None = ..., lastSetRow: int | None = ...) -> None: ...

    def closeColumn(self, /) -> int: ...
    def firstSetRow(self, /) -> int: ...
    def highColumn(self, /) -> int: ...
    def lastSetRow(self, /) -> int: ...
    def lowColumn(self, /) -> int: ...
    def openColumn(self, /) -> int: ...
    def orientation(self, /) -> PySide6.QtCore.Qt.Orientation: ...
    def setCloseColumn(self, closeColumn: int, /) -> None: ...
    def setFirstSetRow(self, firstSetRow: int, /) -> None: ...
    def setHighColumn(self, highColumn: int, /) -> None: ...
    def setLastSetRow(self, lastSetRow: int, /) -> None: ...
    def setLowColumn(self, lowColumn: int, /) -> None: ...
    def setOpenColumn(self, openColumn: int, /) -> None: ...
    def setTimestampColumn(self, timestampColumn: int, /) -> None: ...
    def timestampColumn(self, /) -> int: ...


class QHPieModelMapper(PySide6.QtCharts.QPieModelMapper):

    columnCountChanged       : typing.ClassVar[Signal] = ... # columnCountChanged()
    firstColumnChanged       : typing.ClassVar[Signal] = ... # firstColumnChanged()
    labelsRowChanged         : typing.ClassVar[Signal] = ... # labelsRowChanged()
    modelReplaced            : typing.ClassVar[Signal] = ... # modelReplaced()
    seriesReplaced           : typing.ClassVar[Signal] = ... # seriesReplaced()
    valuesRowChanged         : typing.ClassVar[Signal] = ... # valuesRowChanged()

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, series: PySide6.QtCharts.QPieSeries | None = ..., model: PySide6.QtCore.QAbstractItemModel | None = ..., valuesRow: int | None = ..., labelsRow: int | None = ..., firstColumn: int | None = ..., columnCount: int | None = ...) -> None: ...

    def columnCount(self, /) -> int: ...
    def firstColumn(self, /) -> int: ...
    def labelsRow(self, /) -> int: ...
    def model(self, /) -> PySide6.QtCore.QAbstractItemModel: ...
    def series(self, /) -> PySide6.QtCharts.QPieSeries: ...
    def setColumnCount(self, columnCount: int, /) -> None: ...
    def setFirstColumn(self, firstColumn: int, /) -> None: ...
    def setLabelsRow(self, labelsRow: int, /) -> None: ...
    def setModel(self, model: PySide6.QtCore.QAbstractItemModel, /) -> None: ...
    def setSeries(self, series: PySide6.QtCharts.QPieSeries, /) -> None: ...
    def setValuesRow(self, valuesRow: int, /) -> None: ...
    def valuesRow(self, /) -> int: ...


class QHXYModelMapper(PySide6.QtCharts.QXYModelMapper):

    columnCountChanged       : typing.ClassVar[Signal] = ... # columnCountChanged()
    firstColumnChanged       : typing.ClassVar[Signal] = ... # firstColumnChanged()
    modelReplaced            : typing.ClassVar[Signal] = ... # modelReplaced()
    seriesReplaced           : typing.ClassVar[Signal] = ... # seriesReplaced()
    xRowChanged              : typing.ClassVar[Signal] = ... # xRowChanged()
    yRowChanged              : typing.ClassVar[Signal] = ... # yRowChanged()

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, series: PySide6.QtCharts.QXYSeries | None = ..., model: PySide6.QtCore.QAbstractItemModel | None = ..., xRow: int | None = ..., yRow: int | None = ..., firstColumn: int | None = ..., columnCount: int | None = ...) -> None: ...

    def columnCount(self, /) -> int: ...
    def firstColumn(self, /) -> int: ...
    def model(self, /) -> PySide6.QtCore.QAbstractItemModel: ...
    def series(self, /) -> PySide6.QtCharts.QXYSeries: ...
    def setColumnCount(self, columnCount: int, /) -> None: ...
    def setFirstColumn(self, firstColumn: int, /) -> None: ...
    def setModel(self, model: PySide6.QtCore.QAbstractItemModel, /) -> None: ...
    def setSeries(self, series: PySide6.QtCharts.QXYSeries, /) -> None: ...
    def setXRow(self, xRow: int, /) -> None: ...
    def setYRow(self, yRow: int, /) -> None: ...
    def xRow(self, /) -> int: ...
    def yRow(self, /) -> int: ...


class QHorizontalBarSeries(PySide6.QtCharts.QAbstractBarSeries):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def type(self, /) -> PySide6.QtCharts.QAbstractSeries.SeriesType: ...


class QHorizontalPercentBarSeries(PySide6.QtCharts.QAbstractBarSeries):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def type(self, /) -> PySide6.QtCharts.QAbstractSeries.SeriesType: ...


class QHorizontalStackedBarSeries(PySide6.QtCharts.QAbstractBarSeries):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def type(self, /) -> PySide6.QtCharts.QAbstractSeries.SeriesType: ...


class QIntList: ...


class QLegend(PySide6.QtWidgets.QGraphicsWidget):

    attachedToChartChanged   : typing.ClassVar[Signal] = ... # attachedToChartChanged(bool)
    backgroundVisibleChanged : typing.ClassVar[Signal] = ... # backgroundVisibleChanged(bool)
    borderColorChanged       : typing.ClassVar[Signal] = ... # borderColorChanged(QColor)
    colorChanged             : typing.ClassVar[Signal] = ... # colorChanged(QColor)
    fontChanged              : typing.ClassVar[Signal] = ... # fontChanged(QFont)
    interactiveChanged       : typing.ClassVar[Signal] = ... # interactiveChanged(bool)
    labelColorChanged        : typing.ClassVar[Signal] = ... # labelColorChanged(QColor)
    markerShapeChanged       : typing.ClassVar[Signal] = ... # markerShapeChanged(MarkerShape)
    reverseMarkersChanged    : typing.ClassVar[Signal] = ... # reverseMarkersChanged(bool)
    showToolTipsChanged      : typing.ClassVar[Signal] = ... # showToolTipsChanged(bool)

    class MarkerShape(enum.Enum):

        MarkerShapeDefault        = ...  # 0x0
        MarkerShapeRectangle      = ...  # 0x1
        MarkerShapeCircle         = ...  # 0x2
        MarkerShapeFromSeries     = ...  # 0x3
        MarkerShapeRotatedRectangle = ...  # 0x4
        MarkerShapeTriangle       = ...  # 0x5
        MarkerShapeStar           = ...  # 0x6
        MarkerShapePentagon       = ...  # 0x7


    def alignment(self, /) -> PySide6.QtCore.Qt.AlignmentFlag: ...
    def attachToChart(self, /) -> None: ...
    def borderColor(self, /) -> PySide6.QtGui.QColor: ...
    def brush(self, /) -> PySide6.QtGui.QBrush: ...
    def color(self, /) -> PySide6.QtGui.QColor: ...
    def detachFromChart(self, /) -> None: ...
    def font(self, /) -> PySide6.QtGui.QFont: ...
    def hideEvent(self, event: PySide6.QtGui.QHideEvent, /) -> None: ...
    def isAttachedToChart(self, /) -> bool: ...
    def isBackgroundVisible(self, /) -> bool: ...
    def isInteractive(self, /) -> bool: ...
    def labelBrush(self, /) -> PySide6.QtGui.QBrush: ...
    def labelColor(self, /) -> PySide6.QtGui.QColor: ...
    def markerShape(self, /) -> PySide6.QtCharts.QLegend.MarkerShape: ...
    def markers(self, /, series: PySide6.QtCharts.QAbstractSeries | None = ...) -> typing.List[PySide6.QtCharts.QLegendMarker]: ...
    def paint(self, painter: PySide6.QtGui.QPainter, option: PySide6.QtWidgets.QStyleOptionGraphicsItem, /, widget: PySide6.QtWidgets.QWidget | None = ...) -> None: ...
    def pen(self, /) -> PySide6.QtGui.QPen: ...
    def reverseMarkers(self, /) -> bool: ...
    def setAlignment(self, alignment: PySide6.QtCore.Qt.AlignmentFlag, /) -> None: ...
    def setBackgroundVisible(self, /, visible: bool = ...) -> None: ...
    def setBorderColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setBrush(self, brush: PySide6.QtGui.QBrush | PySide6.QtCore.Qt.BrushStyle | PySide6.QtCore.Qt.GlobalColor | PySide6.QtGui.QColor | PySide6.QtGui.QGradient | PySide6.QtGui.QImage | PySide6.QtGui.QPixmap, /) -> None: ...
    def setColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setFont(self, font: PySide6.QtGui.QFont | str | collections.abc.Sequence[str], /) -> None: ...
    def setInteractive(self, interactive: bool, /) -> None: ...
    def setLabelBrush(self, brush: PySide6.QtGui.QBrush | PySide6.QtCore.Qt.BrushStyle | PySide6.QtCore.Qt.GlobalColor | PySide6.QtGui.QColor | PySide6.QtGui.QGradient | PySide6.QtGui.QImage | PySide6.QtGui.QPixmap, /) -> None: ...
    def setLabelColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setMarkerShape(self, shape: PySide6.QtCharts.QLegend.MarkerShape, /) -> None: ...
    def setPen(self, pen: PySide6.QtGui.QPen | PySide6.QtCore.Qt.PenStyle | PySide6.QtGui.QColor, /) -> None: ...
    def setReverseMarkers(self, /, reverseMarkers: bool = ...) -> None: ...
    def setShowToolTips(self, show: bool, /) -> None: ...
    def showEvent(self, event: PySide6.QtGui.QShowEvent, /) -> None: ...
    def showToolTips(self, /) -> bool: ...


class QLegendMarker(PySide6.QtCore.QObject):

    brushChanged             : typing.ClassVar[Signal] = ... # brushChanged()
    clicked                  : typing.ClassVar[Signal] = ... # clicked()
    fontChanged              : typing.ClassVar[Signal] = ... # fontChanged()
    hovered                  : typing.ClassVar[Signal] = ... # hovered(bool)
    labelBrushChanged        : typing.ClassVar[Signal] = ... # labelBrushChanged()
    labelChanged             : typing.ClassVar[Signal] = ... # labelChanged()
    penChanged               : typing.ClassVar[Signal] = ... # penChanged()
    shapeChanged             : typing.ClassVar[Signal] = ... # shapeChanged()
    visibleChanged           : typing.ClassVar[Signal] = ... # visibleChanged()

    class LegendMarkerType(enum.Enum):

        LegendMarkerTypeArea      = ...  # 0x0
        LegendMarkerTypeBar       = ...  # 0x1
        LegendMarkerTypePie       = ...  # 0x2
        LegendMarkerTypeXY        = ...  # 0x3
        LegendMarkerTypeBoxPlot   = ...  # 0x4
        LegendMarkerTypeCandlestick = ...  # 0x5


    def brush(self, /) -> PySide6.QtGui.QBrush: ...
    def font(self, /) -> PySide6.QtGui.QFont: ...
    def isVisible(self, /) -> bool: ...
    def label(self, /) -> str: ...
    def labelBrush(self, /) -> PySide6.QtGui.QBrush: ...
    def pen(self, /) -> PySide6.QtGui.QPen: ...
    def series(self, /) -> PySide6.QtCharts.QAbstractSeries: ...
    def setBrush(self, brush: PySide6.QtGui.QBrush | PySide6.QtCore.Qt.BrushStyle | PySide6.QtCore.Qt.GlobalColor | PySide6.QtGui.QColor | PySide6.QtGui.QGradient | PySide6.QtGui.QImage | PySide6.QtGui.QPixmap, /) -> None: ...
    def setFont(self, font: PySide6.QtGui.QFont | str | collections.abc.Sequence[str], /) -> None: ...
    def setLabel(self, label: str, /) -> None: ...
    def setLabelBrush(self, brush: PySide6.QtGui.QBrush | PySide6.QtCore.Qt.BrushStyle | PySide6.QtCore.Qt.GlobalColor | PySide6.QtGui.QColor | PySide6.QtGui.QGradient | PySide6.QtGui.QImage | PySide6.QtGui.QPixmap, /) -> None: ...
    def setPen(self, pen: PySide6.QtGui.QPen | PySide6.QtCore.Qt.PenStyle | PySide6.QtGui.QColor, /) -> None: ...
    def setShape(self, shape: PySide6.QtCharts.QLegend.MarkerShape, /) -> None: ...
    def setVisible(self, visible: bool, /) -> None: ...
    def shape(self, /) -> PySide6.QtCharts.QLegend.MarkerShape: ...
    def type(self, /) -> PySide6.QtCharts.QLegendMarker.LegendMarkerType: ...


class QLineSeries(PySide6.QtCharts.QXYSeries):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def type(self, /) -> PySide6.QtCharts.QAbstractSeries.SeriesType: ...


class QLogValueAxis(PySide6.QtCharts.QAbstractAxis):

    baseChanged              : typing.ClassVar[Signal] = ... # baseChanged(double)
    labelFormatChanged       : typing.ClassVar[Signal] = ... # labelFormatChanged(QString)
    maxChanged               : typing.ClassVar[Signal] = ... # maxChanged(double)
    minChanged               : typing.ClassVar[Signal] = ... # minChanged(double)
    minorTickCountChanged    : typing.ClassVar[Signal] = ... # minorTickCountChanged(int)
    rangeChanged             : typing.ClassVar[Signal] = ... # rangeChanged(double,double)
    tickCountChanged         : typing.ClassVar[Signal] = ... # tickCountChanged(int)

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, min: float | None = ..., max: float | None = ..., labelFormat: str | None = ..., base: float | None = ..., tickCount: int | None = ..., minorTickCount: int | None = ...) -> None: ...

    def base(self, /) -> float: ...
    def labelFormat(self, /) -> str: ...
    def max(self, /) -> float: ...
    def min(self, /) -> float: ...
    def minorTickCount(self, /) -> int: ...
    def setBase(self, base: float, /) -> None: ...
    def setLabelFormat(self, format: str, /) -> None: ...
    def setMax(self, max: float, /) -> None: ...
    def setMin(self, min: float, /) -> None: ...
    def setMinorTickCount(self, minorTickCount: int, /) -> None: ...
    def setRange(self, min: float, max: float, /) -> None: ...
    def tickCount(self, /) -> int: ...
    def type(self, /) -> PySide6.QtCharts.QAbstractAxis.AxisType: ...


class QPercentBarSeries(PySide6.QtCharts.QAbstractBarSeries):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def type(self, /) -> PySide6.QtCharts.QAbstractSeries.SeriesType: ...


class QPieLegendMarker(PySide6.QtCharts.QLegendMarker):

    def __init__(self, series: PySide6.QtCharts.QPieSeries, slice: PySide6.QtCharts.QPieSlice, legend: PySide6.QtCharts.QLegend, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def series(self, /) -> PySide6.QtCharts.QPieSeries: ...
    def slice(self, /) -> PySide6.QtCharts.QPieSlice: ...
    def type(self, /) -> PySide6.QtCharts.QLegendMarker.LegendMarkerType: ...


class QPieModelMapper(PySide6.QtCore.QObject):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def count(self, /) -> int: ...
    def first(self, /) -> int: ...
    def labelsSection(self, /) -> int: ...
    def model(self, /) -> PySide6.QtCore.QAbstractItemModel: ...
    def orientation(self, /) -> PySide6.QtCore.Qt.Orientation: ...
    def series(self, /) -> PySide6.QtCharts.QPieSeries: ...
    def setCount(self, count: int, /) -> None: ...
    def setFirst(self, first: int, /) -> None: ...
    def setLabelsSection(self, labelsSection: int, /) -> None: ...
    def setModel(self, model: PySide6.QtCore.QAbstractItemModel, /) -> None: ...
    def setOrientation(self, orientation: PySide6.QtCore.Qt.Orientation, /) -> None: ...
    def setSeries(self, series: PySide6.QtCharts.QPieSeries, /) -> None: ...
    def setValuesSection(self, valuesSection: int, /) -> None: ...
    def valuesSection(self, /) -> int: ...


class QPieSeries(PySide6.QtCharts.QAbstractSeries):

    added                    : typing.ClassVar[Signal] = ... # added(QList<QPieSlice*>)
    clicked                  : typing.ClassVar[Signal] = ... # clicked(QPieSlice*)
    countChanged             : typing.ClassVar[Signal] = ... # countChanged()
    doubleClicked            : typing.ClassVar[Signal] = ... # doubleClicked(QPieSlice*)
    hovered                  : typing.ClassVar[Signal] = ... # hovered(QPieSlice*,bool)
    pressed                  : typing.ClassVar[Signal] = ... # pressed(QPieSlice*)
    released                 : typing.ClassVar[Signal] = ... # released(QPieSlice*)
    removed                  : typing.ClassVar[Signal] = ... # removed(QList<QPieSlice*>)
    sumChanged               : typing.ClassVar[Signal] = ... # sumChanged()

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, horizontalPosition: float | None = ..., verticalPosition: float | None = ..., size: float | None = ..., startAngle: float | None = ..., endAngle: float | None = ..., count: int | None = ..., sum: float | None = ..., holeSize: float | None = ...) -> None: ...

    def __lshift__(self, slice: PySide6.QtCharts.QPieSlice, /) -> PySide6.QtCharts.QPieSeries: ...
    @typing.overload
    def append(self, slice: PySide6.QtCharts.QPieSlice, /) -> bool: ...
    @typing.overload
    def append(self, label: str, value: float, /) -> PySide6.QtCharts.QPieSlice: ...
    @typing.overload
    def append(self, slices: collections.abc.Sequence[PySide6.QtCharts.QPieSlice], /) -> bool: ...
    def clear(self, /) -> None: ...
    def count(self, /) -> int: ...
    def holeSize(self, /) -> float: ...
    def horizontalPosition(self, /) -> float: ...
    def insert(self, index: int, slice: PySide6.QtCharts.QPieSlice, /) -> bool: ...
    def isEmpty(self, /) -> bool: ...
    def pieEndAngle(self, /) -> float: ...
    def pieSize(self, /) -> float: ...
    def pieStartAngle(self, /) -> float: ...
    def remove(self, slice: PySide6.QtCharts.QPieSlice, /) -> bool: ...
    def setHoleSize(self, holeSize: float, /) -> None: ...
    def setHorizontalPosition(self, relativePosition: float, /) -> None: ...
    def setLabelsPosition(self, position: PySide6.QtCharts.QPieSlice.LabelPosition, /) -> None: ...
    def setLabelsVisible(self, /, visible: bool = ...) -> None: ...
    def setPieEndAngle(self, endAngle: float, /) -> None: ...
    def setPieSize(self, relativeSize: float, /) -> None: ...
    def setPieStartAngle(self, startAngle: float, /) -> None: ...
    def setVerticalPosition(self, relativePosition: float, /) -> None: ...
    def slices(self, /) -> typing.List[PySide6.QtCharts.QPieSlice]: ...
    def sum(self, /) -> float: ...
    def take(self, slice: PySide6.QtCharts.QPieSlice, /) -> bool: ...
    def type(self, /) -> PySide6.QtCharts.QAbstractSeries.SeriesType: ...
    def verticalPosition(self, /) -> float: ...


class QPieSlice(PySide6.QtCore.QObject):

    angleSpanChanged         : typing.ClassVar[Signal] = ... # angleSpanChanged()
    borderColorChanged       : typing.ClassVar[Signal] = ... # borderColorChanged()
    borderWidthChanged       : typing.ClassVar[Signal] = ... # borderWidthChanged()
    brushChanged             : typing.ClassVar[Signal] = ... # brushChanged()
    clicked                  : typing.ClassVar[Signal] = ... # clicked()
    colorChanged             : typing.ClassVar[Signal] = ... # colorChanged()
    doubleClicked            : typing.ClassVar[Signal] = ... # doubleClicked()
    hovered                  : typing.ClassVar[Signal] = ... # hovered(bool)
    labelBrushChanged        : typing.ClassVar[Signal] = ... # labelBrushChanged()
    labelChanged             : typing.ClassVar[Signal] = ... # labelChanged()
    labelColorChanged        : typing.ClassVar[Signal] = ... # labelColorChanged()
    labelFontChanged         : typing.ClassVar[Signal] = ... # labelFontChanged()
    labelVisibleChanged      : typing.ClassVar[Signal] = ... # labelVisibleChanged()
    penChanged               : typing.ClassVar[Signal] = ... # penChanged()
    percentageChanged        : typing.ClassVar[Signal] = ... # percentageChanged()
    pressed                  : typing.ClassVar[Signal] = ... # pressed()
    released                 : typing.ClassVar[Signal] = ... # released()
    startAngleChanged        : typing.ClassVar[Signal] = ... # startAngleChanged()
    valueChanged             : typing.ClassVar[Signal] = ... # valueChanged()

    class LabelPosition(enum.Enum):

        LabelOutside              = ...  # 0x0
        LabelInsideHorizontal     = ...  # 0x1
        LabelInsideTangential     = ...  # 0x2
        LabelInsideNormal         = ...  # 0x3


    @typing.overload
    def __init__(self, label: str, value: float, /, parent: PySide6.QtCore.QObject | None = ..., *, labelVisible: bool | None = ..., labelPosition: PySide6.QtCharts.QPieSlice.LabelPosition | None = ..., exploded: bool | None = ..., pen: PySide6.QtGui.QPen | None = ..., borderColor: PySide6.QtGui.QColor | None = ..., borderWidth: int | None = ..., brush: PySide6.QtGui.QBrush | None = ..., color: PySide6.QtGui.QColor | None = ..., labelBrush: PySide6.QtGui.QBrush | None = ..., labelColor: PySide6.QtGui.QColor | None = ..., labelFont: PySide6.QtGui.QFont | None = ..., labelArmLengthFactor: float | None = ..., explodeDistanceFactor: float | None = ..., percentage: float | None = ..., startAngle: float | None = ..., angleSpan: float | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, label: str | None = ..., value: float | None = ..., labelVisible: bool | None = ..., labelPosition: PySide6.QtCharts.QPieSlice.LabelPosition | None = ..., exploded: bool | None = ..., pen: PySide6.QtGui.QPen | None = ..., borderColor: PySide6.QtGui.QColor | None = ..., borderWidth: int | None = ..., brush: PySide6.QtGui.QBrush | None = ..., color: PySide6.QtGui.QColor | None = ..., labelBrush: PySide6.QtGui.QBrush | None = ..., labelColor: PySide6.QtGui.QColor | None = ..., labelFont: PySide6.QtGui.QFont | None = ..., labelArmLengthFactor: float | None = ..., explodeDistanceFactor: float | None = ..., percentage: float | None = ..., startAngle: float | None = ..., angleSpan: float | None = ...) -> None: ...

    def angleSpan(self, /) -> float: ...
    def borderColor(self, /) -> PySide6.QtGui.QColor: ...
    def borderWidth(self, /) -> int: ...
    def brush(self, /) -> PySide6.QtGui.QBrush: ...
    def color(self, /) -> PySide6.QtGui.QColor: ...
    def explodeDistanceFactor(self, /) -> float: ...
    def isExploded(self, /) -> bool: ...
    def isLabelVisible(self, /) -> bool: ...
    def label(self, /) -> str: ...
    def labelArmLengthFactor(self, /) -> float: ...
    def labelBrush(self, /) -> PySide6.QtGui.QBrush: ...
    def labelColor(self, /) -> PySide6.QtGui.QColor: ...
    def labelFont(self, /) -> PySide6.QtGui.QFont: ...
    def labelPosition(self, /) -> PySide6.QtCharts.QPieSlice.LabelPosition: ...
    def pen(self, /) -> PySide6.QtGui.QPen: ...
    def percentage(self, /) -> float: ...
    def series(self, /) -> PySide6.QtCharts.QPieSeries: ...
    def setBorderColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setBorderWidth(self, width: int, /) -> None: ...
    def setBrush(self, brush: PySide6.QtGui.QBrush | PySide6.QtCore.Qt.BrushStyle | PySide6.QtCore.Qt.GlobalColor | PySide6.QtGui.QColor | PySide6.QtGui.QGradient | PySide6.QtGui.QImage | PySide6.QtGui.QPixmap, /) -> None: ...
    def setColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setExplodeDistanceFactor(self, factor: float, /) -> None: ...
    def setExploded(self, /, exploded: bool = ...) -> None: ...
    def setLabel(self, label: str, /) -> None: ...
    def setLabelArmLengthFactor(self, factor: float, /) -> None: ...
    def setLabelBrush(self, brush: PySide6.QtGui.QBrush | PySide6.QtCore.Qt.BrushStyle | PySide6.QtCore.Qt.GlobalColor | PySide6.QtGui.QColor | PySide6.QtGui.QGradient | PySide6.QtGui.QImage | PySide6.QtGui.QPixmap, /) -> None: ...
    def setLabelColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setLabelFont(self, font: PySide6.QtGui.QFont | str | collections.abc.Sequence[str], /) -> None: ...
    def setLabelPosition(self, position: PySide6.QtCharts.QPieSlice.LabelPosition, /) -> None: ...
    def setLabelVisible(self, /, visible: bool = ...) -> None: ...
    def setPen(self, pen: PySide6.QtGui.QPen | PySide6.QtCore.Qt.PenStyle | PySide6.QtGui.QColor, /) -> None: ...
    def setValue(self, value: float, /) -> None: ...
    def startAngle(self, /) -> float: ...
    def value(self, /) -> float: ...


class QPointFList: ...


class QPolarChart(PySide6.QtCharts.QChart):

    class PolarOrientation(enum.Flag):

        PolarOrientationRadial    = ...  # 0x1
        PolarOrientationAngular   = ...  # 0x2


    def __init__(self, /, parent: PySide6.QtWidgets.QGraphicsItem | None = ..., wFlags: PySide6.QtCore.Qt.WindowType = ...) -> None: ...

    def addAxis(self, axis: PySide6.QtCharts.QAbstractAxis, polarOrientation: PySide6.QtCharts.QPolarChart.PolarOrientation, /) -> None: ...
    @staticmethod
    def axisPolarOrientation(axis: PySide6.QtCharts.QAbstractAxis, /) -> PySide6.QtCharts.QPolarChart.PolarOrientation: ...


class QScatterSeries(PySide6.QtCharts.QXYSeries):

    borderColorChanged       : typing.ClassVar[Signal] = ... # borderColorChanged(QColor)
    colorChanged             : typing.ClassVar[Signal] = ... # colorChanged(QColor)
    markerShapeChanged       : typing.ClassVar[Signal] = ... # markerShapeChanged(MarkerShape)
    markerSizeChanged        : typing.ClassVar[Signal] = ... # markerSizeChanged(double)

    class MarkerShape(enum.Enum):

        MarkerShapeCircle         = ...  # 0x0
        MarkerShapeRectangle      = ...  # 0x1
        MarkerShapeRotatedRectangle = ...  # 0x2
        MarkerShapeTriangle       = ...  # 0x3
        MarkerShapeStar           = ...  # 0x4
        MarkerShapePentagon       = ...  # 0x5


    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, color: PySide6.QtGui.QColor | None = ..., borderColor: PySide6.QtGui.QColor | None = ..., markerShape: PySide6.QtCharts.QScatterSeries.MarkerShape | None = ..., markerSize: float | None = ..., brush: PySide6.QtGui.QBrush | None = ...) -> None: ...

    def borderColor(self, /) -> PySide6.QtGui.QColor: ...
    def brush(self, /) -> PySide6.QtGui.QBrush: ...
    def color(self, /) -> PySide6.QtGui.QColor: ...
    def markerShape(self, /) -> PySide6.QtCharts.QScatterSeries.MarkerShape: ...
    def markerSize(self, /) -> float: ...
    def setBorderColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setBrush(self, brush: PySide6.QtGui.QBrush | PySide6.QtCore.Qt.BrushStyle | PySide6.QtCore.Qt.GlobalColor | PySide6.QtGui.QColor | PySide6.QtGui.QGradient | PySide6.QtGui.QImage | PySide6.QtGui.QPixmap, /) -> None: ...
    def setColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setMarkerShape(self, shape: PySide6.QtCharts.QScatterSeries.MarkerShape, /) -> None: ...
    def setMarkerSize(self, size: float, /) -> None: ...
    def setPen(self, pen: PySide6.QtGui.QPen | PySide6.QtCore.Qt.PenStyle | PySide6.QtGui.QColor, /) -> None: ...
    def type(self, /) -> PySide6.QtCharts.QAbstractSeries.SeriesType: ...


class QSplineSeries(PySide6.QtCharts.QLineSeries):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def type(self, /) -> PySide6.QtCharts.QAbstractSeries.SeriesType: ...


class QStackedBarSeries(PySide6.QtCharts.QAbstractBarSeries):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def type(self, /) -> PySide6.QtCharts.QAbstractSeries.SeriesType: ...


class QVBarModelMapper(PySide6.QtCharts.QBarModelMapper):

    firstBarSetColumnChanged : typing.ClassVar[Signal] = ... # firstBarSetColumnChanged()
    firstRowChanged          : typing.ClassVar[Signal] = ... # firstRowChanged()
    lastBarSetColumnChanged  : typing.ClassVar[Signal] = ... # lastBarSetColumnChanged()
    modelReplaced            : typing.ClassVar[Signal] = ... # modelReplaced()
    rowCountChanged          : typing.ClassVar[Signal] = ... # rowCountChanged()
    seriesReplaced           : typing.ClassVar[Signal] = ... # seriesReplaced()

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, series: PySide6.QtCharts.QAbstractBarSeries | None = ..., model: PySide6.QtCore.QAbstractItemModel | None = ..., firstBarSetColumn: int | None = ..., lastBarSetColumn: int | None = ..., firstRow: int | None = ..., rowCount: int | None = ...) -> None: ...

    def firstBarSetColumn(self, /) -> int: ...
    def firstRow(self, /) -> int: ...
    def lastBarSetColumn(self, /) -> int: ...
    def model(self, /) -> PySide6.QtCore.QAbstractItemModel: ...
    def rowCount(self, /) -> int: ...
    def series(self, /) -> PySide6.QtCharts.QAbstractBarSeries: ...
    def setFirstBarSetColumn(self, firstBarSetColumn: int, /) -> None: ...
    def setFirstRow(self, firstRow: int, /) -> None: ...
    def setLastBarSetColumn(self, lastBarSetColumn: int, /) -> None: ...
    def setModel(self, model: PySide6.QtCore.QAbstractItemModel, /) -> None: ...
    def setRowCount(self, rowCount: int, /) -> None: ...
    def setSeries(self, series: PySide6.QtCharts.QAbstractBarSeries, /) -> None: ...


class QVBoxPlotModelMapper(PySide6.QtCharts.QBoxPlotModelMapper):

    firstBoxSetColumnChanged : typing.ClassVar[Signal] = ... # firstBoxSetColumnChanged()
    firstRowChanged          : typing.ClassVar[Signal] = ... # firstRowChanged()
    lastBoxSetColumnChanged  : typing.ClassVar[Signal] = ... # lastBoxSetColumnChanged()
    modelReplaced            : typing.ClassVar[Signal] = ... # modelReplaced()
    rowCountChanged          : typing.ClassVar[Signal] = ... # rowCountChanged()
    seriesReplaced           : typing.ClassVar[Signal] = ... # seriesReplaced()

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, series: PySide6.QtCharts.QBoxPlotSeries | None = ..., model: PySide6.QtCore.QAbstractItemModel | None = ..., firstBoxSetColumn: int | None = ..., lastBoxSetColumn: int | None = ..., firstRow: int | None = ..., rowCount: int | None = ...) -> None: ...

    def firstBoxSetColumn(self, /) -> int: ...
    def firstRow(self, /) -> int: ...
    def lastBoxSetColumn(self, /) -> int: ...
    def model(self, /) -> PySide6.QtCore.QAbstractItemModel: ...
    def rowCount(self, /) -> int: ...
    def series(self, /) -> PySide6.QtCharts.QBoxPlotSeries: ...
    def setFirstBoxSetColumn(self, firstBoxSetColumn: int, /) -> None: ...
    def setFirstRow(self, firstRow: int, /) -> None: ...
    def setLastBoxSetColumn(self, lastBoxSetColumn: int, /) -> None: ...
    def setModel(self, model: PySide6.QtCore.QAbstractItemModel, /) -> None: ...
    def setRowCount(self, rowCount: int, /) -> None: ...
    def setSeries(self, series: PySide6.QtCharts.QBoxPlotSeries, /) -> None: ...


class QVCandlestickModelMapper(PySide6.QtCharts.QCandlestickModelMapper):

    closeRowChanged          : typing.ClassVar[Signal] = ... # closeRowChanged()
    firstSetColumnChanged    : typing.ClassVar[Signal] = ... # firstSetColumnChanged()
    highRowChanged           : typing.ClassVar[Signal] = ... # highRowChanged()
    lastSetColumnChanged     : typing.ClassVar[Signal] = ... # lastSetColumnChanged()
    lowRowChanged            : typing.ClassVar[Signal] = ... # lowRowChanged()
    openRowChanged           : typing.ClassVar[Signal] = ... # openRowChanged()
    timestampRowChanged      : typing.ClassVar[Signal] = ... # timestampRowChanged()

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, timestampRow: int | None = ..., openRow: int | None = ..., highRow: int | None = ..., lowRow: int | None = ..., closeRow: int | None = ..., firstSetColumn: int | None = ..., lastSetColumn: int | None = ...) -> None: ...

    def closeRow(self, /) -> int: ...
    def firstSetColumn(self, /) -> int: ...
    def highRow(self, /) -> int: ...
    def lastSetColumn(self, /) -> int: ...
    def lowRow(self, /) -> int: ...
    def openRow(self, /) -> int: ...
    def orientation(self, /) -> PySide6.QtCore.Qt.Orientation: ...
    def setCloseRow(self, closeRow: int, /) -> None: ...
    def setFirstSetColumn(self, firstSetColumn: int, /) -> None: ...
    def setHighRow(self, highRow: int, /) -> None: ...
    def setLastSetColumn(self, lastSetColumn: int, /) -> None: ...
    def setLowRow(self, lowRow: int, /) -> None: ...
    def setOpenRow(self, openRow: int, /) -> None: ...
    def setTimestampRow(self, timestampRow: int, /) -> None: ...
    def timestampRow(self, /) -> int: ...


class QVPieModelMapper(PySide6.QtCharts.QPieModelMapper):

    firstRowChanged          : typing.ClassVar[Signal] = ... # firstRowChanged()
    labelsColumnChanged      : typing.ClassVar[Signal] = ... # labelsColumnChanged()
    modelReplaced            : typing.ClassVar[Signal] = ... # modelReplaced()
    rowCountChanged          : typing.ClassVar[Signal] = ... # rowCountChanged()
    seriesReplaced           : typing.ClassVar[Signal] = ... # seriesReplaced()
    valuesColumnChanged      : typing.ClassVar[Signal] = ... # valuesColumnChanged()

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, series: PySide6.QtCharts.QPieSeries | None = ..., model: PySide6.QtCore.QAbstractItemModel | None = ..., valuesColumn: int | None = ..., labelsColumn: int | None = ..., firstRow: int | None = ..., rowCount: int | None = ...) -> None: ...

    def firstRow(self, /) -> int: ...
    def labelsColumn(self, /) -> int: ...
    def model(self, /) -> PySide6.QtCore.QAbstractItemModel: ...
    def rowCount(self, /) -> int: ...
    def series(self, /) -> PySide6.QtCharts.QPieSeries: ...
    def setFirstRow(self, firstRow: int, /) -> None: ...
    def setLabelsColumn(self, labelsColumn: int, /) -> None: ...
    def setModel(self, model: PySide6.QtCore.QAbstractItemModel, /) -> None: ...
    def setRowCount(self, rowCount: int, /) -> None: ...
    def setSeries(self, series: PySide6.QtCharts.QPieSeries, /) -> None: ...
    def setValuesColumn(self, valuesColumn: int, /) -> None: ...
    def valuesColumn(self, /) -> int: ...


class QVXYModelMapper(PySide6.QtCharts.QXYModelMapper):

    firstRowChanged          : typing.ClassVar[Signal] = ... # firstRowChanged()
    modelReplaced            : typing.ClassVar[Signal] = ... # modelReplaced()
    rowCountChanged          : typing.ClassVar[Signal] = ... # rowCountChanged()
    seriesReplaced           : typing.ClassVar[Signal] = ... # seriesReplaced()
    xColumnChanged           : typing.ClassVar[Signal] = ... # xColumnChanged()
    yColumnChanged           : typing.ClassVar[Signal] = ... # yColumnChanged()

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, series: PySide6.QtCharts.QXYSeries | None = ..., model: PySide6.QtCore.QAbstractItemModel | None = ..., xColumn: int | None = ..., yColumn: int | None = ..., firstRow: int | None = ..., rowCount: int | None = ...) -> None: ...

    def firstRow(self, /) -> int: ...
    def model(self, /) -> PySide6.QtCore.QAbstractItemModel: ...
    def rowCount(self, /) -> int: ...
    def series(self, /) -> PySide6.QtCharts.QXYSeries: ...
    def setFirstRow(self, firstRow: int, /) -> None: ...
    def setModel(self, model: PySide6.QtCore.QAbstractItemModel, /) -> None: ...
    def setRowCount(self, rowCount: int, /) -> None: ...
    def setSeries(self, series: PySide6.QtCharts.QXYSeries, /) -> None: ...
    def setXColumn(self, xColumn: int, /) -> None: ...
    def setYColumn(self, yColumn: int, /) -> None: ...
    def xColumn(self, /) -> int: ...
    def yColumn(self, /) -> int: ...


class QValueAxis(PySide6.QtCharts.QAbstractAxis):

    labelFormatChanged       : typing.ClassVar[Signal] = ... # labelFormatChanged(QString)
    maxChanged               : typing.ClassVar[Signal] = ... # maxChanged(double)
    minChanged               : typing.ClassVar[Signal] = ... # minChanged(double)
    minorTickCountChanged    : typing.ClassVar[Signal] = ... # minorTickCountChanged(int)
    rangeChanged             : typing.ClassVar[Signal] = ... # rangeChanged(double,double)
    tickAnchorChanged        : typing.ClassVar[Signal] = ... # tickAnchorChanged(double)
    tickCountChanged         : typing.ClassVar[Signal] = ... # tickCountChanged(int)
    tickIntervalChanged      : typing.ClassVar[Signal] = ... # tickIntervalChanged(double)
    tickTypeChanged          : typing.ClassVar[Signal] = ... # tickTypeChanged(QValueAxis::TickType)

    class TickType(enum.Enum):

        TicksDynamic              = ...  # 0x0
        TicksFixed                = ...  # 0x1


    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, tickCount: int | None = ..., min: float | None = ..., max: float | None = ..., labelFormat: str | None = ..., minorTickCount: int | None = ..., tickAnchor: float | None = ..., tickInterval: float | None = ..., tickType: PySide6.QtCharts.QValueAxis.TickType | None = ...) -> None: ...

    def applyNiceNumbers(self, /) -> None: ...
    def labelFormat(self, /) -> str: ...
    def max(self, /) -> float: ...
    def min(self, /) -> float: ...
    def minorTickCount(self, /) -> int: ...
    def setLabelFormat(self, format: str, /) -> None: ...
    def setMax(self, max: float, /) -> None: ...
    def setMin(self, min: float, /) -> None: ...
    def setMinorTickCount(self, count: int, /) -> None: ...
    def setRange(self, min: float, max: float, /) -> None: ...
    def setTickAnchor(self, anchor: float, /) -> None: ...
    def setTickCount(self, count: int, /) -> None: ...
    def setTickInterval(self, insterval: float, /) -> None: ...
    def setTickType(self, type: PySide6.QtCharts.QValueAxis.TickType, /) -> None: ...
    def tickAnchor(self, /) -> float: ...
    def tickCount(self, /) -> int: ...
    def tickInterval(self, /) -> float: ...
    def tickType(self, /) -> PySide6.QtCharts.QValueAxis.TickType: ...
    def type(self, /) -> PySide6.QtCharts.QAbstractAxis.AxisType: ...


class QXYLegendMarker(PySide6.QtCharts.QLegendMarker):

    def __init__(self, series: PySide6.QtCharts.QXYSeries, legend: PySide6.QtCharts.QLegend, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def series(self, /) -> PySide6.QtCharts.QXYSeries: ...
    def type(self, /) -> PySide6.QtCharts.QLegendMarker.LegendMarkerType: ...


class QXYModelMapper(PySide6.QtCore.QObject):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def count(self, /) -> int: ...
    def first(self, /) -> int: ...
    def model(self, /) -> PySide6.QtCore.QAbstractItemModel: ...
    def orientation(self, /) -> PySide6.QtCore.Qt.Orientation: ...
    def series(self, /) -> PySide6.QtCharts.QXYSeries: ...
    def setCount(self, count: int, /) -> None: ...
    def setFirst(self, first: int, /) -> None: ...
    def setModel(self, model: PySide6.QtCore.QAbstractItemModel, /) -> None: ...
    def setOrientation(self, orientation: PySide6.QtCore.Qt.Orientation, /) -> None: ...
    def setSeries(self, series: PySide6.QtCharts.QXYSeries, /) -> None: ...
    def setXSection(self, xSection: int, /) -> None: ...
    def setYSection(self, ySection: int, /) -> None: ...
    def xSection(self, /) -> int: ...
    def ySection(self, /) -> int: ...


class QXYSeries(PySide6.QtCharts.QAbstractSeries):

    bestFitLineColorChanged  : typing.ClassVar[Signal] = ... # bestFitLineColorChanged(QColor)
    bestFitLinePenChanged    : typing.ClassVar[Signal] = ... # bestFitLinePenChanged(QPen)
    bestFitLineVisibilityChanged: typing.ClassVar[Signal] = ... # bestFitLineVisibilityChanged(bool)
    clicked                  : typing.ClassVar[Signal] = ... # clicked(QPointF)
    colorChanged             : typing.ClassVar[Signal] = ... # colorChanged(QColor)
    doubleClicked            : typing.ClassVar[Signal] = ... # doubleClicked(QPointF)
    hovered                  : typing.ClassVar[Signal] = ... # hovered(QPointF,bool)
    lightMarkerChanged       : typing.ClassVar[Signal] = ... # lightMarkerChanged(QImage)
    markerSizeChanged        : typing.ClassVar[Signal] = ... # markerSizeChanged(double)
    penChanged               : typing.ClassVar[Signal] = ... # penChanged(QPen)
    pointAdded               : typing.ClassVar[Signal] = ... # pointAdded(int)
    pointLabelsClippingChanged: typing.ClassVar[Signal] = ... # pointLabelsClippingChanged(bool)
    pointLabelsColorChanged  : typing.ClassVar[Signal] = ... # pointLabelsColorChanged(QColor)
    pointLabelsFontChanged   : typing.ClassVar[Signal] = ... # pointLabelsFontChanged(QFont)
    pointLabelsFormatChanged : typing.ClassVar[Signal] = ... # pointLabelsFormatChanged(QString)
    pointLabelsVisibilityChanged: typing.ClassVar[Signal] = ... # pointLabelsVisibilityChanged(bool)
    pointRemoved             : typing.ClassVar[Signal] = ... # pointRemoved(int)
    pointReplaced            : typing.ClassVar[Signal] = ... # pointReplaced(int)
    pointsConfigurationChanged: typing.ClassVar[Signal] = ... # pointsConfigurationChanged(QXYSeries::PointsConfigurationHash)
    pointsRemoved            : typing.ClassVar[Signal] = ... # pointsRemoved(int,int)
    pointsReplaced           : typing.ClassVar[Signal] = ... # pointsReplaced()
    pressed                  : typing.ClassVar[Signal] = ... # pressed(QPointF)
    released                 : typing.ClassVar[Signal] = ... # released(QPointF)
    selectedColorChanged     : typing.ClassVar[Signal] = ... # selectedColorChanged(QColor)
    selectedLightMarkerChanged: typing.ClassVar[Signal] = ... # selectedLightMarkerChanged(QImage)
    selectedPointsChanged    : typing.ClassVar[Signal] = ... # selectedPointsChanged()

    class PointConfiguration(enum.Enum):

        Color                     = ...  # 0x0
        Size                      = ...  # 0x1
        Visibility                = ...  # 0x2
        LabelVisibility           = ...  # 0x3
        LabelFormat               = ...  # 0x4


    @typing.overload
    def __lshift__(self, points: collections.abc.Sequence[PySide6.QtCore.QPointF], /) -> PySide6.QtCharts.QXYSeries: ...
    @typing.overload
    def __lshift__(self, point: PySide6.QtCore.QPointF | PySide6.QtCore.QPoint | PySide6.QtGui.QPainterPath.Element, /) -> PySide6.QtCharts.QXYSeries: ...
    @typing.overload
    def append(self, points: collections.abc.Sequence[PySide6.QtCore.QPointF], /) -> None: ...
    @typing.overload
    def append(self, point: PySide6.QtCore.QPointF | PySide6.QtCore.QPoint | PySide6.QtGui.QPainterPath.Element, /) -> None: ...
    @typing.overload
    def append(self, x: float, y: float, /) -> None: ...
    def appendNp(self, x: collections.abc.Sequence[typing.Any], y: collections.abc.Sequence[typing.Any], /) -> None: ...
    def at(self, index: int, /) -> PySide6.QtCore.QPointF: ...
    def bestFitLineColor(self, /) -> PySide6.QtGui.QColor: ...
    def bestFitLineEquation(self, ok: bool, /) -> typing.Tuple[float, float]: ...
    def bestFitLinePen(self, /) -> PySide6.QtGui.QPen: ...
    def bestFitLineVisible(self, /) -> bool: ...
    def brush(self, /) -> PySide6.QtGui.QBrush: ...
    def clear(self, /) -> None: ...
    @typing.overload
    def clearPointConfiguration(self, index: int, /) -> None: ...
    @typing.overload
    def clearPointConfiguration(self, index: int, key: PySide6.QtCharts.QXYSeries.PointConfiguration, /) -> None: ...
    @typing.overload
    def clearPointsConfiguration(self, /) -> None: ...
    @typing.overload
    def clearPointsConfiguration(self, key: PySide6.QtCharts.QXYSeries.PointConfiguration, /) -> None: ...
    def color(self, /) -> PySide6.QtGui.QColor: ...
    def colorBy(self, sourceData: collections.abc.Sequence[float], /, gradient: PySide6.QtGui.QLinearGradient = ...) -> None: ...
    def count(self, /) -> int: ...
    def deselectAllPoints(self, /) -> None: ...
    def deselectPoint(self, index: int, /) -> None: ...
    def deselectPoints(self, indexes: collections.abc.Sequence[int], /) -> None: ...
    def insert(self, index: int, point: PySide6.QtCore.QPointF | PySide6.QtCore.QPoint | PySide6.QtGui.QPainterPath.Element, /) -> None: ...
    def isPointSelected(self, index: int, /) -> bool: ...
    def lightMarker(self, /) -> PySide6.QtGui.QImage: ...
    def markerSize(self, /) -> float: ...
    def pen(self, /) -> PySide6.QtGui.QPen: ...
    def pointConfiguration(self, index: int, /) -> typing.Dict[PySide6.QtCharts.QXYSeries.PointConfiguration, typing.Any]: ...
    def pointLabelsClipping(self, /) -> bool: ...
    def pointLabelsColor(self, /) -> PySide6.QtGui.QColor: ...
    def pointLabelsFont(self, /) -> PySide6.QtGui.QFont: ...
    def pointLabelsFormat(self, /) -> str: ...
    def pointLabelsVisible(self, /) -> bool: ...
    def points(self, /) -> typing.List[PySide6.QtCore.QPointF]: ...
    def pointsConfiguration(self, /) -> typing.Dict[int, typing.Dict[PySide6.QtCharts.QXYSeries.PointConfiguration, typing.Any]]: ...
    def pointsVector(self, /) -> typing.List[PySide6.QtCore.QPointF]: ...
    def pointsVisible(self, /) -> bool: ...
    @typing.overload
    def remove(self, point: PySide6.QtCore.QPointF | PySide6.QtCore.QPoint | PySide6.QtGui.QPainterPath.Element, /) -> None: ...
    @typing.overload
    def remove(self, index: int, /) -> None: ...
    @typing.overload
    def remove(self, x: float, y: float, /) -> None: ...
    def removePoints(self, index: int, count: int, /) -> None: ...
    @typing.overload
    def replace(self, points: collections.abc.Sequence[PySide6.QtCore.QPointF], /) -> None: ...
    @typing.overload
    def replace(self, oldPoint: PySide6.QtCore.QPointF | PySide6.QtCore.QPoint | PySide6.QtGui.QPainterPath.Element, newPoint: PySide6.QtCore.QPointF | PySide6.QtCore.QPoint | PySide6.QtGui.QPainterPath.Element, /) -> None: ...
    @typing.overload
    def replace(self, index: int, newPoint: PySide6.QtCore.QPointF | PySide6.QtCore.QPoint | PySide6.QtGui.QPainterPath.Element, /) -> None: ...
    @typing.overload
    def replace(self, index: int, newX: float, newY: float, /) -> None: ...
    @typing.overload
    def replace(self, oldX: float, oldY: float, newX: float, newY: float, /) -> None: ...
    def replaceNp(self, x: collections.abc.Sequence[typing.Any], y: collections.abc.Sequence[typing.Any], /) -> None: ...
    def selectAllPoints(self, /) -> None: ...
    def selectPoint(self, index: int, /) -> None: ...
    def selectPoints(self, indexes: collections.abc.Sequence[int], /) -> None: ...
    def selectedColor(self, /) -> PySide6.QtGui.QColor: ...
    def selectedLightMarker(self, /) -> PySide6.QtGui.QImage: ...
    def selectedPoints(self, /) -> typing.List[int]: ...
    def setBestFitLineColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setBestFitLinePen(self, pen: PySide6.QtGui.QPen | PySide6.QtCore.Qt.PenStyle | PySide6.QtGui.QColor, /) -> None: ...
    def setBestFitLineVisible(self, /, visible: bool = ...) -> None: ...
    def setBrush(self, brush: PySide6.QtGui.QBrush | PySide6.QtCore.Qt.BrushStyle | PySide6.QtCore.Qt.GlobalColor | PySide6.QtGui.QColor | PySide6.QtGui.QGradient | PySide6.QtGui.QImage | PySide6.QtGui.QPixmap, /) -> None: ...
    def setColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setLightMarker(self, lightMarker: PySide6.QtGui.QImage, /) -> None: ...
    def setMarkerSize(self, size: float, /) -> None: ...
    def setPen(self, pen: PySide6.QtGui.QPen | PySide6.QtCore.Qt.PenStyle | PySide6.QtGui.QColor, /) -> None: ...
    @typing.overload
    def setPointConfiguration(self, index: int, key: PySide6.QtCharts.QXYSeries.PointConfiguration, value: typing.Any, /) -> None: ...
    @typing.overload
    def setPointConfiguration(self, index: int, configuration: typing.Dict[PySide6.QtCharts.QXYSeries.PointConfiguration, typing.Any], /) -> None: ...
    def setPointLabelsClipping(self, /, enabled: bool = ...) -> None: ...
    def setPointLabelsColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setPointLabelsFont(self, font: PySide6.QtGui.QFont | str | collections.abc.Sequence[str], /) -> None: ...
    def setPointLabelsFormat(self, format: str, /) -> None: ...
    def setPointLabelsVisible(self, /, visible: bool = ...) -> None: ...
    def setPointSelected(self, index: int, selected: bool, /) -> None: ...
    def setPointsConfiguration(self, pointsConfiguration: typing.Dict[int, typing.Dict[PySide6.QtCharts.QXYSeries.PointConfiguration, typing.Any]], /) -> None: ...
    def setPointsVisible(self, /, visible: bool = ...) -> None: ...
    def setSelectedColor(self, color: typing.Union[PySide6.QtGui.QColor, str, PySide6.QtGui.QRgba64, typing.Any, PySide6.QtCore.Qt.GlobalColor, int], /) -> None: ...
    def setSelectedLightMarker(self, selectedLightMarker: PySide6.QtGui.QImage, /) -> None: ...
    def sizeBy(self, sourceData: collections.abc.Sequence[float], minSize: float, maxSize: float, /) -> None: ...
    def toggleSelection(self, indexes: collections.abc.Sequence[int], /) -> None: ...


# eof
