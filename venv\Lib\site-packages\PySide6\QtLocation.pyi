# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations
"""
This file contains the exact signatures for all functions in module
PySide6.QtLocation, except for defaults which are replaced by "...".

# mypy: disable-error-code="override, overload-overlap"
"""

# Module `PySide6.QtLocation`

import PySide6.QtLocation
import PySide6.QtCore
import PySide6.QtPositioning

import os
import enum
import typing
import collections.abc
from PySide6.QtCore import Signal
from shiboken6 import Shiboken


class QGeoCodeReply(PySide6.QtCore.QObject):

    aborted                  : typing.ClassVar[Signal] = ... # aborted()
    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(QGeoCodeReply::Error,QString); errorOccurred(QGeoCodeReply::Error)
    finished                 : typing.ClassVar[Signal] = ... # finished()

    class Error(enum.Enum):

        NoError                   = ...  # 0x0
        EngineNotSetError         = ...  # 0x1
        CommunicationError        = ...  # 0x2
        ParseError                = ...  # 0x3
        UnsupportedOptionError    = ...  # 0x4
        CombinationError          = ...  # 0x5
        UnknownError              = ...  # 0x6


    @typing.overload
    def __init__(self, error: PySide6.QtLocation.QGeoCodeReply.Error, errorString: str, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def abort(self, /) -> None: ...
    def addLocation(self, location: PySide6.QtPositioning.QGeoLocation, /) -> None: ...
    def error(self, /) -> PySide6.QtLocation.QGeoCodeReply.Error: ...
    def errorString(self, /) -> str: ...
    def isFinished(self, /) -> bool: ...
    def limit(self, /) -> int: ...
    def locations(self, /) -> typing.List[PySide6.QtPositioning.QGeoLocation]: ...
    def offset(self, /) -> int: ...
    def setError(self, error: PySide6.QtLocation.QGeoCodeReply.Error, errorString: str, /) -> None: ...
    def setFinished(self, finished: bool, /) -> None: ...
    def setLimit(self, limit: int, /) -> None: ...
    def setLocations(self, locations: collections.abc.Sequence[PySide6.QtPositioning.QGeoLocation], /) -> None: ...
    def setOffset(self, offset: int, /) -> None: ...
    def setViewport(self, viewport: PySide6.QtPositioning.QGeoShape, /) -> None: ...
    def viewport(self, /) -> PySide6.QtPositioning.QGeoShape: ...


class QGeoCodingManager(PySide6.QtCore.QObject):

    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(QGeoCodeReply*,QGeoCodeReply::Error,QString); errorOccurred(QGeoCodeReply*,QGeoCodeReply::Error)
    finished                 : typing.ClassVar[Signal] = ... # finished(QGeoCodeReply*)
    @typing.overload
    def geocode(self, address: PySide6.QtPositioning.QGeoAddress, /, bounds: PySide6.QtPositioning.QGeoShape = ...) -> PySide6.QtLocation.QGeoCodeReply: ...
    @typing.overload
    def geocode(self, searchString: str, /, limit: int = ..., offset: int | None = ..., bounds: PySide6.QtPositioning.QGeoShape = ...) -> PySide6.QtLocation.QGeoCodeReply: ...
    def locale(self, /) -> PySide6.QtCore.QLocale: ...
    def managerName(self, /) -> str: ...
    def managerVersion(self, /) -> int: ...
    def reverseGeocode(self, coordinate: PySide6.QtPositioning.QGeoCoordinate, /, bounds: PySide6.QtPositioning.QGeoShape = ...) -> PySide6.QtLocation.QGeoCodeReply: ...
    def setLocale(self, locale: PySide6.QtCore.QLocale | PySide6.QtCore.QLocale.Language, /) -> None: ...


class QGeoCodingManagerEngine(PySide6.QtCore.QObject):

    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(QGeoCodeReply*,QGeoCodeReply::Error,QString); errorOccurred(QGeoCodeReply*,QGeoCodeReply::Error)
    finished                 : typing.ClassVar[Signal] = ... # finished(QGeoCodeReply*)

    def __init__(self, parameters: typing.Dict[str, typing.Any], /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    @typing.overload
    def geocode(self, address: PySide6.QtPositioning.QGeoAddress, bounds: PySide6.QtPositioning.QGeoShape, /) -> PySide6.QtLocation.QGeoCodeReply: ...
    @typing.overload
    def geocode(self, address: str, limit: int, offset: int, bounds: PySide6.QtPositioning.QGeoShape, /) -> PySide6.QtLocation.QGeoCodeReply: ...
    def locale(self, /) -> PySide6.QtCore.QLocale: ...
    def managerName(self, /) -> str: ...
    def managerVersion(self, /) -> int: ...
    def reverseGeocode(self, coordinate: PySide6.QtPositioning.QGeoCoordinate, bounds: PySide6.QtPositioning.QGeoShape, /) -> PySide6.QtLocation.QGeoCodeReply: ...
    def setLocale(self, locale: PySide6.QtCore.QLocale | PySide6.QtCore.QLocale.Language, /) -> None: ...


class QGeoManeuver(Shiboken.Object):

    class InstructionDirection(enum.Enum):

        NoDirection               = ...  # 0x0
        DirectionForward          = ...  # 0x1
        DirectionBearRight        = ...  # 0x2
        DirectionLightRight       = ...  # 0x3
        DirectionRight            = ...  # 0x4
        DirectionHardRight        = ...  # 0x5
        DirectionUTurnRight       = ...  # 0x6
        DirectionUTurnLeft        = ...  # 0x7
        DirectionHardLeft         = ...  # 0x8
        DirectionLeft             = ...  # 0x9
        DirectionLightLeft        = ...  # 0xa
        DirectionBearLeft         = ...  # 0xb


    @typing.overload
    def __init__(self, other: PySide6.QtLocation.QGeoManeuver, /, *, valid: bool | None = ..., position: PySide6.QtPositioning.QGeoCoordinate | None = ..., instructionText: str | None = ..., direction: PySide6.QtLocation.QGeoManeuver.InstructionDirection | None = ..., timeToNextInstruction: int | None = ..., distanceToNextInstruction: float | None = ..., waypoint: PySide6.QtPositioning.QGeoCoordinate | None = ..., extendedAttributes: typing.Optional[typing.Dict[str, typing.Any]] = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, valid: bool | None = ..., position: PySide6.QtPositioning.QGeoCoordinate | None = ..., instructionText: str | None = ..., direction: PySide6.QtLocation.QGeoManeuver.InstructionDirection | None = ..., timeToNextInstruction: int | None = ..., distanceToNextInstruction: float | None = ..., waypoint: PySide6.QtPositioning.QGeoCoordinate | None = ..., extendedAttributes: typing.Optional[typing.Dict[str, typing.Any]] = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtLocation.QGeoManeuver, /) -> bool: ...
    def __ne__(self, rhs: PySide6.QtLocation.QGeoManeuver, /) -> bool: ...
    def direction(self, /) -> PySide6.QtLocation.QGeoManeuver.InstructionDirection: ...
    def distanceToNextInstruction(self, /) -> float: ...
    def extendedAttributes(self, /) -> typing.Dict[str, typing.Any]: ...
    def instructionText(self, /) -> str: ...
    def isValid(self, /) -> bool: ...
    def position(self, /) -> PySide6.QtPositioning.QGeoCoordinate: ...
    def setDirection(self, direction: PySide6.QtLocation.QGeoManeuver.InstructionDirection, /) -> None: ...
    def setDistanceToNextInstruction(self, distance: float, /) -> None: ...
    def setExtendedAttributes(self, extendedAttributes: typing.Dict[str, typing.Any], /) -> None: ...
    def setInstructionText(self, instructionText: str, /) -> None: ...
    def setPosition(self, position: PySide6.QtPositioning.QGeoCoordinate, /) -> None: ...
    def setTimeToNextInstruction(self, secs: int, /) -> None: ...
    def setWaypoint(self, coordinate: PySide6.QtPositioning.QGeoCoordinate, /) -> None: ...
    def swap(self, other: PySide6.QtLocation.QGeoManeuver, /) -> None: ...
    def timeToNextInstruction(self, /) -> int: ...
    def waypoint(self, /) -> PySide6.QtPositioning.QGeoCoordinate: ...


class QGeoRoute(Shiboken.Object):

    @typing.overload
    def __init__(self, other: PySide6.QtLocation.QGeoRoute, /, *, routeId: str | None = ..., bounds: PySide6.QtPositioning.QGeoRectangle | None = ..., travelTime: int | None = ..., distance: float | None = ..., path: collections.abc.Sequence[PySide6.QtPositioning.QGeoCoordinate] | None = ..., routeLegs: collections.abc.Sequence[PySide6.QtLocation.QGeoRoute] | None = ..., extendedAttributes: typing.Optional[typing.Dict[str, typing.Any]] = ..., legIndex: int | None = ..., overallRoute: PySide6.QtLocation.QGeoRoute | None = ..., segmentsCount: int | None = ..., segments: collections.abc.Sequence[PySide6.QtLocation.QGeoRouteSegment] | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, routeId: str | None = ..., bounds: PySide6.QtPositioning.QGeoRectangle | None = ..., travelTime: int | None = ..., distance: float | None = ..., path: collections.abc.Sequence[PySide6.QtPositioning.QGeoCoordinate] | None = ..., routeLegs: collections.abc.Sequence[PySide6.QtLocation.QGeoRoute] | None = ..., extendedAttributes: typing.Optional[typing.Dict[str, typing.Any]] = ..., legIndex: int | None = ..., overallRoute: PySide6.QtLocation.QGeoRoute | None = ..., segmentsCount: int | None = ..., segments: collections.abc.Sequence[PySide6.QtLocation.QGeoRouteSegment] | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtLocation.QGeoRoute, /) -> bool: ...
    def __ne__(self, rhs: PySide6.QtLocation.QGeoRoute, /) -> bool: ...
    def bounds(self, /) -> PySide6.QtPositioning.QGeoRectangle: ...
    def distance(self, /) -> float: ...
    def extendedAttributes(self, /) -> typing.Dict[str, typing.Any]: ...
    def firstRouteSegment(self, /) -> PySide6.QtLocation.QGeoRouteSegment: ...
    def legIndex(self, /) -> int: ...
    def overallRoute(self, /) -> PySide6.QtLocation.QGeoRoute: ...
    def path(self, /) -> typing.List[PySide6.QtPositioning.QGeoCoordinate]: ...
    def request(self, /) -> PySide6.QtLocation.QGeoRouteRequest: ...
    def routeId(self, /) -> str: ...
    def routeLegs(self, /) -> typing.List[PySide6.QtLocation.QGeoRoute]: ...
    def segments(self, /) -> typing.List[PySide6.QtLocation.QGeoRouteSegment]: ...
    def segmentsCount(self, /) -> int: ...
    def setBounds(self, bounds: PySide6.QtPositioning.QGeoRectangle | PySide6.QtPositioning.QGeoShape | collections.abc.Sequence[PySide6.QtPositioning.QGeoCoordinate], /) -> None: ...
    def setDistance(self, distance: float, /) -> None: ...
    def setExtendedAttributes(self, extendedAttributes: typing.Dict[str, typing.Any], /) -> None: ...
    def setFirstRouteSegment(self, routeSegment: PySide6.QtLocation.QGeoRouteSegment, /) -> None: ...
    def setLegIndex(self, idx: int, /) -> None: ...
    def setOverallRoute(self, route: PySide6.QtLocation.QGeoRoute, /) -> None: ...
    def setPath(self, path: collections.abc.Sequence[PySide6.QtPositioning.QGeoCoordinate], /) -> None: ...
    def setRequest(self, request: PySide6.QtLocation.QGeoRouteRequest, /) -> None: ...
    def setRouteId(self, id: str, /) -> None: ...
    def setRouteLegs(self, legs: collections.abc.Sequence[PySide6.QtLocation.QGeoRoute], /) -> None: ...
    def setTravelMode(self, mode: PySide6.QtLocation.QGeoRouteRequest.TravelMode, /) -> None: ...
    def setTravelTime(self, secs: int, /) -> None: ...
    def swap(self, other: PySide6.QtLocation.QGeoRoute, /) -> None: ...
    def travelMode(self, /) -> PySide6.QtLocation.QGeoRouteRequest.TravelMode: ...
    def travelTime(self, /) -> int: ...


class QGeoRouteReply(PySide6.QtCore.QObject):

    aborted                  : typing.ClassVar[Signal] = ... # aborted()
    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(QGeoRouteReply::Error,QString); errorOccurred(QGeoRouteReply::Error)
    finished                 : typing.ClassVar[Signal] = ... # finished()

    class Error(enum.Enum):

        NoError                   = ...  # 0x0
        EngineNotSetError         = ...  # 0x1
        CommunicationError        = ...  # 0x2
        ParseError                = ...  # 0x3
        UnsupportedOptionError    = ...  # 0x4
        UnknownError              = ...  # 0x5


    @typing.overload
    def __init__(self, error: PySide6.QtLocation.QGeoRouteReply.Error, errorString: str, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...
    @typing.overload
    def __init__(self, request: PySide6.QtLocation.QGeoRouteRequest, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def abort(self, /) -> None: ...
    def addRoutes(self, routes: collections.abc.Sequence[PySide6.QtLocation.QGeoRoute], /) -> None: ...
    def error(self, /) -> PySide6.QtLocation.QGeoRouteReply.Error: ...
    def errorString(self, /) -> str: ...
    def isFinished(self, /) -> bool: ...
    def request(self, /) -> PySide6.QtLocation.QGeoRouteRequest: ...
    def routes(self, /) -> typing.List[PySide6.QtLocation.QGeoRoute]: ...
    def setError(self, error: PySide6.QtLocation.QGeoRouteReply.Error, errorString: str, /) -> None: ...
    def setFinished(self, finished: bool, /) -> None: ...
    def setRoutes(self, routes: collections.abc.Sequence[PySide6.QtLocation.QGeoRoute], /) -> None: ...


class QGeoRouteRequest(Shiboken.Object):

    class FeatureType(enum.Flag):

        NoFeature                 = ...  # 0x0
        TollFeature               = ...  # 0x1
        HighwayFeature            = ...  # 0x2
        PublicTransitFeature      = ...  # 0x4
        FerryFeature              = ...  # 0x8
        TunnelFeature             = ...  # 0x10
        DirtRoadFeature           = ...  # 0x20
        ParksFeature              = ...  # 0x40
        MotorPoolLaneFeature      = ...  # 0x80
        TrafficFeature            = ...  # 0x100

    class FeatureWeight(enum.Flag):

        NeutralFeatureWeight      = ...  # 0x0
        PreferFeatureWeight       = ...  # 0x1
        RequireFeatureWeight      = ...  # 0x2
        AvoidFeatureWeight        = ...  # 0x4
        DisallowFeatureWeight     = ...  # 0x8

    class ManeuverDetail(enum.Flag):

        NoManeuvers               = ...  # 0x0
        BasicManeuvers            = ...  # 0x1

    class RouteOptimization(enum.Flag):

        ShortestRoute             = ...  # 0x1
        FastestRoute              = ...  # 0x2
        MostEconomicRoute         = ...  # 0x4
        MostScenicRoute           = ...  # 0x8

    class SegmentDetail(enum.Flag):

        NoSegmentData             = ...  # 0x0
        BasicSegmentData          = ...  # 0x1

    class TravelMode(enum.Flag):

        CarTravel                 = ...  # 0x1
        PedestrianTravel          = ...  # 0x2
        BicycleTravel             = ...  # 0x4
        PublicTransitTravel       = ...  # 0x8
        TruckTravel               = ...  # 0x10


    @typing.overload
    def __init__(self, origin: PySide6.QtPositioning.QGeoCoordinate, destination: PySide6.QtPositioning.QGeoCoordinate, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtLocation.QGeoRouteRequest, /) -> None: ...
    @typing.overload
    def __init__(self, /, waypoints: collections.abc.Sequence[PySide6.QtPositioning.QGeoCoordinate] = ...) -> None: ...

    def __eq__(self, rhs: PySide6.QtLocation.QGeoRouteRequest, /) -> bool: ...
    def __ne__(self, rhs: PySide6.QtLocation.QGeoRouteRequest, /) -> bool: ...
    def departureTime(self, /) -> PySide6.QtCore.QDateTime: ...
    def excludeAreas(self, /) -> typing.List[PySide6.QtPositioning.QGeoRectangle]: ...
    def featureTypes(self, /) -> typing.List[PySide6.QtLocation.QGeoRouteRequest.FeatureType]: ...
    def featureWeight(self, featureType: PySide6.QtLocation.QGeoRouteRequest.FeatureType, /) -> PySide6.QtLocation.QGeoRouteRequest.FeatureWeight: ...
    def maneuverDetail(self, /) -> PySide6.QtLocation.QGeoRouteRequest.ManeuverDetail: ...
    def numberAlternativeRoutes(self, /) -> int: ...
    def routeOptimization(self, /) -> PySide6.QtLocation.QGeoRouteRequest.RouteOptimization: ...
    def segmentDetail(self, /) -> PySide6.QtLocation.QGeoRouteRequest.SegmentDetail: ...
    def setDepartureTime(self, departureTime: PySide6.QtCore.QDateTime, /) -> None: ...
    def setExcludeAreas(self, areas: collections.abc.Sequence[PySide6.QtPositioning.QGeoRectangle], /) -> None: ...
    def setFeatureWeight(self, featureType: PySide6.QtLocation.QGeoRouteRequest.FeatureType, featureWeight: PySide6.QtLocation.QGeoRouteRequest.FeatureWeight, /) -> None: ...
    def setManeuverDetail(self, maneuverDetail: PySide6.QtLocation.QGeoRouteRequest.ManeuverDetail, /) -> None: ...
    def setNumberAlternativeRoutes(self, alternatives: int, /) -> None: ...
    def setRouteOptimization(self, optimization: PySide6.QtLocation.QGeoRouteRequest.RouteOptimization, /) -> None: ...
    def setSegmentDetail(self, segmentDetail: PySide6.QtLocation.QGeoRouteRequest.SegmentDetail, /) -> None: ...
    def setTravelModes(self, travelModes: PySide6.QtLocation.QGeoRouteRequest.TravelMode, /) -> None: ...
    def setWaypoints(self, waypoints: collections.abc.Sequence[PySide6.QtPositioning.QGeoCoordinate], /) -> None: ...
    def swap(self, other: PySide6.QtLocation.QGeoRouteRequest, /) -> None: ...
    def travelModes(self, /) -> PySide6.QtLocation.QGeoRouteRequest.TravelMode: ...
    def waypoints(self, /) -> typing.List[PySide6.QtPositioning.QGeoCoordinate]: ...


class QGeoRouteSegment(Shiboken.Object):

    @typing.overload
    def __init__(self, other: PySide6.QtLocation.QGeoRouteSegment, /, *, travelTime: int | None = ..., distance: float | None = ..., path: collections.abc.Sequence[PySide6.QtPositioning.QGeoCoordinate] | None = ..., maneuver: PySide6.QtLocation.QGeoManeuver | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, travelTime: int | None = ..., distance: float | None = ..., path: collections.abc.Sequence[PySide6.QtPositioning.QGeoCoordinate] | None = ..., maneuver: PySide6.QtLocation.QGeoManeuver | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtLocation.QGeoRouteSegment, /) -> bool: ...
    def __ne__(self, rhs: PySide6.QtLocation.QGeoRouteSegment, /) -> bool: ...
    def distance(self, /) -> float: ...
    def isLegLastSegment(self, /) -> bool: ...
    def isValid(self, /) -> bool: ...
    def maneuver(self, /) -> PySide6.QtLocation.QGeoManeuver: ...
    def nextRouteSegment(self, /) -> PySide6.QtLocation.QGeoRouteSegment: ...
    def path(self, /) -> typing.List[PySide6.QtPositioning.QGeoCoordinate]: ...
    def setDistance(self, distance: float, /) -> None: ...
    def setManeuver(self, maneuver: PySide6.QtLocation.QGeoManeuver, /) -> None: ...
    def setNextRouteSegment(self, routeSegment: PySide6.QtLocation.QGeoRouteSegment, /) -> None: ...
    def setPath(self, path: collections.abc.Sequence[PySide6.QtPositioning.QGeoCoordinate], /) -> None: ...
    def setTravelTime(self, secs: int, /) -> None: ...
    def swap(self, other: PySide6.QtLocation.QGeoRouteSegment, /) -> None: ...
    def travelTime(self, /) -> int: ...


class QGeoRoutingManager(PySide6.QtCore.QObject):

    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(QGeoRouteReply*,QGeoRouteReply::Error,QString); errorOccurred(QGeoRouteReply*,QGeoRouteReply::Error)
    finished                 : typing.ClassVar[Signal] = ... # finished(QGeoRouteReply*)
    def calculateRoute(self, request: PySide6.QtLocation.QGeoRouteRequest, /) -> PySide6.QtLocation.QGeoRouteReply: ...
    def locale(self, /) -> PySide6.QtCore.QLocale: ...
    def managerName(self, /) -> str: ...
    def managerVersion(self, /) -> int: ...
    def measurementSystem(self, /) -> PySide6.QtCore.QLocale.MeasurementSystem: ...
    def setLocale(self, locale: PySide6.QtCore.QLocale | PySide6.QtCore.QLocale.Language, /) -> None: ...
    def setMeasurementSystem(self, system: PySide6.QtCore.QLocale.MeasurementSystem, /) -> None: ...
    def supportedFeatureTypes(self, /) -> PySide6.QtLocation.QGeoRouteRequest.FeatureType: ...
    def supportedFeatureWeights(self, /) -> PySide6.QtLocation.QGeoRouteRequest.FeatureWeight: ...
    def supportedManeuverDetails(self, /) -> PySide6.QtLocation.QGeoRouteRequest.ManeuverDetail: ...
    def supportedRouteOptimizations(self, /) -> PySide6.QtLocation.QGeoRouteRequest.RouteOptimization: ...
    def supportedSegmentDetails(self, /) -> PySide6.QtLocation.QGeoRouteRequest.SegmentDetail: ...
    def supportedTravelModes(self, /) -> PySide6.QtLocation.QGeoRouteRequest.TravelMode: ...
    def updateRoute(self, route: PySide6.QtLocation.QGeoRoute, position: PySide6.QtPositioning.QGeoCoordinate, /) -> PySide6.QtLocation.QGeoRouteReply: ...


class QGeoRoutingManagerEngine(PySide6.QtCore.QObject):

    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(QGeoRouteReply*,QGeoRouteReply::Error,QString); errorOccurred(QGeoRouteReply*,QGeoRouteReply::Error)
    finished                 : typing.ClassVar[Signal] = ... # finished(QGeoRouteReply*)

    def __init__(self, parameters: typing.Dict[str, typing.Any], /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def calculateRoute(self, request: PySide6.QtLocation.QGeoRouteRequest, /) -> PySide6.QtLocation.QGeoRouteReply: ...
    def locale(self, /) -> PySide6.QtCore.QLocale: ...
    def managerName(self, /) -> str: ...
    def managerVersion(self, /) -> int: ...
    def measurementSystem(self, /) -> PySide6.QtCore.QLocale.MeasurementSystem: ...
    def setLocale(self, locale: PySide6.QtCore.QLocale | PySide6.QtCore.QLocale.Language, /) -> None: ...
    def setMeasurementSystem(self, system: PySide6.QtCore.QLocale.MeasurementSystem, /) -> None: ...
    def setSupportedFeatureTypes(self, featureTypes: PySide6.QtLocation.QGeoRouteRequest.FeatureType, /) -> None: ...
    def setSupportedFeatureWeights(self, featureWeights: PySide6.QtLocation.QGeoRouteRequest.FeatureWeight, /) -> None: ...
    def setSupportedManeuverDetails(self, maneuverDetails: PySide6.QtLocation.QGeoRouteRequest.ManeuverDetail, /) -> None: ...
    def setSupportedRouteOptimizations(self, optimizations: PySide6.QtLocation.QGeoRouteRequest.RouteOptimization, /) -> None: ...
    def setSupportedSegmentDetails(self, segmentDetails: PySide6.QtLocation.QGeoRouteRequest.SegmentDetail, /) -> None: ...
    def setSupportedTravelModes(self, travelModes: PySide6.QtLocation.QGeoRouteRequest.TravelMode, /) -> None: ...
    def supportedFeatureTypes(self, /) -> PySide6.QtLocation.QGeoRouteRequest.FeatureType: ...
    def supportedFeatureWeights(self, /) -> PySide6.QtLocation.QGeoRouteRequest.FeatureWeight: ...
    def supportedManeuverDetails(self, /) -> PySide6.QtLocation.QGeoRouteRequest.ManeuverDetail: ...
    def supportedRouteOptimizations(self, /) -> PySide6.QtLocation.QGeoRouteRequest.RouteOptimization: ...
    def supportedSegmentDetails(self, /) -> PySide6.QtLocation.QGeoRouteRequest.SegmentDetail: ...
    def supportedTravelModes(self, /) -> PySide6.QtLocation.QGeoRouteRequest.TravelMode: ...
    def updateRoute(self, route: PySide6.QtLocation.QGeoRoute, position: PySide6.QtPositioning.QGeoCoordinate, /) -> PySide6.QtLocation.QGeoRouteReply: ...


class QGeoServiceProvider(PySide6.QtCore.QObject):

    class Error(enum.Enum):

        NoError                   = ...  # 0x0
        NotSupportedError         = ...  # 0x1
        UnknownParameterError     = ...  # 0x2
        MissingRequiredParameterError = ...  # 0x3
        ConnectionError           = ...  # 0x4
        LoaderError               = ...  # 0x5

    class GeocodingFeature(enum.Flag):

        AnyGeocodingFeatures      = ...  # -1
        NoGeocodingFeatures       = ...  # 0x0
        OnlineGeocodingFeature    = ...  # 0x1
        OfflineGeocodingFeature   = ...  # 0x2
        ReverseGeocodingFeature   = ...  # 0x4
        LocalizedGeocodingFeature = ...  # 0x8

    class MappingFeature(enum.Flag):

        AnyMappingFeatures        = ...  # -1
        NoMappingFeatures         = ...  # 0x0
        OnlineMappingFeature      = ...  # 0x1
        OfflineMappingFeature     = ...  # 0x2
        LocalizedMappingFeature   = ...  # 0x4

    class NavigationFeature(enum.Flag):

        AnyNavigationFeatures     = ...  # -1
        NoNavigationFeatures      = ...  # 0x0
        OnlineNavigationFeature   = ...  # 0x1
        OfflineNavigationFeature  = ...  # 0x2

    class PlacesFeature(enum.Flag):

        AnyPlacesFeatures         = ...  # -1
        NoPlacesFeatures          = ...  # 0x0
        OnlinePlacesFeature       = ...  # 0x1
        OfflinePlacesFeature      = ...  # 0x2
        SavePlaceFeature          = ...  # 0x4
        RemovePlaceFeature        = ...  # 0x8
        SaveCategoryFeature       = ...  # 0x10
        RemoveCategoryFeature     = ...  # 0x20
        PlaceRecommendationsFeature = ...  # 0x40
        SearchSuggestionsFeature  = ...  # 0x80
        LocalizedPlacesFeature    = ...  # 0x100
        NotificationsFeature      = ...  # 0x200
        PlaceMatchingFeature      = ...  # 0x400

    class RoutingFeature(enum.Flag):

        AnyRoutingFeatures        = ...  # -1
        NoRoutingFeatures         = ...  # 0x0
        OnlineRoutingFeature      = ...  # 0x1
        OfflineRoutingFeature     = ...  # 0x2
        LocalizedRoutingFeature   = ...  # 0x4
        RouteUpdatesFeature       = ...  # 0x8
        AlternativeRoutesFeature  = ...  # 0x10
        ExcludeAreasRoutingFeature = ...  # 0x20


    def __init__(self, providerName: str, /, parameters: typing.Dict[str, typing.Any] = ..., allowExperimental: bool = ...) -> None: ...

    @staticmethod
    def availableServiceProviders() -> typing.List[str]: ...
    def error(self, /) -> PySide6.QtLocation.QGeoServiceProvider.Error: ...
    def errorString(self, /) -> str: ...
    def geocodingError(self, /) -> PySide6.QtLocation.QGeoServiceProvider.Error: ...
    def geocodingErrorString(self, /) -> str: ...
    def geocodingFeatures(self, /) -> PySide6.QtLocation.QGeoServiceProvider.GeocodingFeature: ...
    def geocodingManager(self, /) -> PySide6.QtLocation.QGeoCodingManager: ...
    def mappingError(self, /) -> PySide6.QtLocation.QGeoServiceProvider.Error: ...
    def mappingErrorString(self, /) -> str: ...
    def mappingFeatures(self, /) -> PySide6.QtLocation.QGeoServiceProvider.MappingFeature: ...
    def navigationError(self, /) -> PySide6.QtLocation.QGeoServiceProvider.Error: ...
    def navigationErrorString(self, /) -> str: ...
    def navigationFeatures(self, /) -> PySide6.QtLocation.QGeoServiceProvider.NavigationFeature: ...
    def placeManager(self, /) -> PySide6.QtLocation.QPlaceManager: ...
    def placesError(self, /) -> PySide6.QtLocation.QGeoServiceProvider.Error: ...
    def placesErrorString(self, /) -> str: ...
    def placesFeatures(self, /) -> PySide6.QtLocation.QGeoServiceProvider.PlacesFeature: ...
    def routingError(self, /) -> PySide6.QtLocation.QGeoServiceProvider.Error: ...
    def routingErrorString(self, /) -> str: ...
    def routingFeatures(self, /) -> PySide6.QtLocation.QGeoServiceProvider.RoutingFeature: ...
    def routingManager(self, /) -> PySide6.QtLocation.QGeoRoutingManager: ...
    def setAllowExperimental(self, allow: bool, /) -> None: ...
    def setLocale(self, locale: PySide6.QtCore.QLocale | PySide6.QtCore.QLocale.Language, /) -> None: ...
    def setParameters(self, parameters: typing.Dict[str, typing.Any], /) -> None: ...


class QGeoServiceProviderFactory(Shiboken.Object):

    def __init__(self, /) -> None: ...

    def createGeocodingManagerEngine(self, parameters: typing.Dict[str, typing.Any], error: PySide6.QtLocation.QGeoServiceProvider.Error, /) -> typing.Tuple[PySide6.QtLocation.QGeoCodingManagerEngine, str]: ...
    def createPlaceManagerEngine(self, parameters: typing.Dict[str, typing.Any], error: PySide6.QtLocation.QGeoServiceProvider.Error, /) -> typing.Tuple[PySide6.QtLocation.QPlaceManagerEngine, str]: ...
    def createRoutingManagerEngine(self, parameters: typing.Dict[str, typing.Any], error: PySide6.QtLocation.QGeoServiceProvider.Error, /) -> typing.Tuple[PySide6.QtLocation.QGeoRoutingManagerEngine, str]: ...


class QIntList: ...


class QLocation(Shiboken.Object):

    class Visibility(enum.Flag):

        UnspecifiedVisibility     = ...  # 0x0
        DeviceVisibility          = ...  # 0x1
        PrivateVisibility         = ...  # 0x2
        PublicVisibility          = ...  # 0x4


class QPlace(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtLocation.QPlace, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtLocation.QPlace, /) -> bool: ...
    def __ne__(self, rhs: PySide6.QtLocation.QPlace, /) -> bool: ...
    def appendContactDetail(self, contactType: str, detail: PySide6.QtLocation.QPlaceContactDetail, /) -> None: ...
    def attribution(self, /) -> str: ...
    def categories(self, /) -> typing.List[PySide6.QtLocation.QPlaceCategory]: ...
    def contactDetails(self, contactType: str, /) -> typing.List[PySide6.QtLocation.QPlaceContactDetail]: ...
    def contactTypes(self, /) -> typing.List[str]: ...
    def content(self, type: PySide6.QtLocation.QPlaceContent.Type, /) -> typing.Dict[int, PySide6.QtLocation.QPlaceContent]: ...
    def detailsFetched(self, /) -> bool: ...
    def extendedAttribute(self, attributeType: str, /) -> PySide6.QtLocation.QPlaceAttribute: ...
    def extendedAttributeTypes(self, /) -> typing.List[str]: ...
    def icon(self, /) -> PySide6.QtLocation.QPlaceIcon: ...
    def insertContent(self, type: PySide6.QtLocation.QPlaceContent.Type, content: typing.Dict[int, PySide6.QtLocation.QPlaceContent], /) -> None: ...
    def isEmpty(self, /) -> bool: ...
    def location(self, /) -> PySide6.QtPositioning.QGeoLocation: ...
    def name(self, /) -> str: ...
    def placeId(self, /) -> str: ...
    def primaryEmail(self, /) -> str: ...
    def primaryFax(self, /) -> str: ...
    def primaryPhone(self, /) -> str: ...
    def primaryWebsite(self, /) -> PySide6.QtCore.QUrl: ...
    def ratings(self, /) -> PySide6.QtLocation.QPlaceRatings: ...
    def removeContactDetails(self, contactType: str, /) -> None: ...
    def removeExtendedAttribute(self, attributeType: str, /) -> None: ...
    def setAttribution(self, attribution: str, /) -> None: ...
    def setCategories(self, categories: collections.abc.Sequence[PySide6.QtLocation.QPlaceCategory], /) -> None: ...
    def setCategory(self, category: PySide6.QtLocation.QPlaceCategory, /) -> None: ...
    def setContactDetails(self, contactType: str, details: collections.abc.Sequence[PySide6.QtLocation.QPlaceContactDetail], /) -> None: ...
    def setContent(self, type: PySide6.QtLocation.QPlaceContent.Type, content: typing.Dict[int, PySide6.QtLocation.QPlaceContent], /) -> None: ...
    def setDetailsFetched(self, fetched: bool, /) -> None: ...
    def setExtendedAttribute(self, attributeType: str, attribute: PySide6.QtLocation.QPlaceAttribute, /) -> None: ...
    def setIcon(self, icon: PySide6.QtLocation.QPlaceIcon, /) -> None: ...
    def setLocation(self, location: PySide6.QtPositioning.QGeoLocation, /) -> None: ...
    def setName(self, name: str, /) -> None: ...
    def setPlaceId(self, identifier: str, /) -> None: ...
    def setRatings(self, ratings: PySide6.QtLocation.QPlaceRatings, /) -> None: ...
    def setSupplier(self, supplier: PySide6.QtLocation.QPlaceSupplier, /) -> None: ...
    def setTotalContentCount(self, type: PySide6.QtLocation.QPlaceContent.Type, total: int, /) -> None: ...
    def setVisibility(self, visibility: PySide6.QtLocation.QLocation.Visibility, /) -> None: ...
    def supplier(self, /) -> PySide6.QtLocation.QPlaceSupplier: ...
    def swap(self, other: PySide6.QtLocation.QPlace, /) -> None: ...
    def totalContentCount(self, type: PySide6.QtLocation.QPlaceContent.Type, /) -> int: ...
    def visibility(self, /) -> PySide6.QtLocation.QLocation.Visibility: ...


class QPlaceAttribute(Shiboken.Object):

    @typing.overload
    def __init__(self, other: PySide6.QtLocation.QPlaceAttribute, /, *, label: str | None = ..., text: str | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, label: str | None = ..., text: str | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtLocation.QPlaceAttribute, /) -> bool: ...
    def __ne__(self, rhs: PySide6.QtLocation.QPlaceAttribute, /) -> bool: ...
    def isEmpty(self, /) -> bool: ...
    def label(self, /) -> str: ...
    def setLabel(self, label: str, /) -> None: ...
    def setText(self, text: str, /) -> None: ...
    def swap(self, other: PySide6.QtLocation.QPlaceAttribute, /) -> None: ...
    def text(self, /) -> str: ...


class QPlaceCategory(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtLocation.QPlaceCategory, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtLocation.QPlaceCategory, /) -> bool: ...
    def __ne__(self, rhs: PySide6.QtLocation.QPlaceCategory, /) -> bool: ...
    def categoryId(self, /) -> str: ...
    def icon(self, /) -> PySide6.QtLocation.QPlaceIcon: ...
    def isEmpty(self, /) -> bool: ...
    def name(self, /) -> str: ...
    def setCategoryId(self, identifier: str, /) -> None: ...
    def setIcon(self, icon: PySide6.QtLocation.QPlaceIcon, /) -> None: ...
    def setName(self, name: str, /) -> None: ...
    def setVisibility(self, visibility: PySide6.QtLocation.QLocation.Visibility, /) -> None: ...
    def swap(self, other: PySide6.QtLocation.QPlaceCategory, /) -> None: ...
    def visibility(self, /) -> PySide6.QtLocation.QLocation.Visibility: ...


class QPlaceContactDetail(Shiboken.Object):

    @typing.overload
    def __init__(self, other: PySide6.QtLocation.QPlaceContactDetail, /, *, label: str | None = ..., value: str | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, label: str | None = ..., value: str | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtLocation.QPlaceContactDetail, /) -> bool: ...
    def __ne__(self, rhs: PySide6.QtLocation.QPlaceContactDetail, /) -> bool: ...
    def clear(self, /) -> None: ...
    def label(self, /) -> str: ...
    def setLabel(self, label: str, /) -> None: ...
    def setValue(self, value: str, /) -> None: ...
    def swap(self, other: PySide6.QtLocation.QPlaceContactDetail, /) -> None: ...
    def value(self, /) -> str: ...


class QPlaceContent(Shiboken.Object):

    class DataTag(enum.Enum):

        ContentSupplier           = ...  # 0x0
        ContentUser               = ...  # 0x1
        ContentAttribution        = ...  # 0x2
        ImageId                   = ...  # 0x3
        ImageUrl                  = ...  # 0x4
        ImageMimeType             = ...  # 0x5
        EditorialTitle            = ...  # 0x6
        EditorialText             = ...  # 0x7
        EditorialLanguage         = ...  # 0x8
        ReviewId                  = ...  # 0x9
        ReviewDateTime            = ...  # 0xa
        ReviewTitle               = ...  # 0xb
        ReviewText                = ...  # 0xc
        ReviewLanguage            = ...  # 0xd
        ReviewRating              = ...  # 0xe
        CustomDataTag             = ...  # 0x3e8

    class Type(enum.Enum):

        NoType                    = ...  # 0x0
        ImageType                 = ...  # 0x1
        ReviewType                = ...  # 0x2
        EditorialType             = ...  # 0x3
        CustomType                = ...  # 0x100


    @typing.overload
    def __init__(self, other: PySide6.QtLocation.QPlaceContent, /) -> None: ...
    @typing.overload
    def __init__(self, /, type: PySide6.QtLocation.QPlaceContent.Type = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, other: PySide6.QtLocation.QPlaceContent | PySide6.QtLocation.QPlaceContent.Type, /) -> bool: ...
    def __ne__(self, other: PySide6.QtLocation.QPlaceContent | PySide6.QtLocation.QPlaceContent.Type, /) -> bool: ...
    def attribution(self, /) -> str: ...
    def dataTags(self, /) -> typing.List[PySide6.QtLocation.QPlaceContent.DataTag]: ...
    def setAttribution(self, attribution: str, /) -> None: ...
    def setSupplier(self, supplier: PySide6.QtLocation.QPlaceSupplier, /) -> None: ...
    def setUser(self, user: PySide6.QtLocation.QPlaceUser, /) -> None: ...
    def setValue(self, tag: PySide6.QtLocation.QPlaceContent.DataTag, arg__2: typing.Any, /) -> None: ...
    def supplier(self, /) -> PySide6.QtLocation.QPlaceSupplier: ...
    def swap(self, other: PySide6.QtLocation.QPlaceContent | PySide6.QtLocation.QPlaceContent.Type, /) -> None: ...
    def type(self, /) -> PySide6.QtLocation.QPlaceContent.Type: ...
    def user(self, /) -> PySide6.QtLocation.QPlaceUser: ...
    def value(self, tag: PySide6.QtLocation.QPlaceContent.DataTag, /) -> typing.Any: ...


class QPlaceContentReply(PySide6.QtLocation.QPlaceReply):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def content(self, /) -> typing.Dict[int, PySide6.QtLocation.QPlaceContent]: ...
    def nextPageRequest(self, /) -> PySide6.QtLocation.QPlaceContentRequest: ...
    def previousPageRequest(self, /) -> PySide6.QtLocation.QPlaceContentRequest: ...
    def request(self, /) -> PySide6.QtLocation.QPlaceContentRequest: ...
    def setContent(self, content: typing.Dict[int, PySide6.QtLocation.QPlaceContent], /) -> None: ...
    def setNextPageRequest(self, next: PySide6.QtLocation.QPlaceContentRequest, /) -> None: ...
    def setPreviousPageRequest(self, previous: PySide6.QtLocation.QPlaceContentRequest, /) -> None: ...
    def setRequest(self, request: PySide6.QtLocation.QPlaceContentRequest, /) -> None: ...
    def setTotalCount(self, total: int, /) -> None: ...
    def totalCount(self, /) -> int: ...
    def type(self, /) -> PySide6.QtLocation.QPlaceReply.Type: ...


class QPlaceContentRequest(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtLocation.QPlaceContentRequest, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtLocation.QPlaceContentRequest, /) -> bool: ...
    def __ne__(self, rhs: PySide6.QtLocation.QPlaceContentRequest, /) -> bool: ...
    def clear(self, /) -> None: ...
    def contentContext(self, /) -> typing.Any: ...
    def contentType(self, /) -> PySide6.QtLocation.QPlaceContent.Type: ...
    def limit(self, /) -> int: ...
    def placeId(self, /) -> str: ...
    def setContentContext(self, context: typing.Any, /) -> None: ...
    def setContentType(self, type: PySide6.QtLocation.QPlaceContent.Type, /) -> None: ...
    def setLimit(self, limit: int, /) -> None: ...
    def setPlaceId(self, identifier: str, /) -> None: ...
    def swap(self, other: PySide6.QtLocation.QPlaceContentRequest, /) -> None: ...


class QPlaceDetailsReply(PySide6.QtLocation.QPlaceReply):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def place(self, /) -> PySide6.QtLocation.QPlace: ...
    def setPlace(self, place: PySide6.QtLocation.QPlace, /) -> None: ...
    def type(self, /) -> PySide6.QtLocation.QPlaceReply.Type: ...


class QPlaceIcon(Shiboken.Object):

    @typing.overload
    def __init__(self, other: PySide6.QtLocation.QPlaceIcon, /, *, parameters: typing.Optional[typing.Dict[str, typing.Any]] = ..., manager: PySide6.QtLocation.QPlaceManager | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, parameters: typing.Optional[typing.Dict[str, typing.Any]] = ..., manager: PySide6.QtLocation.QPlaceManager | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtLocation.QPlaceIcon, /) -> bool: ...
    def __ne__(self, rhs: PySide6.QtLocation.QPlaceIcon, /) -> bool: ...
    def isEmpty(self, /) -> bool: ...
    def manager(self, /) -> PySide6.QtLocation.QPlaceManager: ...
    def parameters(self, /) -> typing.Dict[str, typing.Any]: ...
    def setManager(self, manager: PySide6.QtLocation.QPlaceManager, /) -> None: ...
    def setParameters(self, parameters: typing.Dict[str, typing.Any], /) -> None: ...
    def swap(self, other: PySide6.QtLocation.QPlaceIcon, /) -> None: ...
    def url(self, /, size: PySide6.QtCore.QSize = ...) -> PySide6.QtCore.QUrl: ...


class QPlaceIdReply(PySide6.QtLocation.QPlaceReply):

    class OperationType(enum.Enum):

        SavePlace                 = ...  # 0x0
        SaveCategory              = ...  # 0x1
        RemovePlace               = ...  # 0x2
        RemoveCategory            = ...  # 0x3


    def __init__(self, operationType: PySide6.QtLocation.QPlaceIdReply.OperationType, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def id(self, /) -> str: ...
    def operationType(self, /) -> PySide6.QtLocation.QPlaceIdReply.OperationType: ...
    def setId(self, identifier: str, /) -> None: ...
    def type(self, /) -> PySide6.QtLocation.QPlaceReply.Type: ...


class QPlaceManager(PySide6.QtCore.QObject):

    categoryAdded            : typing.ClassVar[Signal] = ... # categoryAdded(QPlaceCategory,QString)
    categoryRemoved          : typing.ClassVar[Signal] = ... # categoryRemoved(QString,QString)
    categoryUpdated          : typing.ClassVar[Signal] = ... # categoryUpdated(QPlaceCategory,QString)
    dataChanged              : typing.ClassVar[Signal] = ... # dataChanged()
    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(QPlaceReply*,QPlaceReply::Error,QString); errorOccurred(QPlaceReply*,QPlaceReply::Error)
    finished                 : typing.ClassVar[Signal] = ... # finished(QPlaceReply*)
    placeAdded               : typing.ClassVar[Signal] = ... # placeAdded(QString)
    placeRemoved             : typing.ClassVar[Signal] = ... # placeRemoved(QString)
    placeUpdated             : typing.ClassVar[Signal] = ... # placeUpdated(QString)
    def category(self, categoryId: str, /) -> PySide6.QtLocation.QPlaceCategory: ...
    def childCategories(self, /, parentId: str = ...) -> typing.List[PySide6.QtLocation.QPlaceCategory]: ...
    def childCategoryIds(self, /, parentId: str = ...) -> typing.List[str]: ...
    def compatiblePlace(self, place: PySide6.QtLocation.QPlace, /) -> PySide6.QtLocation.QPlace: ...
    def getPlaceContent(self, request: PySide6.QtLocation.QPlaceContentRequest, /) -> PySide6.QtLocation.QPlaceContentReply: ...
    def getPlaceDetails(self, placeId: str, /) -> PySide6.QtLocation.QPlaceDetailsReply: ...
    def initializeCategories(self, /) -> PySide6.QtLocation.QPlaceReply: ...
    def locales(self, /) -> typing.List[PySide6.QtCore.QLocale]: ...
    def managerName(self, /) -> str: ...
    def managerVersion(self, /) -> int: ...
    def matchingPlaces(self, request: PySide6.QtLocation.QPlaceMatchRequest, /) -> PySide6.QtLocation.QPlaceMatchReply: ...
    def parentCategoryId(self, categoryId: str, /) -> str: ...
    def removeCategory(self, categoryId: str, /) -> PySide6.QtLocation.QPlaceIdReply: ...
    def removePlace(self, placeId: str, /) -> PySide6.QtLocation.QPlaceIdReply: ...
    def saveCategory(self, category: PySide6.QtLocation.QPlaceCategory, /, parentId: str = ...) -> PySide6.QtLocation.QPlaceIdReply: ...
    def savePlace(self, place: PySide6.QtLocation.QPlace, /) -> PySide6.QtLocation.QPlaceIdReply: ...
    def search(self, query: PySide6.QtLocation.QPlaceSearchRequest, /) -> PySide6.QtLocation.QPlaceSearchReply: ...
    def searchSuggestions(self, request: PySide6.QtLocation.QPlaceSearchRequest, /) -> PySide6.QtLocation.QPlaceSearchSuggestionReply: ...
    def setLocale(self, locale: PySide6.QtCore.QLocale | PySide6.QtCore.QLocale.Language, /) -> None: ...
    def setLocales(self, locale: collections.abc.Sequence[PySide6.QtCore.QLocale], /) -> None: ...


class QPlaceManagerEngine(PySide6.QtCore.QObject):

    categoryAdded            : typing.ClassVar[Signal] = ... # categoryAdded(QPlaceCategory,QString)
    categoryRemoved          : typing.ClassVar[Signal] = ... # categoryRemoved(QString,QString)
    categoryUpdated          : typing.ClassVar[Signal] = ... # categoryUpdated(QPlaceCategory,QString)
    dataChanged              : typing.ClassVar[Signal] = ... # dataChanged()
    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(QPlaceReply*,QPlaceReply::Error,QString); errorOccurred(QPlaceReply*,QPlaceReply::Error)
    finished                 : typing.ClassVar[Signal] = ... # finished(QPlaceReply*)
    placeAdded               : typing.ClassVar[Signal] = ... # placeAdded(QString)
    placeRemoved             : typing.ClassVar[Signal] = ... # placeRemoved(QString)
    placeUpdated             : typing.ClassVar[Signal] = ... # placeUpdated(QString)

    def __init__(self, parameters: typing.Dict[str, typing.Any], /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def category(self, categoryId: str, /) -> PySide6.QtLocation.QPlaceCategory: ...
    def childCategories(self, parentId: str, /) -> typing.List[PySide6.QtLocation.QPlaceCategory]: ...
    def childCategoryIds(self, categoryId: str, /) -> typing.List[str]: ...
    def compatiblePlace(self, original: PySide6.QtLocation.QPlace, /) -> PySide6.QtLocation.QPlace: ...
    def constructIconUrl(self, icon: PySide6.QtLocation.QPlaceIcon, size: PySide6.QtCore.QSize, /) -> PySide6.QtCore.QUrl: ...
    def getPlaceContent(self, request: PySide6.QtLocation.QPlaceContentRequest, /) -> PySide6.QtLocation.QPlaceContentReply: ...
    def getPlaceDetails(self, placeId: str, /) -> PySide6.QtLocation.QPlaceDetailsReply: ...
    def initializeCategories(self, /) -> PySide6.QtLocation.QPlaceReply: ...
    def locales(self, /) -> typing.List[PySide6.QtCore.QLocale]: ...
    def manager(self, /) -> PySide6.QtLocation.QPlaceManager: ...
    def managerName(self, /) -> str: ...
    def managerVersion(self, /) -> int: ...
    def matchingPlaces(self, request: PySide6.QtLocation.QPlaceMatchRequest, /) -> PySide6.QtLocation.QPlaceMatchReply: ...
    def parentCategoryId(self, categoryId: str, /) -> str: ...
    def removeCategory(self, categoryId: str, /) -> PySide6.QtLocation.QPlaceIdReply: ...
    def removePlace(self, placeId: str, /) -> PySide6.QtLocation.QPlaceIdReply: ...
    def saveCategory(self, category: PySide6.QtLocation.QPlaceCategory, parentId: str, /) -> PySide6.QtLocation.QPlaceIdReply: ...
    def savePlace(self, place: PySide6.QtLocation.QPlace, /) -> PySide6.QtLocation.QPlaceIdReply: ...
    def search(self, request: PySide6.QtLocation.QPlaceSearchRequest, /) -> PySide6.QtLocation.QPlaceSearchReply: ...
    def searchSuggestions(self, request: PySide6.QtLocation.QPlaceSearchRequest, /) -> PySide6.QtLocation.QPlaceSearchSuggestionReply: ...
    def setLocales(self, locales: collections.abc.Sequence[PySide6.QtCore.QLocale], /) -> None: ...


class QPlaceMatchReply(PySide6.QtLocation.QPlaceReply):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def places(self, /) -> typing.List[PySide6.QtLocation.QPlace]: ...
    def request(self, /) -> PySide6.QtLocation.QPlaceMatchRequest: ...
    def setPlaces(self, results: collections.abc.Sequence[PySide6.QtLocation.QPlace], /) -> None: ...
    def setRequest(self, request: PySide6.QtLocation.QPlaceMatchRequest, /) -> None: ...
    def type(self, /) -> PySide6.QtLocation.QPlaceReply.Type: ...


class QPlaceMatchRequest(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtLocation.QPlaceMatchRequest, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtLocation.QPlaceMatchRequest, /) -> bool: ...
    def __ne__(self, rhs: PySide6.QtLocation.QPlaceMatchRequest, /) -> bool: ...
    def clear(self, /) -> None: ...
    def parameters(self, /) -> typing.Dict[str, typing.Any]: ...
    def places(self, /) -> typing.List[PySide6.QtLocation.QPlace]: ...
    def setParameters(self, parameters: typing.Dict[str, typing.Any], /) -> None: ...
    def setPlaces(self, places: collections.abc.Sequence[PySide6.QtLocation.QPlace], /) -> None: ...
    def setResults(self, results: collections.abc.Sequence[PySide6.QtLocation.QPlaceSearchResult], /) -> None: ...
    def swap(self, other: PySide6.QtLocation.QPlaceMatchRequest, /) -> None: ...


class QPlaceProposedSearchResult(PySide6.QtLocation.QPlaceSearchResult):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtLocation.QPlaceSearchResult, /) -> None: ...

    def searchRequest(self, /) -> PySide6.QtLocation.QPlaceSearchRequest: ...
    def setSearchRequest(self, request: PySide6.QtLocation.QPlaceSearchRequest, /) -> None: ...


class QPlaceRatings(Shiboken.Object):

    @typing.overload
    def __init__(self, other: PySide6.QtLocation.QPlaceRatings, /, *, average: float | None = ..., maximum: float | None = ..., count: int | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, average: float | None = ..., maximum: float | None = ..., count: int | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtLocation.QPlaceRatings, /) -> bool: ...
    def __ne__(self, rhs: PySide6.QtLocation.QPlaceRatings, /) -> bool: ...
    def average(self, /) -> float: ...
    def count(self, /) -> int: ...
    def isEmpty(self, /) -> bool: ...
    def maximum(self, /) -> float: ...
    def setAverage(self, average: float, /) -> None: ...
    def setCount(self, count: int, /) -> None: ...
    def setMaximum(self, max: float, /) -> None: ...
    def swap(self, other: PySide6.QtLocation.QPlaceRatings, /) -> None: ...


class QPlaceReply(PySide6.QtCore.QObject):

    aborted                  : typing.ClassVar[Signal] = ... # aborted()
    contentUpdated           : typing.ClassVar[Signal] = ... # contentUpdated()
    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(QPlaceReply::Error,QString); errorOccurred(QPlaceReply::Error)
    finished                 : typing.ClassVar[Signal] = ... # finished()

    class Error(enum.Enum):

        NoError                   = ...  # 0x0
        PlaceDoesNotExistError    = ...  # 0x1
        CategoryDoesNotExistError = ...  # 0x2
        CommunicationError        = ...  # 0x3
        ParseError                = ...  # 0x4
        PermissionsError          = ...  # 0x5
        UnsupportedError          = ...  # 0x6
        BadArgumentError          = ...  # 0x7
        CancelError               = ...  # 0x8
        UnknownError              = ...  # 0x9

    class Type(enum.Enum):

        Reply                     = ...  # 0x0
        DetailsReply              = ...  # 0x1
        SearchReply               = ...  # 0x2
        SearchSuggestionReply     = ...  # 0x3
        ContentReply              = ...  # 0x4
        IdReply                   = ...  # 0x5
        MatchReply                = ...  # 0x6


    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def abort(self, /) -> None: ...
    def error(self, /) -> PySide6.QtLocation.QPlaceReply.Error: ...
    def errorString(self, /) -> str: ...
    def isFinished(self, /) -> bool: ...
    def setError(self, error: PySide6.QtLocation.QPlaceReply.Error, errorString: str, /) -> None: ...
    def setFinished(self, finished: bool, /) -> None: ...
    def type(self, /) -> PySide6.QtLocation.QPlaceReply.Type: ...


class QPlaceResult(PySide6.QtLocation.QPlaceSearchResult):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtLocation.QPlaceSearchResult, /) -> None: ...

    def distance(self, /) -> float: ...
    def isSponsored(self, /) -> bool: ...
    def place(self, /) -> PySide6.QtLocation.QPlace: ...
    def setDistance(self, distance: float, /) -> None: ...
    def setPlace(self, place: PySide6.QtLocation.QPlace, /) -> None: ...
    def setSponsored(self, sponsored: bool, /) -> None: ...


class QPlaceSearchReply(PySide6.QtLocation.QPlaceReply):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def nextPageRequest(self, /) -> PySide6.QtLocation.QPlaceSearchRequest: ...
    def previousPageRequest(self, /) -> PySide6.QtLocation.QPlaceSearchRequest: ...
    def request(self, /) -> PySide6.QtLocation.QPlaceSearchRequest: ...
    def results(self, /) -> typing.List[PySide6.QtLocation.QPlaceSearchResult]: ...
    def setNextPageRequest(self, next: PySide6.QtLocation.QPlaceSearchRequest, /) -> None: ...
    def setPreviousPageRequest(self, previous: PySide6.QtLocation.QPlaceSearchRequest, /) -> None: ...
    def setRequest(self, request: PySide6.QtLocation.QPlaceSearchRequest, /) -> None: ...
    def setResults(self, results: collections.abc.Sequence[PySide6.QtLocation.QPlaceSearchResult], /) -> None: ...
    def type(self, /) -> PySide6.QtLocation.QPlaceReply.Type: ...


class QPlaceSearchRequest(Shiboken.Object):

    class RelevanceHint(enum.Enum):

        UnspecifiedHint           = ...  # 0x0
        DistanceHint              = ...  # 0x1
        LexicalPlaceNameHint      = ...  # 0x2


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtLocation.QPlaceSearchRequest, /) -> None: ...

    def __eq__(self, rhs: PySide6.QtLocation.QPlaceSearchRequest, /) -> bool: ...
    def __ne__(self, rhs: PySide6.QtLocation.QPlaceSearchRequest, /) -> bool: ...
    def categories(self, /) -> typing.List[PySide6.QtLocation.QPlaceCategory]: ...
    def clear(self, /) -> None: ...
    def limit(self, /) -> int: ...
    def recommendationId(self, /) -> str: ...
    def relevanceHint(self, /) -> PySide6.QtLocation.QPlaceSearchRequest.RelevanceHint: ...
    def searchArea(self, /) -> PySide6.QtPositioning.QGeoShape: ...
    def searchContext(self, /) -> typing.Any: ...
    def searchTerm(self, /) -> str: ...
    def setCategories(self, categories: collections.abc.Sequence[PySide6.QtLocation.QPlaceCategory], /) -> None: ...
    def setCategory(self, category: PySide6.QtLocation.QPlaceCategory, /) -> None: ...
    def setLimit(self, limit: int, /) -> None: ...
    def setRecommendationId(self, recommendationId: str, /) -> None: ...
    def setRelevanceHint(self, hint: PySide6.QtLocation.QPlaceSearchRequest.RelevanceHint, /) -> None: ...
    def setSearchArea(self, area: PySide6.QtPositioning.QGeoShape, /) -> None: ...
    def setSearchContext(self, context: typing.Any, /) -> None: ...
    def setSearchTerm(self, term: str, /) -> None: ...
    def setVisibilityScope(self, visibilityScopes: PySide6.QtLocation.QLocation.Visibility, /) -> None: ...
    def swap(self, other: PySide6.QtLocation.QPlaceSearchRequest, /) -> None: ...
    def visibilityScope(self, /) -> PySide6.QtLocation.QLocation.Visibility: ...


class QPlaceSearchResult(Shiboken.Object):

    class SearchResultType(enum.Enum):

        UnknownSearchResult       = ...  # 0x0
        PlaceResult               = ...  # 0x1
        ProposedSearchResult      = ...  # 0x2


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtLocation.QPlaceSearchResult, /) -> None: ...

    def __eq__(self, other: PySide6.QtLocation.QPlaceSearchResult, /) -> bool: ...
    def __ne__(self, other: PySide6.QtLocation.QPlaceSearchResult, /) -> bool: ...
    def icon(self, /) -> PySide6.QtLocation.QPlaceIcon: ...
    def setIcon(self, icon: PySide6.QtLocation.QPlaceIcon, /) -> None: ...
    def setTitle(self, title: str, /) -> None: ...
    def title(self, /) -> str: ...
    def type(self, /) -> PySide6.QtLocation.QPlaceSearchResult.SearchResultType: ...


class QPlaceSearchSuggestionReply(PySide6.QtLocation.QPlaceReply):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def setSuggestions(self, suggestions: collections.abc.Sequence[str], /) -> None: ...
    def suggestions(self, /) -> typing.List[str]: ...
    def type(self, /) -> PySide6.QtLocation.QPlaceReply.Type: ...


class QPlaceSupplier(Shiboken.Object):

    @typing.overload
    def __init__(self, other: PySide6.QtLocation.QPlaceSupplier, /, *, name: str | None = ..., supplierId: str | None = ..., url: PySide6.QtCore.QUrl | None = ..., icon: PySide6.QtLocation.QPlaceIcon | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, name: str | None = ..., supplierId: str | None = ..., url: PySide6.QtCore.QUrl | None = ..., icon: PySide6.QtLocation.QPlaceIcon | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtLocation.QPlaceSupplier, /) -> bool: ...
    def __ne__(self, rhs: PySide6.QtLocation.QPlaceSupplier, /) -> bool: ...
    def icon(self, /) -> PySide6.QtLocation.QPlaceIcon: ...
    def isEmpty(self, /) -> bool: ...
    def name(self, /) -> str: ...
    def setIcon(self, icon: PySide6.QtLocation.QPlaceIcon, /) -> None: ...
    def setName(self, data: str, /) -> None: ...
    def setSupplierId(self, identifier: str, /) -> None: ...
    def setUrl(self, data: PySide6.QtCore.QUrl | str, /) -> None: ...
    def supplierId(self, /) -> str: ...
    def swap(self, other: PySide6.QtLocation.QPlaceSupplier, /) -> None: ...
    def url(self, /) -> PySide6.QtCore.QUrl: ...


class QPlaceUser(Shiboken.Object):

    @typing.overload
    def __init__(self, other: PySide6.QtLocation.QPlaceUser, /, *, userId: str | None = ..., name: str | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, userId: str | None = ..., name: str | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtLocation.QPlaceUser, /) -> bool: ...
    def __ne__(self, rhs: PySide6.QtLocation.QPlaceUser, /) -> bool: ...
    def name(self, /) -> str: ...
    def setName(self, name: str, /) -> None: ...
    def setUserId(self, identifier: str, /) -> None: ...
    def swap(self, other: PySide6.QtLocation.QPlaceUser, /) -> None: ...
    def userId(self, /) -> str: ...


# eof
