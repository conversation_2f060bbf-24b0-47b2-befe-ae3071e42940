Metadata-Version: 2.4
Name: edge-tts
Version: 7.0.2
Summary: Microsoft Edge's TTS
Home-page: https://github.com/rany2/edge-tts
Author: rany
Author-email: <EMAIL>
Project-URL: Bug Tracker, https://github.com/rany2/edge-tts/issues
Classifier: Programming Language :: Python :: 3
Classifier: License :: OSI Approved :: GNU Lesser General Public License v3 (LGPLv3)
Classifier: Operating System :: OS Independent
Requires-Python: >=3.7
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: aiohttp<4.0.0,>=3.8.0
Requires-Dist: certifi>=2023.11.17
Requires-Dist: srt<4.0.0,>=3.4.1
Requires-Dist: tabulate<1.0.0,>=0.4.4
Requires-Dist: typing-extensions<5.0.0,>=4.1.0
Provides-Extra: dev
Requires-Dist: black; extra == "dev"
Requires-Dist: isort; extra == "dev"
Requires-Dist: mypy; extra == "dev"
Requires-Dist: pylint; extra == "dev"
Requires-Dist: types-tabulate; extra == "dev"
Dynamic: license-file
Dynamic: requires-dist

# edge-tts

`edge-tts` is a Python module that allows you to use Microsoft Edge's online text-to-speech service from within your Python code or using the provided `edge-tts` or `edge-playback` command.

## Installation

To install it, run the following command:

    $ pip install edge-tts

If you only want to use the `edge-tts` and `edge-playback` commands, it would be better to use `pipx`:

    $ pipx install edge-tts

## Usage

### Basic usage

If you want to use the `edge-tts` command, you can simply run it with the following command:

    $ edge-tts --text "Hello, world!" --write-media hello.mp3 --write-subtitles hello.srt

If you wish to play it back immediately with subtitles, you could use the `edge-playback` command:

    $ edge-playback --text "Hello, world!"

Note that `edge-playback` requires the installation of the [`mpv` command line player](https://mpv.io/), except on Windows.

All `edge-tts` commands work with `edge-playback` with the exception of the `--write-media`, `--write-subtitles` and `--list-voices` options.

### Changing the voice

You can change the voice used by the text-to-speech service by using the `--voice` option. The `--list-voices` option can be used to list all available voices.

    $ edge-tts --list-voices
    Name                               Gender    ContentCategories      VoicePersonalities
    ---------------------------------  --------  ---------------------  --------------------------------------
    af-ZA-AdriNeural                   Female    General                Friendly, Positive
    af-ZA-WillemNeural                 Male      General                Friendly, Positive
    am-ET-AmehaNeural                  Male      General                Friendly, Positive
    am-ET-MekdesNeural                 Female    General                Friendly, Positive
    ar-AE-FatimaNeural                 Female    General                Friendly, Positive
    ar-AE-HamdanNeural                 Male      General                Friendly, Positive
    ar-BH-AliNeural                    Male      General                Friendly, Positive
    ar-BH-LailaNeural                  Female    General                Friendly, Positive
    ar-DZ-AminaNeural                  Female    General                Friendly, Positive
    ar-DZ-IsmaelNeural                 Male      General                Friendly, Positive
    ar-EG-SalmaNeural                  Female    General                Friendly, Positive
    ...

    $ edge-tts --voice ar-EG-SalmaNeural --text "مرحبا كيف حالك؟" --write-media hello_in_arabic.mp3 --write-subtitles hello_in_arabic.srt

### Custom SSML

Support for custom SSML was removed because Microsoft prevents the use of any SSML that could not be generated by Microsoft Edge itself. This means that all the cases where custom SSML would be useful cannot be supported as the service only permits a single `<voice>` tag with a single `<prosody>` tag inside it. Any available customization options that could be used in the `<prosody>` tag are already available from the library or the command line itself.

### Changing rate, volume and pitch

You can change the rate, volume and pitch of the generated speech by using the `--rate`, `--volume` and `--pitch` options. When using a negative value, you will need to use `--[option]=-50%` instead of `--[option] -50%` to avoid the option being interpreted as a command line option.

    $ edge-tts --rate=-50% --text "Hello, world!" --write-media hello_with_rate_lowered.mp3 --write-subtitles hello_with_rate_lowered.srt
    $ edge-tts --volume=-50% --text "Hello, world!" --write-media hello_with_volume_lowered.mp3 --write-subtitles hello_with_volume_lowered.srt
    $ edge-tts --pitch=-50Hz --text "Hello, world!" --write-media hello_with_pitch_lowered.mp3 --write-subtitles hello_with_pitch_lowered.srt

## Python module

It is possible to use the `edge-tts` module directly from Python. Examples from the project itself include:

* [/examples/](/examples/)
* [/src/edge_tts/util.py](/src/edge_tts/util.py)

Other projects that use the `edge-tts` module include:

* [hass-edge-tts](https://github.com/hasscc/hass-edge-tts/blob/main/custom_components/edge_tts/tts.py)
* [Podcastfy](https://github.com/souzatharsis/podcastfy/blob/main/podcastfy/tts/providers/edge.py)
* [tts-samples](https://github.com/yaph/tts-samples/blob/main/bin/create_sound_samples.py) - a collection of [mp3 sound samples](https://github.com/yaph/tts-samples/tree/main/mp3) to facilitate picking a voice for your project.
