"""
测试导入修复
"""

import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_imports():
    """测试各种导入"""
    try:
        print("🔍 测试基础导入...")
        
        # 测试Qt库导入
        try:
            from PySide6.QtWidgets import QApplication
            print("✅ PySide6导入成功")
        except ImportError:
            try:
                from PyQt6.QtWidgets import QApplication
                print("✅ PyQt6导入成功")
            except ImportError:
                print("❌ Qt库导入失败")
                return False
        
        # 测试业务模块导入
        print("🔍 测试业务模块导入...")
        try:
            from business.cookie_manager import CookieManager
            print("✅ CookieManager导入成功")
        except Exception as e:
            print(f"❌ CookieManager导入失败: {e}")
            
        try:
            from business.automation_login import AutomationLoginManager
            print("✅ AutomationLoginManager导入成功")
        except Exception as e:
            print(f"❌ AutomationLoginManager导入失败: {e}")
        
        # 测试UI组件导入
        print("🔍 测试UI组件导入...")
        try:
            from ui.components.account_widget import AccountWidget, AccountDialog
            print("✅ AccountWidget和AccountDialog导入成功")
        except Exception as e:
            print(f"❌ AccountWidget导入失败: {e}")
            import traceback
            traceback.print_exc()
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dialog_creation():
    """测试对话框创建"""
    try:
        print("🔍 测试对话框创建...")
        
        # 创建QApplication
        try:
            from PySide6.QtWidgets import QApplication
        except ImportError:
            from PyQt6.QtWidgets import QApplication
            
        app = QApplication([])
        
        # 导入并创建对话框
        from ui.components.account_widget import AccountDialog
        dialog = AccountDialog()
        
        print("✅ AccountDialog创建成功")
        
        # 测试获取账号数据方法
        try:
            # 设置一些测试数据
            dialog.username_edit.setText("test_user")
            dialog.nickname_edit.setText("测试用户")
            dialog.phone_edit.setText("***********")
            
            account_data = dialog.get_account_data()
            print(f"✅ 获取账号数据成功: {account_data}")
            
        except Exception as e:
            print(f"❌ 获取账号数据失败: {e}")
            
        return True
        
    except Exception as e:
        print(f"❌ 对话框创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("导入修复测试")
    print("=" * 50)
    
    # 测试导入
    import_success = test_imports()
    
    if import_success:
        print("\n" + "=" * 50)
        print("对话框创建测试")
        print("=" * 50)
        
        # 测试对话框创建
        dialog_success = test_dialog_creation()
        
        if dialog_success:
            print("\n🎉 所有测试通过！")
        else:
            print("\n❌ 对话框测试失败")
    else:
        print("\n❌ 导入测试失败")
        
    print("=" * 50)
