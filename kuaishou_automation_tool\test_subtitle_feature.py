#!/usr/bin/env python3
"""
测试字幕功能
"""

import os
import sys
from PIL import Image

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def create_test_images_with_subtitle():
    """创建测试图片"""
    test_dir = os.path.join(current_dir, "test_images")
    os.makedirs(test_dir, exist_ok=True)
    
    # 创建3张测试图片
    colors = [
        (255, 150, 150),  # 浅红色
        (150, 255, 150),  # 浅绿色
        (150, 150, 255),  # 浅蓝色
    ]
    
    texts = [
        "产品展示 - 图片1",
        "产品特点 - 图片2", 
        "购买优惠 - 图片3"
    ]
    
    image_paths = []
    
    for i, (color, text) in enumerate(zip(colors, texts)):
        # 创建1080x1920的竖屏图片
        img = Image.new('RGB', (1080, 1920), color)
        
        # 可以在这里添加文字到图片上（需要PIL的ImageDraw）
        try:
            from PIL import ImageDraw, ImageFont
            draw = ImageDraw.Draw(img)
            
            # 尝试使用默认字体
            try:
                font = ImageFont.truetype("arial.ttf", 80)
            except:
                font = ImageFont.load_default()
            
            # 在图片中央添加文字
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            x = (1080 - text_width) // 2
            y = (1920 - text_height) // 2
            
            draw.text((x, y), text, fill=(255, 255, 255), font=font)
            
        except Exception as e:
            print(f"添加图片文字失败: {e}")
        
        # 保存图片
        img_path = os.path.join(test_dir, f"subtitle_test_{i+1}.jpg")
        img.save(img_path, 'JPEG', quality=95)
        image_paths.append(img_path)
        print(f"✅ 创建测试图片: {img_path}")
    
    return image_paths

def test_subtitle_generation():
    """测试字幕生成功能"""
    try:
        from business.video_composer import VideoComposer
        
        print("🎬 开始测试字幕功能...")
        
        # 创建测试图片
        image_paths = create_test_images_with_subtitle()
        
        # 测试文案（专门设计用于测试字幕）
        test_text = """
        欢迎来到我们的直播间！今天为大家推荐这款超值好物。
        这个产品有三大特点：第一，质量优良，采用进口材料。
        第二，价格实惠，比市场价便宜百分之三十。
        第三，性价比超高，现在下单还有特别优惠。
        机会难得，不要错过！立即下单购买吧！
        """
        
        # 输出路径
        output_path = os.path.join(current_dir, "test_subtitle_video.mp4")
        
        # 创建视频合成器
        composer = VideoComposer()
        
        # 配置参数（启用字幕）
        config = {
            'video_width': 1080,
            'video_height': 1920,
            'fps': 30,
            'image_duration': 3.0,
            'voice_speed': 1.0,
            'voice_volume': 0.8,
            'enable_subtitles': True,
            'subtitle_position': 'bottom_center',
            'subtitle_background': '半透明黑色背景',
            'subtitle_font_size': 48,
            'subtitle_font_color': 'white'
        }
        
        print("🔄 开始合成带字幕的视频...")
        
        # 合成视频
        success = composer.create_video_from_images_and_text(
            images=image_paths,
            text_content=test_text.strip(),
            output_path=output_path,
            config=config
        )
        
        if success:
            print(f"🎉 带字幕视频合成成功！")
            print(f"📁 输出文件: {output_path}")
            
            # 检查文件是否存在
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
                print(f"📊 文件大小: {file_size:.2f} MB")
                
                print("\n🎯 字幕功能测试要点:")
                print("• 检查视频底部是否有字幕显示")
                print("• 字幕是否与语音同步")
                print("• 字幕背景是否为半透明黑色")
                print("• 字幕文字是否清晰可读")
                
            else:
                print("⚠️ 输出文件不存在")
        else:
            print("❌ 带字幕视频合成失败")
            
        return success
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_different_subtitle_styles():
    """测试不同的字幕样式"""
    print("\n🎨 测试不同字幕样式...")
    
    # 不同的字幕配置
    subtitle_configs = [
        {
            'name': '底部白字黑底',
            'config': {
                'subtitle_position': 'bottom_center',
                'subtitle_background': '半透明黑色背景',
                'subtitle_font_color': 'white',
                'subtitle_font_size': 48
            }
        },
        {
            'name': '顶部黄字无背景',
            'config': {
                'subtitle_position': 'top_center',
                'subtitle_background': '无背景',
                'subtitle_font_color': 'yellow',
                'subtitle_font_size': 52
            }
        },
        {
            'name': '中间红字白底',
            'config': {
                'subtitle_position': 'center',
                'subtitle_background': '白色半透明背景',
                'subtitle_font_color': 'red',
                'subtitle_font_size': 44
            }
        }
    ]
    
    print("📋 可用的字幕样式配置:")
    for i, style in enumerate(subtitle_configs, 1):
        print(f"{i}. {style['name']}:")
        config = style['config']
        print(f"   位置: {config['subtitle_position']}")
        print(f"   背景: {config['subtitle_background']}")
        print(f"   颜色: {config['subtitle_font_color']}")
        print(f"   大小: {config['subtitle_font_size']}px")
        print()

def main():
    """主函数"""
    print("🎬 字幕功能测试工具")
    print("=" * 50)
    print("这个工具用于测试图文视频合成的字幕功能。")
    print()
    
    # 测试字幕样式选项
    test_different_subtitle_styles()
    
    # 测试字幕生成
    success = test_subtitle_generation()
    
    print("\n" + "=" * 50)
    print("📋 字幕功能说明:")
    print("✅ 支持的字幕位置:")
    print("  • 底部居中、顶部居中、中间居中")
    print("  • 底部左对齐、底部右对齐")
    
    print("\n✅ 支持的背景样式:")
    print("  • 半透明黑色背景（推荐）")
    print("  • 纯色黑色背景")
    print("  • 无背景")
    print("  • 白色半透明背景")
    print("  • 彩色渐变背景")
    
    print("\n✅ 支持的字体颜色:")
    print("  • 白色（推荐）、黄色、红色")
    print("  • 蓝色、绿色、黑色")
    
    print("\n✅ 字体大小: 20-100px可调")
    
    if success:
        print("\n🎉 字幕功能测试完成！")
        print("现在可以在主界面的高级设置中配置字幕样式。")
    else:
        print("\n❌ 字幕功能测试失败，请检查错误信息")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        input("\n按回车键退出...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
        sys.exit(1)
