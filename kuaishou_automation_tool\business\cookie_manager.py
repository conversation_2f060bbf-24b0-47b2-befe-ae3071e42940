"""
快手无人带货工具 - Cookie管理模块
Kuaishou Automation Tool - Cookie Manager

本模块实现Cookie的存储、读取、管理等核心业务逻辑，包括：
- Cookie文件的创建和保存
- Cookie数据的读取和验证
- Cookie文件的管理和清理
- 与账号数据的关联管理
"""

import os
import json
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from pathlib import Path

try:
    from ..data.models.base import ConfigManager
    from ..utils.logger import get_logger
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from data.models.base import ConfigManager
    from utils.logger import get_logger


class CookieManager:
    """
    Cookie管理器类
    
    负责Cookie文件的完整生命周期管理，包括存储、读取、验证、清理等操作。
    采用文件系统存储，支持自定义存储路径。
    
    Attributes:
        config_manager (ConfigManager): 配置管理器
        logger: 日志记录器
        default_storage_path (str): 默认存储路径
    """
    
    def __init__(self, config_manager: ConfigManager = None):
        """
        初始化Cookie管理器
        
        Args:
            config_manager (ConfigManager, optional): 配置管理器实例
        """
        self.config_manager = config_manager or ConfigManager()
        self.logger = get_logger("CookieManager")
        self.default_storage_path = self._get_default_storage_path()
        
        # 确保存储目录存在
        self._ensure_storage_directory()
        
    def _get_default_storage_path(self) -> str:
        """
        获取默认存储路径
        
        Returns:
            str: 默认Cookie存储路径
        """
        # 从配置中获取，如果没有则使用默认路径
        configured_path = self.config_manager.get('automation.cookie_storage_path')
        if configured_path and os.path.isabs(configured_path):
            return configured_path
        
        # 默认路径：项目根目录下的cookies文件夹
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        return os.path.join(project_root, 'cookies')
        
    def _ensure_storage_directory(self):
        """确保存储目录存在"""
        try:
            os.makedirs(self.default_storage_path, exist_ok=True)
            self.logger.info(f"Cookie存储目录已准备: {self.default_storage_path}")
        except Exception as e:
            self.logger.error(f"创建Cookie存储目录失败: {e}")
            raise
            
    def get_storage_path(self) -> str:
        """
        获取当前存储路径
        
        Returns:
            str: 当前Cookie存储路径
        """
        return self.default_storage_path
        
    def set_storage_path(self, path: str) -> bool:
        """
        设置存储路径
        
        Args:
            path (str): 新的存储路径
            
        Returns:
            bool: 设置是否成功
        """
        try:
            # 验证路径
            if not os.path.isabs(path):
                raise ValueError("存储路径必须是绝对路径")
                
            # 创建目录
            os.makedirs(path, exist_ok=True)
            
            # 更新配置
            self.config_manager.set('automation.cookie_storage_path', path)
            self.config_manager.save_config()
            
            # 更新实例变量
            self.default_storage_path = path
            
            self.logger.info(f"Cookie存储路径已更新: {path}")
            return True
            
        except Exception as e:
            self.logger.error(f"设置存储路径失败: {e}")
            return False
            
    def save_cookies(self, phone: str, cookies: List[Dict[str, Any]], 
                    custom_path: str = None) -> Optional[str]:
        """
        保存Cookie到文件
        
        Args:
            phone (str): 手机号（用于文件命名）
            cookies (List[Dict]): Cookie数据列表
            custom_path (str, optional): 自定义保存路径
            
        Returns:
            str: 保存的文件路径，失败返回None
        """
        try:
            # 验证输入
            if not phone or not cookies:
                raise ValueError("手机号和Cookie数据不能为空")
                
            # 确定保存路径
            storage_path = custom_path or self.default_storage_path
            filename = f"{phone}.txt"
            file_path = os.path.join(storage_path, filename)
            
            # 准备Cookie数据
            cookie_data = {
                'phone': phone,
                'cookies': cookies,
                'created_time': datetime.now().isoformat(),
                'domain': 'kuaishou.com',
                'user_agent': self._get_default_user_agent()
            }
            
            # 保存到文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(cookie_data, f, ensure_ascii=False, indent=2)
                
            self.logger.info(f"Cookie已保存: {file_path}")
            return file_path
            
        except Exception as e:
            self.logger.error(f"保存Cookie失败: {e}")
            return None
            
    def load_cookies(self, phone: str, custom_path: str = None) -> Optional[Dict[str, Any]]:
        """
        从文件加载Cookie
        
        Args:
            phone (str): 手机号
            custom_path (str, optional): 自定义文件路径
            
        Returns:
            Dict: Cookie数据，失败返回None
        """
        try:
            # 确定文件路径
            if custom_path:
                file_path = custom_path
            else:
                filename = f"{phone}.txt"
                file_path = os.path.join(self.default_storage_path, filename)
                
            # 检查文件是否存在
            if not os.path.exists(file_path):
                self.logger.warning(f"Cookie文件不存在: {file_path}")
                return None
                
            # 读取文件
            with open(file_path, 'r', encoding='utf-8') as f:
                cookie_data = json.load(f)
                
            # 验证数据格式
            if not self._validate_cookie_data(cookie_data):
                self.logger.error(f"Cookie文件格式无效: {file_path}")
                return None
                
            self.logger.info(f"Cookie已加载: {file_path}")
            return cookie_data
            
        except Exception as e:
            self.logger.error(f"加载Cookie失败: {e}")
            return None
            
    def _validate_cookie_data(self, cookie_data: Dict[str, Any]) -> bool:
        """
        验证Cookie数据格式
        
        Args:
            cookie_data (Dict): Cookie数据
            
        Returns:
            bool: 数据是否有效
        """
        required_fields = ['phone', 'cookies', 'created_time']
        return all(field in cookie_data for field in required_fields)
        
    def _get_default_user_agent(self) -> str:
        """
        获取默认User-Agent
        
        Returns:
            str: 默认User-Agent字符串
        """
        return ("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
                "(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
                
    def is_cookie_valid(self, phone: str, max_age_days: int = 30) -> bool:
        """
        检查Cookie是否有效
        
        Args:
            phone (str): 手机号
            max_age_days (int): 最大有效天数
            
        Returns:
            bool: Cookie是否有效
        """
        try:
            cookie_data = self.load_cookies(phone)
            if not cookie_data:
                return False
                
            # 检查创建时间
            created_time = datetime.fromisoformat(cookie_data['created_time'])
            max_age = timedelta(days=max_age_days)
            
            return datetime.now() - created_time < max_age
            
        except Exception as e:
            self.logger.error(f"检查Cookie有效性失败: {e}")
            return False
            
    def delete_cookies(self, phone: str) -> bool:
        """
        删除Cookie文件
        
        Args:
            phone (str): 手机号
            
        Returns:
            bool: 删除是否成功
        """
        try:
            filename = f"{phone}.txt"
            file_path = os.path.join(self.default_storage_path, filename)
            
            if os.path.exists(file_path):
                os.remove(file_path)
                self.logger.info(f"Cookie文件已删除: {file_path}")
                return True
            else:
                self.logger.warning(f"Cookie文件不存在: {file_path}")
                return False
                
        except Exception as e:
            self.logger.error(f"删除Cookie失败: {e}")
            return False
            
    def list_cookie_files(self) -> List[Dict[str, Any]]:
        """
        列出所有Cookie文件
        
        Returns:
            List[Dict]: Cookie文件信息列表
        """
        try:
            cookie_files = []
            
            if not os.path.exists(self.default_storage_path):
                return cookie_files
                
            for filename in os.listdir(self.default_storage_path):
                if filename.endswith('.txt'):
                    file_path = os.path.join(self.default_storage_path, filename)
                    phone = filename[:-4]  # 移除.txt扩展名
                    
                    # 获取文件信息
                    stat = os.stat(file_path)
                    file_info = {
                        'phone': phone,
                        'filename': filename,
                        'file_path': file_path,
                        'size': stat.st_size,
                        'modified_time': datetime.fromtimestamp(stat.st_mtime),
                        'is_valid': self.is_cookie_valid(phone)
                    }
                    cookie_files.append(file_info)
                    
            # 按修改时间排序
            cookie_files.sort(key=lambda x: x['modified_time'], reverse=True)
            return cookie_files
            
        except Exception as e:
            self.logger.error(f"列出Cookie文件失败: {e}")
            return []
            
    def cleanup_expired_cookies(self, max_age_days: int = 30) -> int:
        """
        清理过期的Cookie文件
        
        Args:
            max_age_days (int): 最大有效天数
            
        Returns:
            int: 清理的文件数量
        """
        try:
            cleaned_count = 0
            cookie_files = self.list_cookie_files()
            
            for file_info in cookie_files:
                if not file_info['is_valid']:
                    if self.delete_cookies(file_info['phone']):
                        cleaned_count += 1
                        
            self.logger.info(f"已清理 {cleaned_count} 个过期Cookie文件")
            return cleaned_count
            
        except Exception as e:
            self.logger.error(f"清理过期Cookie失败: {e}")
            return 0
            
    def get_cookie_statistics(self) -> Dict[str, Any]:
        """
        获取Cookie统计信息
        
        Returns:
            Dict: 统计信息
        """
        try:
            cookie_files = self.list_cookie_files()
            
            total_count = len(cookie_files)
            valid_count = sum(1 for f in cookie_files if f['is_valid'])
            expired_count = total_count - valid_count
            
            total_size = sum(f['size'] for f in cookie_files)
            
            return {
                'total_count': total_count,
                'valid_count': valid_count,
                'expired_count': expired_count,
                'total_size': total_size,
                'storage_path': self.default_storage_path
            }
            
        except Exception as e:
            self.logger.error(f"获取Cookie统计信息失败: {e}")
            return {
                'total_count': 0,
                'valid_count': 0,
                'expired_count': 0,
                'total_size': 0,
                'storage_path': self.default_storage_path
            }
