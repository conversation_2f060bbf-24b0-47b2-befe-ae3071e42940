"""
快手无人带货工具 - Cookie管理模块
Kuaishou Automation Tool - Cookie Manager

本模块实现Cookie的存储、读取、管理等核心业务逻辑，包括：
- Cookie文件的创建和保存
- Cookie数据的读取和验证
- Cookie文件的管理和清理
- 与账号数据的关联管理
"""

import os
import json
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from pathlib import Path

try:
    from ..data.models.base import ConfigManager
    from ..utils.logger import get_logger
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from data.models.base import ConfigManager
    from utils.logger import get_logger


class CookieManager:
    """
    Cookie管理器类
    
    负责Cookie文件的完整生命周期管理，包括存储、读取、验证、清理等操作。
    采用文件系统存储，支持自定义存储路径。
    
    Attributes:
        config_manager (ConfigManager): 配置管理器
        logger: 日志记录器
        default_storage_path (str): 默认存储路径
    """
    
    def __init__(self, config_manager: ConfigManager = None):
        """
        初始化Cookie管理器
        
        Args:
            config_manager (ConfigManager, optional): 配置管理器实例
        """
        self.config_manager = config_manager or ConfigManager()
        self.logger = get_logger("CookieManager")
        self.default_storage_path = self._get_default_storage_path()
        
        # 确保存储目录存在
        self._ensure_storage_directory()
        
    def _get_default_storage_path(self) -> str:
        """
        获取默认存储路径
        
        Returns:
            str: 默认Cookie存储路径
        """
        # 从配置中获取，如果没有则使用默认路径
        configured_path = self.config_manager.get('automation.cookie_storage_path')
        if configured_path and os.path.isabs(configured_path):
            return configured_path
        
        # 默认路径：项目根目录下的cookies文件夹
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        return os.path.join(project_root, 'cookies')
        
    def _ensure_storage_directory(self):
        """确保存储目录存在"""
        try:
            os.makedirs(self.default_storage_path, exist_ok=True)
            self.logger.info(f"Cookie存储目录已准备: {self.default_storage_path}")
        except Exception as e:
            self.logger.error(f"创建Cookie存储目录失败: {e}")
            raise
            
    def get_storage_path(self) -> str:
        """
        获取当前存储路径
        
        Returns:
            str: 当前Cookie存储路径
        """
        return self.default_storage_path
        
    def set_storage_path(self, path: str) -> bool:
        """
        设置存储路径
        
        Args:
            path (str): 新的存储路径
            
        Returns:
            bool: 设置是否成功
        """
        try:
            # 验证路径
            if not os.path.isabs(path):
                raise ValueError("存储路径必须是绝对路径")
                
            # 创建目录
            os.makedirs(path, exist_ok=True)
            
            # 更新配置
            self.config_manager.set('automation.cookie_storage_path', path)
            self.config_manager.save_config()
            
            # 更新实例变量
            self.default_storage_path = path
            
            self.logger.info(f"Cookie存储路径已更新: {path}")
            return True
            
        except Exception as e:
            self.logger.error(f"设置存储路径失败: {e}")
            return False
            
    def save_cookies(self, phone: str, cookies: List[Dict[str, Any]],
                    custom_path: str = None, domain_info: str = None) -> Optional[str]:
        """
        保存Cookie到文件，支持双域名信息

        Args:
            phone (str): 手机号（用于文件命名）
            cookies (List[Dict]): Cookie数据列表
            custom_path (str, optional): 自定义保存路径
            domain_info (str, optional): 域名信息，用于标识Cookie来源

        Returns:
            str: 保存的文件路径，失败返回None
        """
        try:
            # 验证输入
            if not phone or not cookies:
                raise ValueError("手机号和Cookie数据不能为空")

            # 确定保存路径
            storage_path = custom_path or self.default_storage_path
            filename = f"{phone}.txt"
            file_path = os.path.join(storage_path, filename)

            # 分析Cookie域名信息
            detected_domains = self._analyze_cookie_domains(cookies)

            # 准备Cookie数据，增强域名支持
            cookie_data = {
                'phone': phone,
                'cookies': cookies,
                'created_time': datetime.now().isoformat(),
                'domain': domain_info or detected_domains.get('primary', 'kuaishou.com'),
                'supported_domains': detected_domains.get('all', ['kuaishou.com']),
                'dual_domain_support': True,  # 标记支持双域名
                'user_agent': self._get_default_user_agent(),
                'cookie_analysis': {
                    'total_count': len(cookies),
                    'universal_cookies': detected_domains.get('universal', []),
                    'domain_specific_cookies': detected_domains.get('specific', {}),
                    'conversion_capable': detected_domains.get('convertible', True)
                }
            }

            # 保存到文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(cookie_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"Cookie已保存: {file_path} (支持域名: {cookie_data['supported_domains']})")
            return file_path

        except Exception as e:
            self.logger.error(f"保存Cookie失败: {e}")
            return None

    def _analyze_cookie_domains(self, cookies: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        分析Cookie的域名信息和转换能力

        Args:
            cookies (List[Dict]): Cookie列表

        Returns:
            Dict: 域名分析结果
        """
        try:
            # 定义Cookie分类
            universal_cookies = {'userId', 'bUserId', 'did', '_did', 'clientid', 'client_key'}
            kuaishou_cookies = {'kuaishou.web.cp.api_ph', 'kuaishou.web.cp.api_st', 'kuaishou.web.st', 'kuaishou.web.ph'}
            kwaixiaodian_cookies = {'sid', 'kuaishou.shop.b_ph', 'kuaishou.shop.b_st', 'kuaishou.shop.st', 'kuaishou.shop.ph'}

            analysis = {
                'universal': [],
                'specific': {'kuaishou.com': [], 'kwaixiaodian.com': []},
                'all': set(),
                'primary': 'kuaishou.com',
                'convertible': False
            }

            kuaishou_count = 0
            kwaixiaodian_count = 0

            for cookie in cookies:
                if isinstance(cookie, dict) and 'name' in cookie:
                    cookie_name = cookie['name']
                    cookie_domain = cookie.get('domain', '')

                    # 记录域名
                    if 'kuaishou.com' in cookie_domain:
                        analysis['all'].add('kuaishou.com')
                    elif 'kwaixiaodian.com' in cookie_domain:
                        analysis['all'].add('kwaixiaodian.com')

                    # 分类Cookie
                    if cookie_name in universal_cookies:
                        analysis['universal'].append(cookie_name)
                    elif cookie_name in kuaishou_cookies:
                        analysis['specific']['kuaishou.com'].append(cookie_name)
                        kuaishou_count += 1
                    elif cookie_name in kwaixiaodian_cookies:
                        analysis['specific']['kwaixiaodian.com'].append(cookie_name)
                        kwaixiaodian_count += 1

            # 确定主域名
            if kwaixiaodian_count > kuaishou_count:
                analysis['primary'] = 'kwaixiaodian.com'

            # 转换为列表
            analysis['all'] = list(analysis['all']) if analysis['all'] else ['kuaishou.com']

            # 判断是否可转换
            analysis['convertible'] = len(analysis['universal']) > 0 or kuaishou_count > 0 or kwaixiaodian_count > 0

            return analysis

        except Exception as e:
            self.logger.error(f"分析Cookie域名失败: {e}")
            return {
                'universal': [],
                'specific': {'kuaishou.com': [], 'kwaixiaodian.com': []},
                'all': ['kuaishou.com'],
                'primary': 'kuaishou.com',
                'convertible': False
            }
            
    def load_cookies(self, phone: str, custom_path: str = None) -> Optional[Dict[str, Any]]:
        """
        从文件加载Cookie
        
        Args:
            phone (str): 手机号
            custom_path (str, optional): 自定义文件路径
            
        Returns:
            Dict: Cookie数据，失败返回None
        """
        try:
            # 确定文件路径
            if custom_path:
                file_path = custom_path
            else:
                filename = f"{phone}.txt"
                file_path = os.path.join(self.default_storage_path, filename)
                
            # 检查文件是否存在
            if not os.path.exists(file_path):
                self.logger.warning(f"Cookie文件不存在: {file_path}")
                return None
                
            # 读取文件
            with open(file_path, 'r', encoding='utf-8') as f:
                cookie_data = json.load(f)
                
            # 验证数据格式
            if not self._validate_cookie_data(cookie_data):
                self.logger.error(f"Cookie文件格式无效: {file_path}")
                return None
                
            self.logger.info(f"Cookie已加载: {file_path}")
            return cookie_data
            
        except Exception as e:
            self.logger.error(f"加载Cookie失败: {e}")
            return None
            
    def _validate_cookie_data(self, cookie_data: Dict[str, Any]) -> bool:
        """
        验证Cookie数据格式
        
        Args:
            cookie_data (Dict): Cookie数据
            
        Returns:
            bool: 数据是否有效
        """
        required_fields = ['phone', 'cookies', 'created_time']
        return all(field in cookie_data for field in required_fields)
        
    def _get_default_user_agent(self) -> str:
        """
        获取默认User-Agent
        
        Returns:
            str: 默认User-Agent字符串
        """
        return ("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
                "(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
                
    def is_cookie_valid(self, phone: str, max_age_days: int = 30) -> bool:
        """
        检查Cookie是否有效

        Args:
            phone (str): 手机号
            max_age_days (int): 最大有效天数

        Returns:
            bool: Cookie是否有效
        """
        try:
            cookie_data = self.load_cookies(phone)
            if not cookie_data:
                return False

            # 检查创建时间
            created_time = datetime.fromisoformat(cookie_data['created_time'])
            max_age = timedelta(days=max_age_days)

            return datetime.now() - created_time < max_age

        except Exception as e:
            self.logger.error(f"检查Cookie有效性失败: {e}")
            return False

    def get_cookie_domain_info(self, phone: str) -> Optional[Dict[str, Any]]:
        """
        获取Cookie的域名支持信息

        Args:
            phone (str): 手机号

        Returns:
            Dict: 域名支持信息，包含支持的域名列表和转换能力
        """
        try:
            cookie_data = self.load_cookies(phone)
            if not cookie_data:
                return None

            # 返回域名支持信息
            domain_info = {
                'primary_domain': cookie_data.get('domain', 'kuaishou.com'),
                'supported_domains': cookie_data.get('supported_domains', ['kuaishou.com']),
                'dual_domain_support': cookie_data.get('dual_domain_support', False),
                'conversion_capable': cookie_data.get('cookie_analysis', {}).get('conversion_capable', False),
                'universal_cookies': cookie_data.get('cookie_analysis', {}).get('universal_cookies', []),
                'domain_specific_cookies': cookie_data.get('cookie_analysis', {}).get('domain_specific_cookies', {}),
                'total_cookies': cookie_data.get('cookie_analysis', {}).get('total_count', 0)
            }

            self.logger.info(f"Cookie域名信息: {phone} - 支持域名: {domain_info['supported_domains']}")
            return domain_info

        except Exception as e:
            self.logger.error(f"获取Cookie域名信息失败: {e}")
            return None

    def can_convert_to_domain(self, phone: str, target_domain: str) -> bool:
        """
        检查Cookie是否可以转换到指定域名

        Args:
            phone (str): 手机号
            target_domain (str): 目标域名

        Returns:
            bool: 是否可以转换
        """
        try:
            domain_info = self.get_cookie_domain_info(phone)
            if not domain_info:
                return False

            # 检查是否支持双域名
            if not domain_info['dual_domain_support']:
                return target_domain in domain_info['supported_domains']

            # 检查是否有转换能力
            if not domain_info['conversion_capable']:
                return False

            # 支持的目标域名
            supported_targets = ['kuaishou.com', 'kwaixiaodian.com']
            clean_target = target_domain.replace('.', '').replace('www.', '')

            return any(supported in clean_target for supported in supported_targets)

        except Exception as e:
            self.logger.error(f"检查Cookie转换能力失败: {e}")
            return False

    def auto_convert_cookies_for_domain(self, phone: str, target_domain: str) -> Optional[str]:
        """
        自动转换Cookie到目标域名并保存为新的Cookie文件

        Args:
            phone (str): 原始手机号
            target_domain (str): 目标域名 ('kuaishou.com' 或 'kwaixiaodian.com')

        Returns:
            str: 转换后的Cookie文件路径，失败返回None
        """
        try:
            # 加载原始Cookie
            original_cookie_data = self.load_cookies(phone)
            if not original_cookie_data:
                self.logger.error(f"无法加载原始Cookie: {phone}")
                return None

            original_cookies = original_cookie_data.get('cookies', [])
            if not original_cookies:
                self.logger.error(f"原始Cookie为空: {phone}")
                return None

            # 执行Cookie转换
            converted_cookies = self._convert_cookies_to_target_domain(original_cookies, target_domain)
            if not converted_cookies:
                self.logger.error(f"Cookie转换失败: {phone} -> {target_domain}")
                return None

            # 生成新的文件名
            target_suffix = "creator" if "kuaishou.com" in target_domain else "shop"
            new_phone = f"{phone}_{target_suffix}"

            # 保存转换后的Cookie
            converted_file_path = self.save_cookies(
                phone=new_phone,
                cookies=converted_cookies,
                domain_info=target_domain
            )

            if converted_file_path:
                self.logger.info(f"Cookie自动转换成功: {phone} -> {new_phone} (域名: {target_domain})")
                return converted_file_path
            else:
                self.logger.error(f"转换后Cookie保存失败: {new_phone}")
                return None

        except Exception as e:
            self.logger.error(f"自动转换Cookie失败: {e}")
            return None

    def _convert_cookies_to_target_domain(self, cookies: List[Dict[str, Any]], target_domain: str) -> List[Dict[str, Any]]:
        """
        将Cookie转换到目标域名

        Args:
            cookies (List[Dict]): 原始Cookie列表
            target_domain (str): 目标域名

        Returns:
            List[Dict]: 转换后的Cookie列表
        """
        try:
            self.logger.info(f"开始转换Cookie到目标域名: {target_domain}")
            converted_cookies = []

            # 定义Cookie转换规则
            conversion_rules = {
                'universal_cookies': {
                    'userId', 'bUserId', 'did', '_did', 'clientid', 'client_key'
                },
                'domain_mapping': {
                    # kuaishou.com -> kwaixiaodian.com
                    'kuaishou.web.cp.api_ph': 'kuaishou.shop.b_ph',
                    'kuaishou.web.cp.api_st': 'kuaishou.shop.b_st',
                    'kuaishou.web.st': 'kuaishou.shop.st',
                    'kuaishou.web.ph': 'kuaishou.shop.ph',

                    # kwaixiaodian.com -> kuaishou.com
                    'kuaishou.shop.b_ph': 'kuaishou.web.cp.api_ph',
                    'kuaishou.shop.b_st': 'kuaishou.web.cp.api_st',
                    'kuaishou.shop.st': 'kuaishou.web.st',
                    'kuaishou.shop.ph': 'kuaishou.web.ph',
                    'sid': 'kuaishou.web.cp.api_st'  # 特殊映射
                }
            }

            # 确定目标域名格式
            target_domain_with_dot = f".{target_domain}" if not target_domain.startswith('.') else target_domain

            for cookie in cookies:
                if not isinstance(cookie, dict) or 'name' not in cookie or 'value' not in cookie:
                    continue

                cookie_name = cookie['name']

                # 处理通用Cookie - 直接转换域名
                if cookie_name in conversion_rules['universal_cookies']:
                    converted_cookie = self._create_converted_cookie_with_domain(cookie, target_domain_with_dot)
                    converted_cookies.append(converted_cookie)
                    self.logger.debug(f"转换通用Cookie: {cookie_name} -> {target_domain}")

                # 处理需要映射的Cookie
                elif cookie_name in conversion_rules['domain_mapping']:
                    mapped_name = conversion_rules['domain_mapping'][cookie_name]
                    converted_cookie = self._create_converted_cookie_with_domain(
                        cookie, target_domain_with_dot, new_name=mapped_name
                    )
                    converted_cookies.append(converted_cookie)
                    self.logger.debug(f"映射Cookie: {cookie_name} -> {mapped_name} (域名: {target_domain})")

                # 处理其他Cookie - 尝试直接转换
                else:
                    converted_cookie = self._create_converted_cookie_with_domain(cookie, target_domain_with_dot)
                    converted_cookies.append(converted_cookie)
                    self.logger.debug(f"直接转换Cookie: {cookie_name} -> {target_domain}")

            self.logger.info(f"Cookie转换完成: {len(cookies)} -> {len(converted_cookies)}")
            return converted_cookies

        except Exception as e:
            self.logger.error(f"Cookie转换失败: {e}")
            return []

    def _create_converted_cookie_with_domain(self, original_cookie: Dict[str, Any],
                                           target_domain: str, new_name: str = None) -> Dict[str, Any]:
        """
        创建转换后的Cookie

        Args:
            original_cookie (Dict): 原始Cookie
            target_domain (str): 目标域名
            new_name (str, optional): 新的Cookie名称

        Returns:
            Dict: 转换后的Cookie
        """
        converted_cookie = {
            'name': new_name or original_cookie['name'],
            'value': original_cookie['value'],
            'domain': target_domain,
            'path': original_cookie.get('path', '/'),
        }

        # 保留其他属性
        for attr in ['secure', 'httpOnly', 'sameSite', 'expiry']:
            if attr in original_cookie:
                converted_cookie[attr] = original_cookie[attr]

        return converted_cookie
            
    def delete_cookies(self, phone: str) -> bool:
        """
        删除Cookie文件
        
        Args:
            phone (str): 手机号
            
        Returns:
            bool: 删除是否成功
        """
        try:
            filename = f"{phone}.txt"
            file_path = os.path.join(self.default_storage_path, filename)
            
            if os.path.exists(file_path):
                os.remove(file_path)
                self.logger.info(f"Cookie文件已删除: {file_path}")
                return True
            else:
                self.logger.warning(f"Cookie文件不存在: {file_path}")
                return False
                
        except Exception as e:
            self.logger.error(f"删除Cookie失败: {e}")
            return False
            
    def list_cookie_files(self) -> List[Dict[str, Any]]:
        """
        列出所有Cookie文件
        
        Returns:
            List[Dict]: Cookie文件信息列表
        """
        try:
            cookie_files = []
            
            if not os.path.exists(self.default_storage_path):
                return cookie_files
                
            for filename in os.listdir(self.default_storage_path):
                if filename.endswith('.txt'):
                    file_path = os.path.join(self.default_storage_path, filename)
                    phone = filename[:-4]  # 移除.txt扩展名
                    
                    # 获取文件信息
                    stat = os.stat(file_path)
                    file_info = {
                        'phone': phone,
                        'filename': filename,
                        'file_path': file_path,
                        'size': stat.st_size,
                        'modified_time': datetime.fromtimestamp(stat.st_mtime),
                        'is_valid': self.is_cookie_valid(phone)
                    }
                    cookie_files.append(file_info)
                    
            # 按修改时间排序
            cookie_files.sort(key=lambda x: x['modified_time'], reverse=True)
            return cookie_files
            
        except Exception as e:
            self.logger.error(f"列出Cookie文件失败: {e}")
            return []
            
    def cleanup_expired_cookies(self, max_age_days: int = 30) -> int:
        """
        清理过期的Cookie文件
        
        Args:
            max_age_days (int): 最大有效天数
            
        Returns:
            int: 清理的文件数量
        """
        try:
            cleaned_count = 0
            cookie_files = self.list_cookie_files()
            
            for file_info in cookie_files:
                if not file_info['is_valid']:
                    if self.delete_cookies(file_info['phone']):
                        cleaned_count += 1
                        
            self.logger.info(f"已清理 {cleaned_count} 个过期Cookie文件")
            return cleaned_count
            
        except Exception as e:
            self.logger.error(f"清理过期Cookie失败: {e}")
            return 0
            
    def get_cookie_statistics(self) -> Dict[str, Any]:
        """
        获取Cookie统计信息
        
        Returns:
            Dict: 统计信息
        """
        try:
            cookie_files = self.list_cookie_files()
            
            total_count = len(cookie_files)
            valid_count = sum(1 for f in cookie_files if f['is_valid'])
            expired_count = total_count - valid_count
            
            total_size = sum(f['size'] for f in cookie_files)
            
            return {
                'total_count': total_count,
                'valid_count': valid_count,
                'expired_count': expired_count,
                'total_size': total_size,
                'storage_path': self.default_storage_path
            }
            
        except Exception as e:
            self.logger.error(f"获取Cookie统计信息失败: {e}")
            return {
                'total_count': 0,
                'valid_count': 0,
                'expired_count': 0,
                'total_size': 0,
                'storage_path': self.default_storage_path
            }
