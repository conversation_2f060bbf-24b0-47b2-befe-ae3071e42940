# to be generated a package build time
application_paths = {
    'androiddeployqt': 'androiddeployqt.exe',
    'androidtestrunner': 'androidtestrunner.exe',
    'assistant': 'assistant.exe',
    'designer': 'designer.exe',
    'lconvert': 'lconvert.exe',
    'linguist': 'linguist.exe',
    'lprodump': 'lprodump.exe',
    'lrelease-pro': 'lrelease-pro.exe',
    'lrelease': 'lrelease.exe',
    'lupdate-pro': 'lupdate-pro.exe',
    'lupdate': 'lupdate.exe',
    'pixeltool': 'pixeltool.exe',
    'qdbus': 'qdbus.exe',
    'qdbuscpp2xml': 'qdbuscpp2xml.exe',
    'qdbusviewer': 'qdbusviewer.exe',
    'qdbusxml2cpp': 'qdbusxml2cpp.exe',
    'qdistancefieldgenerator': 'qdistancefieldgenerator.exe',
    'qdoc': 'qdoc.exe',
    'qhelpgenerator': 'qhelpgenerator.exe',
    'qlalr': 'qlalr.exe',
    'qml': 'qml.exe',
    'qmlcachegen': 'qmlcachegen.exe',
    'qmleasing': 'qmleasing.exe',
    'qmlformat': 'qmlformat.exe',
    'qmlimportscanner': 'qmlimportscanner.exe',
    'qmllint': 'qmllint.exe',
    'qmlplugindump': 'qmlplugindump.exe',
    'qmlpreview': 'qmlpreview.exe',
    'qmlprofiler': 'qmlprofiler.exe',
    'qmlscene': 'qmlscene.exe',
    'qmltestrunner': 'qmltestrunner.exe',
    'qmltime': 'qmltime.exe',
    'qmltyperegistrar': 'qmltyperegistrar.exe',
    'qtattributionsscanner': 'qtattributionsscanner.exe',
    'qtdiag': 'qtdiag.exe',
    'qtdiag6': 'qtdiag6.exe',
    'qtpaths': 'qtpaths.exe',
    'qtplugininfo': 'qtplugininfo.exe',
    'qvkgen': 'qvkgen.exe',
    'uic': 'uic.exe',
    'windeployqt': 'windeployqt.exe',
}
