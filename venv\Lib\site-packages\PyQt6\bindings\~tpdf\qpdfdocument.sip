// qpdfdocument.sip generated by MetaSIP
//
// This file is part of the QtPdf Python extension module.
//
// Copyright (c) 2022 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QPdfDocument : QObject
{
%TypeHeaderCode
#include <qpdfdocument.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QPdfBookmarkModel, &sipType_QPdfBookmarkModel, -1, 1},
        {sipName_QPdfDocument, &sipType_QPdfDocument, -1, 2},
        {sipName_QPdfPageNavigator, &sipType_QPdfPageNavigator, -1, 3},
        {sipName_QPdfPageRenderer, &sipType_QPdfPageRenderer, -1, 4},
        {sipName_QPdfSearchModel, &sipType_QPdfSearchModel, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    enum class Status
    {
        Null,
        Loading,
        Ready,
        Unloading,
        Error,
    };

    enum class Error
    {
        None,
        Unknown,
        DataNotYetAvailable,
        FileNotFound,
        InvalidFileFormat,
        IncorrectPassword,
        UnsupportedSecurityScheme,
    };

    enum class MetaDataField
    {
        Title,
        Subject,
        Author,
        Keywords,
        Producer,
        Creator,
        CreationDate,
        ModificationDate,
    };

    enum class PageModelRole
    {
        Label,
        PointSize,
    };

    explicit QPdfDocument(QObject *parent /TransferThis/);
    virtual ~QPdfDocument();
    QPdfDocument::Error load(const QString &fileName) /ReleaseGIL/;
    void load(QIODevice *device) /ReleaseGIL/;
    QPdfDocument::Status status() const;
    void setPassword(const QString &password);
    QString password() const;
    QVariant metaData(QPdfDocument::MetaDataField field) const;
    QPdfDocument::Error error() const;
    void close();
    int pageCount() const;
    QSizeF pagePointSize(int page) const;
    QString pageLabel(int page);
    QAbstractListModel *pageModel();
    QImage render(int page, QSize imageSize, QPdfDocumentRenderOptions options = QPdfDocumentRenderOptions());
    QPdfSelection getSelection(int page, QPointF start, QPointF end);
    QPdfSelection getSelectionAtIndex(int page, int startIndex, int maxLength);
    QPdfSelection getAllText(int page);

signals:
    void passwordChanged();
    void statusChanged(QPdfDocument::Status status);
    void pageCountChanged(int pageCount);
    void pageModelChanged();
};
