"""
快手无人带货工具 - 自动化功能测试
Kuaishou Automation Tool - Automation Feature Test

本模块测试自动化登录和Cookie管理功能，包括：
- Cookie管理器测试
- 自动化登录流程测试
- 数据模型扩展测试
- 集成功能测试
"""

import sys
import os
import unittest
import tempfile
import shutil
from datetime import datetime
import json

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 修复导入路径
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from data.models.base import DatabaseManager, ConfigManager
    from data.models.account import Account, AccountStatus, AccountType
    from data.repositories.account_repository import AccountRepository
    from business.cookie_manager import CookieManager
    from business.automation_login import AutomationLoginManager
    from utils.logger import setup_logger, get_logger
except ImportError as e:
    print(f"导入错误: {e}")
    print("尝试使用绝对导入...")

    # 添加当前目录到路径
    current_path = os.path.dirname(os.path.abspath(__file__))
    if current_path not in sys.path:
        sys.path.insert(0, current_path)

    # 重新尝试导入
    from data.models.base import DatabaseManager, ConfigManager
    from data.models.account import Account, AccountStatus, AccountType
    from data.repositories.account_repository import AccountRepository
    from business.cookie_manager import CookieManager
    from business.automation_login import AutomationLoginManager
    from utils.logger import setup_logger, get_logger


class TestCookieManager(unittest.TestCase):
    """Cookie管理器测试"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时目录用于测试
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建测试配置管理器
        self.config_manager = ConfigManager()
        self.config_manager.set('automation.cookie_storage_path', self.temp_dir)
        
        # 创建Cookie管理器
        self.cookie_manager = CookieManager(self.config_manager)
        
    def tearDown(self):
        """测试后清理"""
        # 删除临时目录
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
            
    def test_cookie_manager_initialization(self):
        """测试Cookie管理器初始化"""
        self.assertIsNotNone(self.cookie_manager)
        self.assertEqual(self.cookie_manager.get_storage_path(), self.temp_dir)
        print("✅ Cookie管理器初始化测试通过")
        
    def test_save_and_load_cookies(self):
        """测试Cookie保存和加载"""
        # 测试数据
        phone = "***********"
        test_cookies = [
            {
                'name': 'session_id',
                'value': 'test_session_123',
                'domain': '.kuaishou.com',
                'path': '/',
                'secure': True
            },
            {
                'name': 'user_token',
                'value': 'test_token_456',
                'domain': '.kuaishou.com',
                'path': '/',
                'secure': True
            }
        ]
        
        # 保存Cookie
        file_path = self.cookie_manager.save_cookies(phone, test_cookies)
        self.assertIsNotNone(file_path)
        self.assertTrue(os.path.exists(file_path))
        
        # 加载Cookie
        loaded_data = self.cookie_manager.load_cookies(phone)
        self.assertIsNotNone(loaded_data)
        self.assertEqual(loaded_data['phone'], phone)
        self.assertEqual(len(loaded_data['cookies']), 2)
        
        print("✅ Cookie保存和加载测试通过")
        
    def test_cookie_validation(self):
        """测试Cookie有效性验证"""
        phone = "***********"
        test_cookies = [{'name': 'test', 'value': 'value'}]
        
        # 保存Cookie
        self.cookie_manager.save_cookies(phone, test_cookies)
        
        # 检查有效性
        is_valid = self.cookie_manager.is_cookie_valid(phone, max_age_days=30)
        self.assertTrue(is_valid)
        
        # 检查不存在的Cookie
        is_valid_nonexistent = self.cookie_manager.is_cookie_valid("99999999999")
        self.assertFalse(is_valid_nonexistent)
        
        print("✅ Cookie有效性验证测试通过")
        
    def test_cookie_statistics(self):
        """测试Cookie统计功能"""
        # 创建多个Cookie文件
        phones = ["***********", "13800138002", "1**********"]
        test_cookies = [{'name': 'test', 'value': 'value'}]
        
        for phone in phones:
            self.cookie_manager.save_cookies(phone, test_cookies)
            
        # 获取统计信息
        stats = self.cookie_manager.get_cookie_statistics()
        self.assertEqual(stats['total_count'], 3)
        self.assertEqual(stats['valid_count'], 3)
        self.assertEqual(stats['expired_count'], 0)
        
        print("✅ Cookie统计功能测试通过")


class TestExtendedAccountModel(unittest.TestCase):
    """扩展账号模型测试"""
    
    def setUp(self):
        """测试前准备"""
        self.db_manager = DatabaseManager()
        self.db_manager.initialize()
        
    def test_account_with_phone_validation(self):
        """测试带手机号的账号验证"""
        account = Account()
        
        # 测试有效手机号
        account.username = "test_user_phone"
        account.nickname = "测试用户"
        account.phone = "***********"
        
        # 应该不抛出异常
        try:
            # 这里模拟验证过程
            self.assertEqual(account.phone, "***********")
            print("✅ 有效手机号验证通过")
        except Exception as e:
            self.fail(f"有效手机号验证失败: {e}")
            
        # 测试无效手机号
        with self.assertRaises(ValueError):
            account.phone = "123456"  # 无效手机号
            
        print("✅ 手机号验证测试通过")
        
    def test_automation_related_methods(self):
        """测试自动化相关方法"""
        account = Account()
        account.username = "test_automation"
        account.nickname = "自动化测试"
        account.phone = "***********"
        
        # 测试Cookie文件名生成
        cookie_filename = account.get_cookie_filename()
        self.assertEqual(cookie_filename, "***********.txt")
        
        # 测试自动化信息更新
        account.update_automation_info("/path/to/cookie.txt", "已配置")
        self.assertEqual(account.automation_status, "已配置")
        self.assertEqual(account.cookie_file_path, "/path/to/cookie.txt")
        self.assertIsNotNone(account.last_automation_time)
        
        print("✅ 自动化相关方法测试通过")


class TestAutomationLoginManager(unittest.TestCase):
    """自动化登录管理器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_manager = ConfigManager()
        self.config_manager.set('automation.cookie_storage_path', self.temp_dir)
        
        self.cookie_manager = CookieManager(self.config_manager)
        self.automation_manager = AutomationLoginManager(self.cookie_manager)
        
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
            
    def test_automation_manager_initialization(self):
        """测试自动化管理器初始化"""
        self.assertIsNotNone(self.automation_manager)
        self.assertIsNotNone(self.automation_manager.cookie_manager)
        self.assertEqual(self.automation_manager.login_url, "https://cp.kuaishou.com/profile")
        print("✅ 自动化管理器初始化测试通过")
        
    def test_webdriver_setup_test(self):
        """测试WebDriver设置（如果环境支持）"""
        try:
            # 尝试测试WebDriver设置
            test_result = self.automation_manager.test_webdriver_setup()
            if test_result:
                print("✅ WebDriver设置测试通过")
            else:
                print("⚠️ WebDriver设置测试失败（可能是环境问题）")
        except Exception as e:
            print(f"⚠️ WebDriver测试跳过（环境不支持）: {e}")
            
    def test_timeout_setting(self):
        """测试超时设置"""
        # 测试设置超时时间
        self.automation_manager.set_timeout(600)
        self.assertEqual(self.automation_manager.timeout_seconds, 600)
        
        # 测试最小超时限制
        self.automation_manager.set_timeout(30)
        self.assertEqual(self.automation_manager.timeout_seconds, 60)  # 应该被限制为最小值
        
        print("✅ 超时设置测试通过")


class TestAccountRepository(unittest.TestCase):
    """账号仓库扩展功能测试"""
    
    def setUp(self):
        """测试前准备"""
        self.db_manager = DatabaseManager()
        self.db_manager.initialize()
        self.repository = AccountRepository()
        
    def test_create_account_with_phone(self):
        """测试创建带手机号的账号"""
        account_data = {
            'username': f'test_phone_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
            'nickname': '手机号测试账号',
            'phone': '***********',
            'email': '<EMAIL>',
            'remarks': '自动化功能测试账号'
        }
        
        try:
            created_account = self.repository.create(account_data)
            self.assertIsNotNone(created_account)
            self.assertEqual(created_account.phone, '***********')
            print(f"✅ 带手机号账号创建成功，ID: {created_account.id}")
            
            # 测试Cookie文件名生成
            cookie_filename = created_account.get_cookie_filename()
            self.assertEqual(cookie_filename, "***********.txt")
            
        except Exception as e:
            self.fail(f"创建带手机号账号失败: {e}")


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_manager = ConfigManager()
        self.config_manager.set('automation.cookie_storage_path', self.temp_dir)
        
        self.db_manager = DatabaseManager()
        self.db_manager.initialize()
        
        self.cookie_manager = CookieManager(self.config_manager)
        self.repository = AccountRepository()
        
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
            
    def test_complete_workflow(self):
        """测试完整工作流程"""
        # 1. 创建账号
        account_data = {
            'username': f'integration_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
            'nickname': '集成测试账号',
            'phone': '***********',
            'email': '<EMAIL>',
            'remarks': '完整工作流程测试'
        }
        
        created_account = self.repository.create(account_data)
        self.assertIsNotNone(created_account)
        print(f"✅ 步骤1: 账号创建成功，ID: {created_account.id}")
        
        # 2. 模拟Cookie保存
        test_cookies = [
            {
                'name': 'session_id',
                'value': 'integration_test_session',
                'domain': '.kuaishou.com'
            }
        ]
        
        cookie_file_path = self.cookie_manager.save_cookies(
            created_account.phone, test_cookies
        )
        self.assertIsNotNone(cookie_file_path)
        print("✅ 步骤2: Cookie保存成功")
        
        # 3. 更新账号自动化信息
        update_data = {
            'cookie_file_path': cookie_file_path,
            'automation_status': '已配置'
        }
        
        updated_account = self.repository.update(created_account.id, update_data)
        self.assertIsNotNone(updated_account)
        self.assertEqual(updated_account.automation_status, '已配置')
        print("✅ 步骤3: 账号自动化信息更新成功")
        
        # 4. 验证Cookie文件存在
        loaded_cookies = self.cookie_manager.load_cookies(created_account.phone)
        self.assertIsNotNone(loaded_cookies)
        self.assertEqual(len(loaded_cookies['cookies']), 1)
        print("✅ 步骤4: Cookie文件验证成功")
        
        # 5. 获取统计信息
        stats = self.cookie_manager.get_cookie_statistics()
        self.assertGreaterEqual(stats['total_count'], 1)
        print("✅ 步骤5: 统计信息获取成功")
        
        print("🎉 完整工作流程测试通过！")


def run_automation_tests():
    """运行自动化功能测试"""
    print("=" * 70)
    print("快手无人带货工具 - 自动化功能测试")
    print("=" * 70)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_classes = [
        TestCookieManager,
        TestExtendedAccountModel,
        TestAutomationLoginManager,
        TestAccountRepository,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    print("\n" + "=" * 70)
    if result.wasSuccessful():
        print("🎉 所有自动化功能测试通过！")
        print("✅ Cookie管理功能正常")
        print("✅ 自动化登录模块正常")
        print("✅ 数据模型扩展正常")
        print("✅ 集成功能正常")
    else:
        print("❌ 部分自动化功能测试失败！")
        print(f"失败数量: {len(result.failures)}")
        print(f"错误数量: {len(result.errors)}")
        
        # 输出失败详情
        if result.failures:
            print("\n失败详情:")
            for test, traceback in result.failures:
                print(f"- {test}: {traceback}")
                
        if result.errors:
            print("\n错误详情:")
            for test, traceback in result.errors:
                print(f"- {test}: {traceback}")
    
    print("=" * 70)
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_automation_tests()
    
    if success:
        print("\n🚀 自动化功能验证完成，所有功能正常！")
        print("💡 提示：")
        print("   - Cookie管理功能已就绪")
        print("   - 自动化登录流程已实现")
        print("   - 数据模型已扩展支持新功能")
        print("   - 可以开始使用自动化登录功能")
        exit_code = 0
    else:
        print("\n⚠️ 自动化功能测试失败，请检查相关问题。")
        exit_code = 1
        
    sys.exit(exit_code)
