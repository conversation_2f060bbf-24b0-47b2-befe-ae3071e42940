"""
快手无人带货工具 - 核心功能测试
Kuaishou Automation Tool - Core Function Test

本模块测试应用程序的核心功能，不依赖GUI组件：
- 数据库连接测试
- 数据模型测试
- 配置管理测试
- 业务逻辑测试
"""

import sys
import os
import unittest
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from data.models.base import DatabaseManager, ConfigManager
from data.models.account import Account, AccountStatus, AccountType, AccountStatistics
from data.repositories.account_repository import AccountRepository
from utils.logger import setup_logger, get_logger


class TestDatabaseManager(unittest.TestCase):
    """数据库管理器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.db_manager = DatabaseManager()
        
    def test_singleton_pattern(self):
        """测试单例模式"""
        db_manager2 = DatabaseManager()
        self.assertIs(self.db_manager, db_manager2)
        
    def test_database_initialization(self):
        """测试数据库初始化"""
        try:
            self.db_manager.initialize()
            self.assertIsNotNone(self.db_manager.engine)
            self.assertIsNotNone(self.db_manager.SessionLocal)
            print("✅ 数据库初始化成功")
        except Exception as e:
            self.fail(f"数据库初始化失败: {e}")
            
    def test_session_creation(self):
        """测试会话创建"""
        try:
            self.db_manager.initialize()
            session = self.db_manager.get_session()
            self.assertIsNotNone(session)
            session.close()
            print("✅ 数据库会话创建成功")
        except Exception as e:
            self.fail(f"数据库会话创建失败: {e}")


class TestConfigManager(unittest.TestCase):
    """配置管理器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.config_manager = ConfigManager()
        
    def test_config_loading(self):
        """测试配置加载"""
        self.assertIsNotNone(self.config_manager)
        
        # 测试获取默认配置
        app_name = self.config_manager.get('app.name')
        self.assertIsNotNone(app_name)
        print(f"✅ 配置加载成功，应用名称: {app_name}")
        
    def test_config_operations(self):
        """测试配置操作"""
        # 测试设置配置
        test_key = 'test.timestamp'
        test_value = datetime.now().isoformat()
        
        self.config_manager.set(test_key, test_value)
        retrieved_value = self.config_manager.get(test_key)
        self.assertEqual(retrieved_value, test_value)
        
        # 测试默认值
        default_value = self.config_manager.get('non.existent.key', 'default_test')
        self.assertEqual(default_value, 'default_test')
        
        print("✅ 配置操作测试通过")


class TestAccountModel(unittest.TestCase):
    """账号模型测试"""
    
    def test_account_creation(self):
        """测试账号创建"""
        account = Account()
        account.username = "test_user_001"
        account.nickname = "测试用户001"
        account.status = AccountStatus.OFFLINE
        account.account_type = AccountType.TEST
        account.remarks = "这是一个测试账号"
        
        self.assertEqual(account.username, "test_user_001")
        self.assertEqual(account.nickname, "测试用户001")
        self.assertEqual(account.status, AccountStatus.OFFLINE)
        self.assertEqual(account.account_type, AccountType.TEST)
        
        print("✅ 账号对象创建成功")
        
    def test_account_validation(self):
        """测试账号验证"""
        account = Account()
        
        # 测试用户名验证
        with self.assertRaises(ValueError):
            account.username = ""  # 空用户名
            
        with self.assertRaises(ValueError):
            account.username = "ab"  # 太短的用户名
            
        with self.assertRaises(ValueError):
            account.username = "invalid-username!"  # 包含非法字符
            
        # 测试昵称验证
        with self.assertRaises(ValueError):
            account.nickname = ""  # 空昵称
            
        # 测试手机号验证
        with self.assertRaises(ValueError):
            account.phone = "123456"  # 无效手机号
            
        # 测试邮箱验证
        with self.assertRaises(ValueError):
            account.email = "invalid-email"  # 无效邮箱
            
        print("✅ 账号验证测试通过")
        
    def test_account_methods(self):
        """测试账号方法"""
        account = Account()
        account.username = "test_user"
        account.nickname = "测试用户"
        
        # 测试登录信息更新
        account.update_login_info("*************")
        self.assertEqual(account.status, AccountStatus.ONLINE)
        self.assertEqual(account.last_login_ip, "*************")
        self.assertEqual(account.login_count, 1)
        
        # 测试状态检查
        self.assertTrue(account.is_online())
        self.assertTrue(account.is_available())
        
        # 测试设置离线
        account.set_offline()
        self.assertEqual(account.status, AccountStatus.OFFLINE)
        self.assertFalse(account.is_online())
        
        # 测试设置封禁
        account.set_suspended("违规操作")
        self.assertEqual(account.status, AccountStatus.SUSPENDED)
        self.assertFalse(account.is_available())
        
        print("✅ 账号方法测试通过")
        
    def test_account_to_dict(self):
        """测试账号转字典"""
        account = Account()
        account.username = "test_user"
        account.nickname = "测试用户"
        account.status = AccountStatus.ONLINE
        account.account_type = AccountType.MAIN
        
        data = account.to_dict()
        self.assertIsInstance(data, dict)
        self.assertEqual(data['username'], "test_user")
        self.assertEqual(data['nickname'], "测试用户")
        self.assertIn('status_display', data)
        self.assertIn('type_display', data)
        self.assertNotIn('password_hash', data)  # 敏感信息应被移除
        
        print("✅ 账号转字典测试通过")


class TestAccountRepository(unittest.TestCase):
    """账号仓库测试"""
    
    def setUp(self):
        """测试前准备"""
        self.db_manager = DatabaseManager()
        self.db_manager.initialize()
        self.repository = AccountRepository()
        
    def test_repository_creation(self):
        """测试仓库创建"""
        self.assertIsNotNone(self.repository)
        self.assertIsNotNone(self.repository.db_manager)
        print("✅ 账号仓库创建成功")
        
    def test_account_crud_operations(self):
        """测试账号CRUD操作"""
        # 创建测试账号数据
        test_account_data = {
            'username': f'test_user_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
            'nickname': '测试用户CRUD',
            'phone': '***********',
            'email': '<EMAIL>',
            'remarks': 'CRUD测试账号'
        }
        
        try:
            # 测试创建
            created_account = self.repository.create(test_account_data)
            self.assertIsNotNone(created_account)
            self.assertEqual(created_account.username, test_account_data['username'])
            print(f"✅ 账号创建成功，ID: {created_account.id}")
            
            # 测试根据ID查询
            found_account = self.repository.get_by_id(created_account.id)
            self.assertIsNotNone(found_account)
            self.assertEqual(found_account.username, test_account_data['username'])
            print("✅ 根据ID查询成功")
            
            # 测试根据用户名查询
            found_by_username = self.repository.get_by_username(test_account_data['username'])
            self.assertIsNotNone(found_by_username)
            self.assertEqual(found_by_username.id, created_account.id)
            print("✅ 根据用户名查询成功")
            
            # 测试更新
            update_data = {'nickname': '更新后的昵称', 'remarks': '更新后的备注'}
            updated_account = self.repository.update(created_account.id, update_data)
            self.assertIsNotNone(updated_account)
            self.assertEqual(updated_account.nickname, '更新后的昵称')
            print("✅ 账号更新成功")
            
            # 测试状态更新
            status_updated = self.repository.update_status(
                created_account.id, AccountStatus.ONLINE, "*************"
            )
            self.assertTrue(status_updated)
            print("✅ 状态更新成功")
            
            # 测试删除（软删除）
            deleted = self.repository.delete(created_account.id, soft_delete=True)
            self.assertTrue(deleted)
            
            # 验证软删除后无法查询到
            deleted_account = self.repository.get_by_id(created_account.id)
            self.assertIsNone(deleted_account)
            print("✅ 账号软删除成功")
            
        except Exception as e:
            self.fail(f"CRUD操作测试失败: {e}")
            
    def test_repository_queries(self):
        """测试仓库查询方法"""
        try:
            # 测试获取所有账号
            all_accounts = self.repository.get_all()
            self.assertIsInstance(all_accounts, list)
            print(f"✅ 获取所有账号成功，数量: {len(all_accounts)}")
            
            # 测试按状态查询
            online_accounts = self.repository.get_by_status(AccountStatus.ONLINE)
            self.assertIsInstance(online_accounts, list)
            print(f"✅ 按状态查询成功，在线账号数量: {len(online_accounts)}")
            
            # 测试搜索
            search_results = self.repository.search("test")
            self.assertIsInstance(search_results, list)
            print(f"✅ 搜索功能正常，结果数量: {len(search_results)}")
            
            # 测试统计信息
            statistics = self.repository.get_statistics()
            self.assertIsInstance(statistics, dict)
            self.assertIn('total_count', statistics)
            self.assertIn('status_count', statistics)
            print(f"✅ 统计信息获取成功: {statistics}")
            
        except Exception as e:
            self.fail(f"查询方法测试失败: {e}")


class TestLogger(unittest.TestCase):
    """日志系统测试"""
    
    def test_logger_setup(self):
        """测试日志设置"""
        try:
            logger = setup_logger("TestLogger", "INFO")
            self.assertIsNotNone(logger)
            
            # 测试日志记录
            logger.info("这是一条测试信息")
            logger.warning("这是一条测试警告")
            logger.error("这是一条测试错误")
            
            print("✅ 日志系统测试通过")
            
        except Exception as e:
            self.fail(f"日志系统测试失败: {e}")
            
    def test_logger_get(self):
        """测试获取日志记录器"""
        logger = get_logger("TestModule")
        self.assertIsNotNone(logger)
        
        logger.info("模块日志测试")
        print("✅ 模块日志记录器测试通过")


def run_core_tests():
    """运行核心功能测试"""
    print("=" * 70)
    print("快手无人带货工具 - 核心功能测试")
    print("=" * 70)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_classes = [
        TestDatabaseManager,
        TestConfigManager,
        TestAccountModel,
        TestAccountRepository,
        TestLogger
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    print("\n" + "=" * 70)
    if result.wasSuccessful():
        print("🎉 所有核心功能测试通过！")
        print("✅ 数据库连接正常")
        print("✅ 配置管理正常")
        print("✅ 数据模型正常")
        print("✅ 数据仓库正常")
        print("✅ 日志系统正常")
    else:
        print("❌ 部分核心功能测试失败！")
        print(f"失败数量: {len(result.failures)}")
        print(f"错误数量: {len(result.errors)}")
        
        # 输出失败详情
        if result.failures:
            print("\n失败详情:")
            for test, traceback in result.failures:
                print(f"- {test}: {traceback}")
                
        if result.errors:
            print("\n错误详情:")
            for test, traceback in result.errors:
                print(f"- {test}: {traceback}")
    
    print("=" * 70)
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_core_tests()
    
    if success:
        print("\n🚀 核心功能验证完成，应用程序后端功能正常！")
        print("💡 注意：由于PyQt6 GUI组件在当前环境下可能存在兼容性问题，")
        print("   建议在安装了完整Visual C++运行库的环境中运行GUI界面。")
        exit_code = 0
    else:
        print("\n⚠️ 核心功能测试失败，请检查相关问题。")
        exit_code = 1
        
    sys.exit(exit_code)
