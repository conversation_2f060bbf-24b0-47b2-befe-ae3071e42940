#!/usr/bin/env python3
"""
测试MoviePy导入
"""

import sys
import os

def test_moviepy_import():
    """测试MoviePy导入"""
    print("🎬 测试MoviePy导入...")
    
    try:
        print("1. 导入moviepy模块...")
        import moviepy
        print(f"✅ MoviePy版本: {moviepy.__version__}")
        
        print("2. 导入editor模块...")
        from moviepy import editor
        print("✅ editor模块导入成功")
        
        print("3. 导入具体类...")
        from moviepy.editor import ImageClip, AudioFileClip, concatenate_videoclips
        print("✅ 具体类导入成功")
        
        print("4. 测试创建ImageClip...")
        # 创建一个简单的测试
        clip = ImageClip("test", duration=1)  # 这会失败但不会崩溃
        print("✅ ImageClip类可用")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"⚠️ 其他错误: {e}")
        return True  # 其他错误不影响导入成功

def test_simple_video_creation():
    """测试简单视频创建"""
    print("\n🎥 测试简单视频创建...")
    
    try:
        from moviepy.editor import ColorClip, AudioFileClip, CompositeVideoClip
        
        # 创建一个简单的彩色视频
        print("1. 创建彩色剪辑...")
        clip = ColorClip(size=(640, 480), color=(255, 0, 0), duration=2)
        print("✅ 彩色剪辑创建成功")
        
        # 设置输出路径
        output_path = "test_simple_video.mp4"
        
        print("2. 导出视频...")
        clip.write_videofile(
            output_path,
            fps=24,
            codec='libx264',
            verbose=False,
            logger=None
        )
        
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            print(f"✅ 视频创建成功! 文件大小: {file_size} bytes")
            
            # 清理测试文件
            try:
                os.remove(output_path)
                print("✅ 测试文件已清理")
            except:
                pass
            
            return True
        else:
            print("❌ 视频文件未创建")
            return False
            
    except Exception as e:
        print(f"❌ 视频创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 MoviePy功能测试")
    print("=" * 40)
    
    # 测试导入
    import_ok = test_moviepy_import()
    
    if import_ok:
        print("\n✅ MoviePy导入测试通过")
        
        # 测试视频创建
        video_ok = test_simple_video_creation()
        
        if video_ok:
            print("\n🎉 MoviePy功能完全正常!")
            print("现在可以使用图文视频合成功能了。")
        else:
            print("\n⚠️ MoviePy导入成功，但视频创建有问题")
            print("可能需要安装额外的编解码器")
    else:
        print("\n❌ MoviePy导入失败")
        print("请尝试重新安装: pip install moviepy")
    
    return import_ok

if __name__ == "__main__":
    try:
        success = main()
        input("\n按回车键退出...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
        sys.exit(1)
