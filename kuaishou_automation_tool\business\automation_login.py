"""
快手无人带货工具 - 自动化登录模块
Kuaishou Automation Tool - Automation Login Module

本模块实现自动化登录流程，包括：
- Selenium WebDriver管理
- 浏览器自动化控制
- 登录状态检测
- Cookie提取和保存
"""

import time
import threading
from typing import Optional, Callable, Dict, Any, List
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager

try:
    from ..utils.logger import get_logger
    from .cookie_manager import CookieManager
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from utils.logger import get_logger
    from business.cookie_manager import CookieManager


class AutomationLoginManager:
    """
    自动化登录管理器
    
    负责管理整个自动化登录流程，包括浏览器启动、登录检测、Cookie提取等。
    支持多线程操作，避免阻塞UI界面。
    
    Attributes:
        logger: 日志记录器
        cookie_manager (CookieManager): Cookie管理器
        driver: WebDriver实例
        is_running (bool): 是否正在运行
        login_url (str): 登录页面URL
        success_element_xpath (str): 登录成功检测元素XPath
    """
    
    def __init__(self, cookie_manager: CookieManager = None):
        """
        初始化自动化登录管理器
        
        Args:
            cookie_manager (CookieManager, optional): Cookie管理器实例
        """
        self.logger = get_logger("AutomationLogin")
        self.cookie_manager = cookie_manager or CookieManager()
        self.driver = None
        self.is_running = False
        
        # 配置参数
        self.login_url = "https://cp.kuaishou.com/profile"
        self.success_element_xpath = "/html/body/div[1]/div[1]/div[1]/div/section/ul/li[1]"
        self.timeout_seconds = 300  # 5分钟超时
        
    def _setup_chrome_options(self) -> Options:
        """
        设置Chrome浏览器选项
        
        Returns:
            Options: Chrome选项对象
        """
        options = Options()
        
        # 基本设置
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        # 用户体验设置
        options.add_argument("--start-maximized")
        options.add_argument("--disable-web-security")
        options.add_argument("--allow-running-insecure-content")
        
        # 设置User-Agent
        user_agent = ("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
                     "(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        options.add_argument(f"--user-agent={user_agent}")
        
        return options
        
    def _create_webdriver(self) -> webdriver.Chrome:
        """
        创建WebDriver实例，支持多种WebDriver获取方式

        Returns:
            webdriver.Chrome: Chrome WebDriver实例

        Raises:
            WebDriverException: WebDriver创建失败
        """
        try:
            # 优先尝试使用系统PATH中的ChromeDriver
            options = self._setup_chrome_options()
            driver = None

            # 方法1: 尝试使用系统PATH中的ChromeDriver
            try:
                self.logger.info("尝试使用系统PATH中的ChromeDriver...")
                driver = webdriver.Chrome(options=options)
                self.logger.info("✅ 使用系统PATH中的ChromeDriver成功")
            except Exception as path_error:
                self.logger.warning(f"系统PATH中的ChromeDriver不可用: {path_error}")

                # 方法2: 使用ChromeDriverManager自动下载
                try:
                    self.logger.info("尝试使用ChromeDriverManager自动下载...")
                    # 禁用版本检查警告
                    import warnings
                    with warnings.catch_warnings():
                        warnings.simplefilter("ignore")
                        service = Service(ChromeDriverManager().install())
                    driver = webdriver.Chrome(service=service, options=options)
                    self.logger.info("✅ 使用ChromeDriverManager自动下载成功")
                except Exception as manager_error:
                    self.logger.error(f"ChromeDriverManager失败: {manager_error}")
                    raise WebDriverException(f"无法创建WebDriver: PATH方式失败({path_error}), 自动下载失败({manager_error})")

            if driver:
                # 执行反检测脚本
                try:
                    driver.execute_script(
                        "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})"
                    )
                    # 设置更长的超时时间，避免会话断开
                    driver.implicitly_wait(10)
                    driver.set_page_load_timeout(30)

                    self.logger.info("WebDriver创建成功，反检测脚本已执行")
                except Exception as script_error:
                    self.logger.warning(f"反检测脚本执行失败: {script_error}")

                return driver
            else:
                raise WebDriverException("WebDriver创建失败：所有方法都不可用")

        except Exception as e:
            self.logger.error(f"创建WebDriver失败: {e}")
            raise WebDriverException(f"WebDriver创建失败: {e}")
            
    def start_login_process(self, phone: str, 
                          progress_callback: Callable[[str], None] = None,
                          success_callback: Callable[[str], None] = None,
                          error_callback: Callable[[str], None] = None) -> threading.Thread:
        """
        启动登录流程（异步）
        
        Args:
            phone (str): 手机号
            progress_callback (Callable, optional): 进度回调函数
            success_callback (Callable, optional): 成功回调函数
            error_callback (Callable, optional): 错误回调函数
            
        Returns:
            threading.Thread: 工作线程
        """
        def login_worker():
            """登录工作线程"""
            try:
                self._execute_login_process(phone, progress_callback, success_callback, error_callback)
            except Exception as e:
                self.logger.error(f"登录流程异常: {e}")
                if error_callback:
                    error_callback(f"登录流程异常: {e}")
                    
        # 创建并启动工作线程
        thread = threading.Thread(target=login_worker, daemon=True)
        thread.start()
        return thread
        
    def _execute_login_process(self, phone: str,
                             progress_callback: Callable[[str], None] = None,
                             success_callback: Callable[[str], None] = None,
                             error_callback: Callable[[str], None] = None):
        """
        执行登录流程的核心逻辑
        
        Args:
            phone (str): 手机号
            progress_callback (Callable, optional): 进度回调函数
            success_callback (Callable, optional): 成功回调函数
            error_callback (Callable, optional): 错误回调函数
        """
        try:
            self.is_running = True
            
            # 步骤1: 创建浏览器
            if progress_callback:
                progress_callback("正在启动浏览器...")
            
            self.driver = self._create_webdriver()
            
            # 步骤2: 导航到登录页面
            if progress_callback:
                progress_callback("正在打开登录页面...")
                
            self.driver.get(self.login_url)
            self.logger.info(f"已导航到登录页面: {self.login_url}")
            
            # 步骤3: 等待用户手动登录
            if progress_callback:
                progress_callback("请在浏览器中完成登录操作...")
                
            # 步骤4: 监控登录状态
            login_success = self._wait_for_login_success()
            
            if login_success:
                # 步骤5: 提取Cookie
                if progress_callback:
                    progress_callback("登录成功，正在提取Cookie...")
                    
                cookies = self._extract_cookies()
                
                # 步骤6: 保存Cookie
                cookie_file_path = self.cookie_manager.save_cookies(phone, cookies)
                
                if cookie_file_path:
                    self.logger.info(f"登录流程完成，Cookie已保存: {cookie_file_path}")
                    if success_callback:
                        success_callback(cookie_file_path)
                else:
                    error_msg = "Cookie保存失败"
                    self.logger.error(error_msg)
                    if error_callback:
                        error_callback(error_msg)
            else:
                error_msg = "登录超时或失败"
                self.logger.warning(error_msg)
                if error_callback:
                    error_callback(error_msg)
                    
        except Exception as e:
            error_msg = f"登录流程执行失败: {e}"
            self.logger.error(error_msg)
            if error_callback:
                error_callback(error_msg)
        finally:
            self._cleanup()
            
    def _wait_for_login_success(self) -> bool:
        """
        等待登录成功
        
        Returns:
            bool: 是否登录成功
        """
        try:
            self.logger.info("开始监控登录状态...")
            
            # 使用WebDriverWait等待成功元素出现
            wait = WebDriverWait(self.driver, self.timeout_seconds)
            
            # 等待登录成功标志元素出现
            success_element = wait.until(
                EC.presence_of_element_located((By.XPATH, self.success_element_xpath))
            )
            
            if success_element:
                self.logger.info("检测到登录成功标志")
                return True
                
        except TimeoutException:
            self.logger.warning(f"登录超时（{self.timeout_seconds}秒）")
            return False
        except Exception as e:
            self.logger.error(f"登录状态检测失败: {e}")
            return False
            
        return False
        
    def _extract_cookies(self) -> List[Dict[str, Any]]:
        """
        提取浏览器Cookie
        
        Returns:
            List[Dict]: Cookie列表
        """
        try:
            if not self.driver:
                raise RuntimeError("WebDriver未初始化")
                
            # 获取所有Cookie
            cookies = self.driver.get_cookies()
            
            self.logger.info(f"成功提取 {len(cookies)} 个Cookie")
            return cookies
            
        except Exception as e:
            self.logger.error(f"提取Cookie失败: {e}")
            return []
            
    def _cleanup(self):
        """清理资源"""
        try:
            self.is_running = False
            
            if self.driver:
                self.driver.quit()
                self.driver = None
                self.logger.info("WebDriver已关闭")
                
        except Exception as e:
            self.logger.error(f"清理资源失败: {e}")
            
    def stop_login_process(self):
        """停止登录流程"""
        try:
            self.logger.info("正在停止登录流程...")
            self._cleanup()
        except Exception as e:
            self.logger.error(f"停止登录流程失败: {e}")
            
    def is_process_running(self) -> bool:
        """
        检查登录流程是否正在运行
        
        Returns:
            bool: 是否正在运行
        """
        return self.is_running
        
    def get_current_url(self) -> Optional[str]:
        """
        获取当前页面URL
        
        Returns:
            str: 当前URL，失败返回None
        """
        try:
            if self.driver:
                return self.driver.current_url
        except Exception as e:
            self.logger.error(f"获取当前URL失败: {e}")
        return None
        
    def take_screenshot(self, file_path: str) -> bool:
        """
        截取当前页面截图
        
        Args:
            file_path (str): 截图保存路径
            
        Returns:
            bool: 截图是否成功
        """
        try:
            if self.driver:
                self.driver.save_screenshot(file_path)
                self.logger.info(f"截图已保存: {file_path}")
                return True
        except Exception as e:
            self.logger.error(f"截图失败: {e}")
        return False
        
    def set_timeout(self, timeout_seconds: int):
        """
        设置超时时间
        
        Args:
            timeout_seconds (int): 超时时间（秒）
        """
        self.timeout_seconds = max(60, timeout_seconds)  # 最少1分钟
        self.logger.info(f"登录超时时间已设置为: {self.timeout_seconds}秒")
        
    def test_webdriver_setup(self) -> bool:
        """
        测试WebDriver设置
        
        Returns:
            bool: 测试是否成功
        """
        try:
            self.logger.info("开始测试WebDriver设置...")
            
            # 创建临时WebDriver
            test_driver = self._create_webdriver()
            
            # 访问测试页面
            test_driver.get("https://www.baidu.com")
            
            # 检查页面标题
            title = test_driver.title
            self.logger.info(f"测试页面标题: {title}")
            
            # 清理
            test_driver.quit()
            
            self.logger.info("WebDriver测试成功")
            return True
            
        except Exception as e:
            self.logger.error(f"WebDriver测试失败: {e}")
            return False
