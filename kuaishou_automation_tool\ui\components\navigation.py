"""
快手无人带货工具 - 导航栏组件
Kuaishou Automation Tool - Navigation Component

本模块实现左侧导航栏组件，包括：
- 应用标题显示
- 功能模块导航按钮
- 页面切换信号处理
- 响应式布局支持
"""

# 尝试导入Qt库，优先使用PySide6，回退到PyQt6
try:
    from PySide6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QLabel,
        QPushButton, QFrame, QSpacerItem, QSizePolicy
    )
    from PySide6.QtCore import Qt, Signal as pyqtSignal, QSize
    from PySide6.QtGui import QFont
    QT_LIBRARY = "PySide6"
except ImportError:
    try:
        from PyQt6.QtWidgets import (
            QWidget, QVBoxLayout, QHBoxLayout, QLabel,
            QPushButton, QFrame, QSpacerItem, QSizePolicy
        )
        from PyQt6.QtCore import Qt, pyqtSignal, QSize
        from PyQt6.QtGui import QFont
        QT_LIBRARY = "PyQt6"
    except ImportError as e:
        print("❌ 无法导入Qt库，请使用命令行版本: python main_cli.py")
        import sys
        sys.exit(1)


class NavigationWidget(QWidget):
    """
    导航栏组件类
    
    实现左侧导航栏的所有功能，包括标题显示、导航按钮管理、
    页面切换信号发送等。采用信号-槽机制与主窗口通信。
    
    Signals:
        page_changed (str): 页面切换信号，传递页面标识符
        
    Attributes:
        nav_buttons (dict): 导航按钮字典，键为页面ID，值为按钮对象
        current_page (str): 当前选中的页面标识符
    """
    
    # 定义信号
    page_changed = pyqtSignal(str)
    
    def __init__(self, parent=None):
        """
        初始化导航栏组件
        
        Args:
            parent: 父组件，默认为None
        """
        super().__init__(parent)
        self.nav_buttons = {}
        self.current_page = "account_management"
        self.init_ui()
        
    def init_ui(self):
        """
        初始化用户界面
        
        创建导航栏的完整布局结构：
        - 应用标题区域
        - 导航按钮区域
        - 底部信息区域
        """
        # 设置组件属性
        self.setObjectName("navigationWidget")
        self.setFixedWidth(220)
        
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建各个区域
        self.create_title_section(main_layout)
        self.create_navigation_section(main_layout)
        self.create_footer_section(main_layout)
        
    def create_title_section(self, parent_layout):
        """
        创建标题区域
        
        Args:
            parent_layout: 父布局对象
        """
        title_label = QLabel("快手无人带货")
        title_label.setObjectName("navigationTitle")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setMinimumHeight(60)
        
        parent_layout.addWidget(title_label)
        
    def create_navigation_section(self, parent_layout):
        """
        创建导航按钮区域
        
        Args:
            parent_layout: 父布局对象
        """
        # 创建导航按钮容器
        nav_container = QWidget()
        nav_layout = QVBoxLayout(nav_container)
        nav_layout.setContentsMargins(0, 20, 0, 0)
        nav_layout.setSpacing(2)
        
        # 定义导航项目
        nav_items = self.get_navigation_items()
        
        # 创建导航按钮
        for page_id, text, tooltip, icon in nav_items:
            button = self.create_navigation_button(page_id, text, tooltip, icon)
            self.nav_buttons[page_id] = button
            nav_layout.addWidget(button)
        
        # 添加弹性空间
        nav_layout.addStretch()
        
        # 添加到主布局
        parent_layout.addWidget(nav_container)
        
    def create_navigation_button(self, page_id, text, tooltip, icon):
        """
        创建单个导航按钮
        
        Args:
            page_id (str): 页面标识符
            text (str): 按钮显示文本
            tooltip (str): 工具提示文本
            icon (str): 图标字符
            
        Returns:
            QPushButton: 创建的导航按钮
        """
        button = QPushButton(f"{icon} {text}")
        button.setObjectName("navButton")
        button.setCheckable(True)
        button.setToolTip(tooltip)
        button.setMinimumHeight(45)
        
        # 连接点击信号
        button.clicked.connect(lambda checked, pid=page_id: self.on_button_clicked(pid))
        
        # 设置默认选中状态
        if page_id == self.current_page:
            button.setChecked(True)
            
        return button
        
    def create_footer_section(self, parent_layout):
        """
        创建底部信息区域
        
        Args:
            parent_layout: 父布局对象
        """
        footer_widget = QWidget()
        footer_layout = QVBoxLayout(footer_widget)
        footer_layout.setContentsMargins(15, 15, 15, 20)
        footer_layout.setSpacing(5)
        
        # 版本信息
        version_label = QLabel("版本: v1.0.0")
        version_label.setStyleSheet("""
            color: #888888; 
            font-size: 10px;
            font-weight: normal;
        """)
        version_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 状态信息
        status_label = QLabel("● 就绪")
        status_label.setStyleSheet("""
            color: #28a745; 
            font-size: 10px;
            font-weight: bold;
        """)
        status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        footer_layout.addWidget(version_label)
        footer_layout.addWidget(status_label)
        
        parent_layout.addWidget(footer_widget)
        
    def get_navigation_items(self):
        """
        获取导航项目配置
        
        Returns:
            list: 导航项目列表，每个项目包含(page_id, text, tooltip, icon)
        """
        return [
            (
                "account_management", 
                "账号管理", 
                "管理快手账号信息，包括添加、编辑、删除账号",
                "📱"
            ),
            (
                "product_management", 
                "商品管理", 
                "管理带货商品信息，包括商品库存、价格等",
                "🛍️"
            ),
            (
                "live_settings", 
                "直播设置", 
                "配置直播相关参数，包括推流设置、互动设置等",
                "📺"
            ),
            (
                "data_statistics", 
                "数据统计", 
                "查看运营数据统计，包括销售数据、用户数据等",
                "📊"
            ),
            (
                "automation_settings", 
                "自动化设置", 
                "配置自动化规则和策略",
                "🤖"
            ),
            (
                "system_settings", 
                "系统设置", 
                "配置系统参数，包括通知设置、日志设置等",
                "⚙️"
            )
        ]
        
    def on_button_clicked(self, page_id):
        """
        处理导航按钮点击事件
        
        Args:
            page_id (str): 被点击按钮对应的页面标识符
        """
        if page_id != self.current_page:
            # 更新按钮状态
            self.update_button_states(page_id)
            
            # 更新当前页面
            self.current_page = page_id
            
            # 发送页面切换信号
            self.page_changed.emit(page_id)
            
    def update_button_states(self, active_page_id):
        """
        更新所有导航按钮的选中状态
        
        Args:
            active_page_id (str): 当前激活的页面标识符
        """
        for page_id, button in self.nav_buttons.items():
            button.setChecked(page_id == active_page_id)
            
    def set_current_page(self, page_id):
        """
        设置当前页面（外部调用）
        
        Args:
            page_id (str): 要设置为当前页面的标识符
        """
        if page_id in self.nav_buttons and page_id != self.current_page:
            self.update_button_states(page_id)
            self.current_page = page_id
            
    def get_current_page(self):
        """
        获取当前页面标识符
        
        Returns:
            str: 当前页面标识符
        """
        return self.current_page
        
    def add_navigation_item(self, page_id, text, tooltip, icon, position=None):
        """
        动态添加导航项目
        
        Args:
            page_id (str): 页面标识符
            text (str): 按钮显示文本
            tooltip (str): 工具提示文本
            icon (str): 图标字符
            position (int, optional): 插入位置，默认添加到末尾
        """
        if page_id not in self.nav_buttons:
            button = self.create_navigation_button(page_id, text, tooltip, icon)
            self.nav_buttons[page_id] = button
            
            # 获取导航容器布局
            nav_container = self.findChild(QWidget)
            if nav_container:
                nav_layout = nav_container.layout()
                if position is not None:
                    nav_layout.insertWidget(position, button)
                else:
                    # 在弹性空间之前插入
                    nav_layout.insertWidget(nav_layout.count() - 1, button)
                    
    def remove_navigation_item(self, page_id):
        """
        移除导航项目
        
        Args:
            page_id (str): 要移除的页面标识符
        """
        if page_id in self.nav_buttons:
            button = self.nav_buttons[page_id]
            button.setParent(None)
            del self.nav_buttons[page_id]
            
            # 如果移除的是当前页面，切换到第一个可用页面
            if page_id == self.current_page and self.nav_buttons:
                first_page = next(iter(self.nav_buttons.keys()))
                self.set_current_page(first_page)
                self.page_changed.emit(first_page)
                
    def update_status(self, status_text, status_color="#28a745"):
        """
        更新底部状态显示
        
        Args:
            status_text (str): 状态文本
            status_color (str): 状态颜色，默认为绿色
        """
        # 查找状态标签并更新
        for child in self.findChildren(QLabel):
            if "●" in child.text():
                child.setText(f"● {status_text}")
                child.setStyleSheet(f"""
                    color: {status_color}; 
                    font-size: 10px;
                    font-weight: bold;
                """)
                break
