# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations
"""
This file contains the exact signatures for all functions in module
PySide6.QtConcurrent, except for defaults which are replaced by "...".

# mypy: disable-error-code="override, overload-overlap"
"""

# Module `PySide6.QtConcurrent`

import PySide6.QtConcurrent
import PySide6.QtCore

import enum
import typing
from shiboken6 import Shiboken


class QFutureQString(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, QFutureQString: PySide6.QtConcurrent.QFutureQString, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def cancel(self, /) -> None: ...
    def isCanceled(self, /) -> bool: ...
    def isFinished(self, /) -> bool: ...
    def isPaused(self, /) -> bool: ...
    def isRunning(self, /) -> bool: ...
    def isStarted(self, /) -> bool: ...
    def isSuspended(self, /) -> bool: ...
    def isSuspending(self, /) -> bool: ...
    def isValid(self, /) -> bool: ...
    def pause(self, /) -> None: ...
    def progressMaximum(self, /) -> int: ...
    def progressMinimum(self, /) -> int: ...
    def progressText(self, /) -> str: ...
    def progressValue(self, /) -> int: ...
    def resultCount(self, /) -> int: ...
    def resume(self, /) -> None: ...
    def setPaused(self, paused: bool, /) -> None: ...
    def setSuspended(self, suspend: bool, /) -> None: ...
    def suspend(self, /) -> None: ...
    def togglePaused(self, /) -> None: ...
    def toggleSuspended(self, /) -> None: ...
    def waitForFinished(self, /) -> None: ...


class QFutureVoid(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, QFutureVoid: PySide6.QtConcurrent.QFutureVoid, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def cancel(self, /) -> None: ...
    def isCanceled(self, /) -> bool: ...
    def isFinished(self, /) -> bool: ...
    def isPaused(self, /) -> bool: ...
    def isRunning(self, /) -> bool: ...
    def isStarted(self, /) -> bool: ...
    def isSuspended(self, /) -> bool: ...
    def isSuspending(self, /) -> bool: ...
    def isValid(self, /) -> bool: ...
    def pause(self, /) -> None: ...
    def progressMaximum(self, /) -> int: ...
    def progressMinimum(self, /) -> int: ...
    def progressText(self, /) -> str: ...
    def progressValue(self, /) -> int: ...
    def resultCount(self, /) -> int: ...
    def resume(self, /) -> None: ...
    def setPaused(self, paused: bool, /) -> None: ...
    def setSuspended(self, suspend: bool, /) -> None: ...
    def suspend(self, /) -> None: ...
    def togglePaused(self, /) -> None: ...
    def toggleSuspended(self, /) -> None: ...
    def waitForFinished(self, /) -> None: ...


class QFutureWatcherQString(PySide6.QtCore.QObject):

    def __init__(self, /, _parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def future(self, /) -> PySide6.QtConcurrent.QFutureQString: ...
    def setFuture(self, future: PySide6.QtConcurrent.QFutureQString, /) -> None: ...


class QFutureWatcherVoid(PySide6.QtCore.QObject):

    def __init__(self, /, _parent: PySide6.QtCore.QObject | None = ...) -> None: ...


class QIntList: ...


class QtConcurrent(Shiboken.Object):

    class FutureResult(enum.Enum):

        Ignore                    = ...  # 0x0

    class ReduceOption(enum.Flag):

        UnorderedReduce           = ...  # 0x1
        OrderedReduce             = ...  # 0x2
        SequentialReduce          = ...  # 0x4

    class ThreadFunctionResult(enum.Enum):

        ThrottleThread            = ...  # 0x0
        ThreadFinished            = ...  # 0x1


# eof
