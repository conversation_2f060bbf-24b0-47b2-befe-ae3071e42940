"""
测试按钮点击事件
"""

import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QMessageBox
    from PySide6.QtCore import Qt
    QT_LIBRARY = "PySide6"
    print("✅ 使用PySide6库")
except ImportError:
    try:
        from PyQt6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QMessageBox
        from PyQt6.QtCore import Qt
        QT_LIBRARY = "PyQt6"
        print("✅ 使用PyQt6库")
    except ImportError as e:
        print("❌ 无法导入Qt库")
        sys.exit(1)

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("按钮测试")
        self.setGeometry(100, 100, 300, 200)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建测试按钮
        self.test_button = QPushButton("点击测试")
        self.test_button.clicked.connect(self.on_button_clicked)
        layout.addWidget(self.test_button)
        
        # 创建添加账号按钮（模拟原始功能）
        self.add_button = QPushButton("➕ 添加账号")
        self.add_button.clicked.connect(self.test_add_account)
        layout.addWidget(self.add_button)
        
        print("🔧 测试窗口初始化完成")
        
    def on_button_clicked(self):
        print("✅ 测试按钮被点击了！")
        QMessageBox.information(self, "测试", "按钮点击事件正常工作！")
        
    def test_add_account(self):
        print("🔍 添加账号按钮被点击了！")
        try:
            # 尝试导入AccountDialog
            from ui.components.account_widget import AccountDialog
            print("✅ AccountDialog导入成功")
            
            dialog = AccountDialog(self)
            print("✅ AccountDialog创建成功")
            
            result = dialog.exec()
            print(f"📋 对话框结果: {result}")
            
            if result == 1:  # Accepted
                account_data = dialog.get_account_data()
                print(f"📊 获取的账号数据: {account_data}")
                QMessageBox.information(self, "成功", f"账号数据: {account_data}")
            else:
                print("❌ 用户取消了对话框")
                
        except Exception as e:
            print(f"❌ 测试添加账号时发生错误: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"发生错误：\n{e}")

def main():
    app = QApplication(sys.argv)
    
    window = TestWindow()
    window.show()
    
    print("🚀 应用程序启动，请点击按钮测试...")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
