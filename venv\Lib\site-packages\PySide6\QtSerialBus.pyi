# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations
"""
This file contains the exact signatures for all functions in module
PySide6.QtSerialBus, except for defaults which are replaced by "...".

# mypy: disable-error-code="override, overload-overlap"
"""

# Module `PySide6.QtSerialBus`

import PySide6.QtSerialBus
import PySide6.QtCore
import PySide6.QtNetwork

import enum
import typing
import collections.abc
from PySide6.QtCore import Signal
from shiboken6 import Shiboken


class QCanBus(PySide6.QtCore.QObject):
    @typing.overload
    def availableDevices(self, /) -> tuple: ...
    @typing.overload
    def availableDevices(self, plugin: str, /) -> tuple: ...
    def createDevice(self, plugin: str, interfaceName: str, /) -> tuple: ...
    @staticmethod
    def instance() -> PySide6.QtSerialBus.QCanBus: ...
    def plugins(self, /) -> typing.List[str]: ...


class QCanBusDevice(PySide6.QtCore.QObject):

    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(QCanBusDevice::CanBusError)
    framesReceived           : typing.ClassVar[Signal] = ... # framesReceived()
    framesWritten            : typing.ClassVar[Signal] = ... # framesWritten(qlonglong)
    stateChanged             : typing.ClassVar[Signal] = ... # stateChanged(QCanBusDevice::CanBusDeviceState)

    class CanBusDeviceState(enum.Enum):

        UnconnectedState          = ...  # 0x0
        ConnectingState           = ...  # 0x1
        ConnectedState            = ...  # 0x2
        ClosingState              = ...  # 0x3

    class CanBusError(enum.Enum):

        NoError                   = ...  # 0x0
        ReadError                 = ...  # 0x1
        WriteError                = ...  # 0x2
        ConnectionError           = ...  # 0x3
        ConfigurationError        = ...  # 0x4
        UnknownError              = ...  # 0x5
        OperationError            = ...  # 0x6
        TimeoutError              = ...  # 0x7

    class CanBusStatus(enum.Enum):

        Unknown                   = ...  # 0x0
        Good                      = ...  # 0x1
        Warning                   = ...  # 0x2
        Error                     = ...  # 0x3
        BusOff                    = ...  # 0x4

    class ConfigurationKey(enum.Enum):

        RawFilterKey              = ...  # 0x0
        ErrorFilterKey            = ...  # 0x1
        LoopbackKey               = ...  # 0x2
        ReceiveOwnKey             = ...  # 0x3
        BitRateKey                = ...  # 0x4
        CanFdKey                  = ...  # 0x5
        DataBitRateKey            = ...  # 0x6
        ProtocolKey               = ...  # 0x7
        UserKey                   = ...  # 0x1e

    class Direction(enum.Flag):

        Input                     = ...  # 0x1
        Output                    = ...  # 0x2
        AllDirections             = ...  # 0x3

    class Filter(Shiboken.Object):

        class FormatFilter(enum.Flag):

            MatchBaseFormat           = ...  # 0x1
            MatchExtendedFormat       = ...  # 0x2
            MatchBaseAndExtendedFormat = ...  # 0x3


        @typing.overload
        def __init__(self, /) -> None: ...
        @typing.overload
        def __init__(self, Filter: PySide6.QtSerialBus.QCanBusDevice.Filter, /) -> None: ...

        def __copy__(self, /) -> typing.Self: ...
        def __eq__(self, b: PySide6.QtSerialBus.QCanBusDevice.Filter, /) -> bool: ...
        def __ne__(self, b: PySide6.QtSerialBus.QCanBusDevice.Filter, /) -> bool: ...


    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def busStatus(self, /) -> PySide6.QtSerialBus.QCanBusDevice.CanBusStatus: ...
    def clear(self, /, direction: PySide6.QtSerialBus.QCanBusDevice.Direction = ...) -> None: ...
    def clearError(self, /) -> None: ...
    def close(self, /) -> None: ...
    def configurationKeys(self, /) -> typing.List[PySide6.QtSerialBus.QCanBusDevice.ConfigurationKey]: ...
    def configurationParameter(self, key: PySide6.QtSerialBus.QCanBusDevice.ConfigurationKey, /) -> typing.Any: ...
    def connectDevice(self, /) -> bool: ...
    @typing.overload
    @staticmethod
    def createDeviceInfo(plugin: str, name: str, serialNumber: str, description: str, alias: str, channel: int, isVirtual: bool, isFlexibleDataRateCapable: bool, /) -> PySide6.QtSerialBus.QCanBusDeviceInfo: ...
    @typing.overload
    @staticmethod
    def createDeviceInfo(plugin: str, name: str, isVirtual: bool, isFlexibleDataRateCapable: bool, /) -> PySide6.QtSerialBus.QCanBusDeviceInfo: ...
    def dequeueOutgoingFrame(self, /) -> PySide6.QtSerialBus.QCanBusFrame: ...
    def deviceInfo(self, /) -> PySide6.QtSerialBus.QCanBusDeviceInfo: ...
    def disconnectDevice(self, /) -> None: ...
    def enqueueOutgoingFrame(self, newFrame: PySide6.QtSerialBus.QCanBusFrame | PySide6.QtSerialBus.QCanBusFrame.FrameType, /) -> None: ...
    def enqueueReceivedFrames(self, newFrames: collections.abc.Sequence[PySide6.QtSerialBus.QCanBusFrame], /) -> None: ...
    def error(self, /) -> PySide6.QtSerialBus.QCanBusDevice.CanBusError: ...
    def errorString(self, /) -> str: ...
    def framesAvailable(self, /) -> int: ...
    def framesToWrite(self, /) -> int: ...
    def hasBusStatus(self, /) -> bool: ...
    def hasOutgoingFrames(self, /) -> bool: ...
    def interpretErrorFrame(self, errorFrame: PySide6.QtSerialBus.QCanBusFrame | PySide6.QtSerialBus.QCanBusFrame.FrameType, /) -> str: ...
    def open(self, /) -> bool: ...
    def readAllFrames(self, /) -> typing.List[PySide6.QtSerialBus.QCanBusFrame]: ...
    def readFrame(self, /) -> PySide6.QtSerialBus.QCanBusFrame: ...
    def resetController(self, /) -> None: ...
    def setConfigurationParameter(self, key: PySide6.QtSerialBus.QCanBusDevice.ConfigurationKey, value: typing.Any, /) -> None: ...
    def setError(self, errorText: str, arg__2: PySide6.QtSerialBus.QCanBusDevice.CanBusError, /) -> None: ...
    def setState(self, newState: PySide6.QtSerialBus.QCanBusDevice.CanBusDeviceState, /) -> None: ...
    def state(self, /) -> PySide6.QtSerialBus.QCanBusDevice.CanBusDeviceState: ...
    def waitForFramesReceived(self, msecs: int, /) -> bool: ...
    def waitForFramesWritten(self, msecs: int, /) -> bool: ...
    def writeFrame(self, frame: PySide6.QtSerialBus.QCanBusFrame | PySide6.QtSerialBus.QCanBusFrame.FrameType, /) -> bool: ...


class QCanBusDeviceInfo(Shiboken.Object):

    def __init__(self, other: PySide6.QtSerialBus.QCanBusDeviceInfo, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def alias(self, /) -> str: ...
    def channel(self, /) -> int: ...
    def description(self, /) -> str: ...
    def hasFlexibleDataRate(self, /) -> bool: ...
    def isVirtual(self, /) -> bool: ...
    def name(self, /) -> str: ...
    def plugin(self, /) -> str: ...
    def serialNumber(self, /) -> str: ...


class QCanBusFactory(Shiboken.Object):

    def __init__(self, /) -> None: ...

    def availableDevices(self, /) -> typing.Tuple[typing.List[PySide6.QtSerialBus.QCanBusDeviceInfo], str]: ...
    def createDevice(self, interfaceName: str, /) -> typing.Tuple[PySide6.QtSerialBus.QCanBusDevice, str]: ...


class QCanBusFrame(Shiboken.Object):

    class FrameError(enum.Flag):

        NoError                   = ...  # 0x0
        TransmissionTimeoutError  = ...  # 0x1
        LostArbitrationError      = ...  # 0x2
        ControllerError           = ...  # 0x4
        ProtocolViolationError    = ...  # 0x8
        TransceiverError          = ...  # 0x10
        MissingAcknowledgmentError = ...  # 0x20
        BusOffError               = ...  # 0x40
        BusError                  = ...  # 0x80
        ControllerRestartError    = ...  # 0x100
        UnknownError              = ...  # 0x200
        AnyError                  = ...  # 0x1fffffff

    class FrameType(enum.Enum):

        UnknownFrame              = ...  # 0x0
        DataFrame                 = ...  # 0x1
        ErrorFrame                = ...  # 0x2
        RemoteRequestFrame        = ...  # 0x3
        InvalidFrame              = ...  # 0x4

    class TimeStamp(Shiboken.Object):

        @typing.overload
        def __init__(self, TimeStamp: PySide6.QtSerialBus.QCanBusFrame.TimeStamp, /) -> None: ...
        @typing.overload
        def __init__(self, /, s: int | None = ..., usec: int | None = ...) -> None: ...

        def __copy__(self, /) -> typing.Self: ...
        @staticmethod
        def fromMicroSeconds(usec: int, /) -> PySide6.QtSerialBus.QCanBusFrame.TimeStamp: ...
        def microSeconds(self, /) -> int: ...
        def seconds(self, /) -> int: ...


    @typing.overload
    def __init__(self, /, type: PySide6.QtSerialBus.QCanBusFrame.FrameType = ...) -> None: ...
    @typing.overload
    def __init__(self, QCanBusFrame: PySide6.QtSerialBus.QCanBusFrame, /) -> None: ...
    @typing.overload
    def __init__(self, identifier: int, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __lshift__(self, arg__1: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def __rshift__(self, arg__1: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def error(self, /) -> PySide6.QtSerialBus.QCanBusFrame.FrameError: ...
    def frameId(self, /) -> int: ...
    def frameType(self, /) -> PySide6.QtSerialBus.QCanBusFrame.FrameType: ...
    def hasBitrateSwitch(self, /) -> bool: ...
    def hasErrorStateIndicator(self, /) -> bool: ...
    def hasExtendedFrameFormat(self, /) -> bool: ...
    def hasFlexibleDataRateFormat(self, /) -> bool: ...
    def hasLocalEcho(self, /) -> bool: ...
    def isValid(self, /) -> bool: ...
    def payload(self, /) -> PySide6.QtCore.QByteArray: ...
    def setBitrateSwitch(self, bitrateSwitch: bool, /) -> None: ...
    def setError(self, e: PySide6.QtSerialBus.QCanBusFrame.FrameError, /) -> None: ...
    def setErrorStateIndicator(self, errorStateIndicator: bool, /) -> None: ...
    def setExtendedFrameFormat(self, isExtended: bool, /) -> None: ...
    def setFlexibleDataRateFormat(self, isFlexibleData: bool, /) -> None: ...
    def setFrameId(self, newFrameId: int, /) -> None: ...
    def setFrameType(self, newFormat: PySide6.QtSerialBus.QCanBusFrame.FrameType, /) -> None: ...
    def setLocalEcho(self, localEcho: bool, /) -> None: ...
    def setPayload(self, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def setTimeStamp(self, ts: PySide6.QtSerialBus.QCanBusFrame.TimeStamp, /) -> None: ...
    def timeStamp(self, /) -> PySide6.QtSerialBus.QCanBusFrame.TimeStamp: ...
    def toString(self, /) -> str: ...


class QCanDbcFileParser(Shiboken.Object):

    class Error(enum.Enum):

        None_                     = ...  # 0x0
        FileReading               = ...  # 0x1
        Parsing                   = ...  # 0x2


    def __init__(self, /) -> None: ...

    def error(self, /) -> PySide6.QtSerialBus.QCanDbcFileParser.Error: ...
    def errorString(self, /) -> str: ...
    def messageDescriptions(self, /) -> typing.List[PySide6.QtSerialBus.QCanMessageDescription]: ...
    def messageValueDescriptions(self, /) -> typing.Dict[PySide6.QtSerialBus.QtCanBus.UniqueId, typing.Dict[str, typing.Dict[int, str]]]: ...
    @typing.overload
    def parse(self, fileName: str, /) -> bool: ...
    @typing.overload
    def parse(self, fileNames: collections.abc.Sequence[str], /) -> bool: ...
    def parseData(self, data: str, /) -> bool: ...
    @staticmethod
    def uniqueIdDescription() -> PySide6.QtSerialBus.QCanUniqueIdDescription: ...
    def warnings(self, /) -> typing.List[str]: ...


class QCanFrameProcessor(Shiboken.Object):

    class Error(enum.Enum):

        None_                     = ...  # 0x0
        InvalidFrame              = ...  # 0x1
        UnsupportedFrameFormat    = ...  # 0x2
        Decoding                  = ...  # 0x3
        Encoding                  = ...  # 0x4

    class ParseResult(Shiboken.Object):

        @typing.overload
        def __init__(self, /) -> None: ...
        @typing.overload
        def __init__(self, ParseResult: PySide6.QtSerialBus.QCanFrameProcessor.ParseResult, /) -> None: ...

        def __copy__(self, /) -> typing.Self: ...


    def __init__(self, /) -> None: ...

    def addMessageDescriptions(self, descriptions: collections.abc.Sequence[PySide6.QtSerialBus.QCanMessageDescription], /) -> None: ...
    def clearMessageDescriptions(self, /) -> None: ...
    def error(self, /) -> PySide6.QtSerialBus.QCanFrameProcessor.Error: ...
    def errorString(self, /) -> str: ...
    def messageDescriptions(self, /) -> typing.List[PySide6.QtSerialBus.QCanMessageDescription]: ...
    def parseFrame(self, frame: PySide6.QtSerialBus.QCanBusFrame | PySide6.QtSerialBus.QCanBusFrame.FrameType, /) -> PySide6.QtSerialBus.QCanFrameProcessor.ParseResult: ...
    def prepareFrame(self, uniqueId: PySide6.QtSerialBus.QtCanBus.UniqueId, signalValues: typing.Dict[str, typing.Any], /) -> PySide6.QtSerialBus.QCanBusFrame: ...
    def setMessageDescriptions(self, descriptions: collections.abc.Sequence[PySide6.QtSerialBus.QCanMessageDescription], /) -> None: ...
    def setUniqueIdDescription(self, description: PySide6.QtSerialBus.QCanUniqueIdDescription, /) -> None: ...
    def uniqueIdDescription(self, /) -> PySide6.QtSerialBus.QCanUniqueIdDescription: ...
    def warnings(self, /) -> typing.List[str]: ...


class QCanMessageDescription(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtSerialBus.QCanMessageDescription, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __repr__(self, /) -> str: ...
    def addSignalDescription(self, description: PySide6.QtSerialBus.QCanSignalDescription, /) -> None: ...
    def clearSignalDescriptions(self, /) -> None: ...
    def comment(self, /) -> str: ...
    def isValid(self, /) -> bool: ...
    def name(self, /) -> str: ...
    def setComment(self, text: str, /) -> None: ...
    def setName(self, name: str, /) -> None: ...
    def setSignalDescriptions(self, descriptions: collections.abc.Sequence[PySide6.QtSerialBus.QCanSignalDescription], /) -> None: ...
    def setSize(self, size: int, /) -> None: ...
    def setTransmitter(self, transmitter: str, /) -> None: ...
    def setUniqueId(self, id: PySide6.QtSerialBus.QtCanBus.UniqueId, /) -> None: ...
    def signalDescriptionForName(self, name: str, /) -> PySide6.QtSerialBus.QCanSignalDescription: ...
    def signalDescriptions(self, /) -> typing.List[PySide6.QtSerialBus.QCanSignalDescription]: ...
    def size(self, /) -> int: ...
    def swap(self, other: PySide6.QtSerialBus.QCanMessageDescription, /) -> None: ...
    def transmitter(self, /) -> str: ...
    def uniqueId(self, /) -> PySide6.QtSerialBus.QtCanBus.UniqueId: ...


class QCanSignalDescription(Shiboken.Object):

    class MultiplexValueRange(Shiboken.Object):

        @typing.overload
        def __init__(self, /) -> None: ...
        @typing.overload
        def __init__(self, MultiplexValueRange: PySide6.QtSerialBus.QCanSignalDescription.MultiplexValueRange, /) -> None: ...

        def __copy__(self, /) -> typing.Self: ...
        def __eq__(self, rhs: PySide6.QtSerialBus.QCanSignalDescription.MultiplexValueRange, /) -> bool: ...
        def __ne__(self, rhs: PySide6.QtSerialBus.QCanSignalDescription.MultiplexValueRange, /) -> bool: ...
        def __repr__(self, /) -> str: ...


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtSerialBus.QCanSignalDescription, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __repr__(self, /) -> str: ...
    @typing.overload
    def addMultiplexSignal(self, name: str, ranges: collections.abc.Sequence[PySide6.QtSerialBus.QCanSignalDescription.MultiplexValueRange], /) -> None: ...
    @typing.overload
    def addMultiplexSignal(self, name: str, value: typing.Any, /) -> None: ...
    def bitLength(self, /) -> int: ...
    def clearMultiplexSignals(self, /) -> None: ...
    def comment(self, /) -> str: ...
    def dataEndian(self, /) -> PySide6.QtCore.QSysInfo.Endian: ...
    def dataFormat(self, /) -> PySide6.QtSerialBus.QtCanBus.DataFormat: ...
    def dataSource(self, /) -> PySide6.QtSerialBus.QtCanBus.DataSource: ...
    def factor(self, /) -> float: ...
    def isValid(self, /) -> bool: ...
    def maximum(self, /) -> float: ...
    def minimum(self, /) -> float: ...
    def multiplexSignals(self, /) -> typing.Dict[str, typing.List[PySide6.QtSerialBus.QCanSignalDescription.MultiplexValueRange]]: ...
    def multiplexState(self, /) -> PySide6.QtSerialBus.QtCanBus.MultiplexState: ...
    def name(self, /) -> str: ...
    def offset(self, /) -> float: ...
    def physicalUnit(self, /) -> str: ...
    def receiver(self, /) -> str: ...
    def scaling(self, /) -> float: ...
    def setBitLength(self, length: int, /) -> None: ...
    def setComment(self, text: str, /) -> None: ...
    def setDataEndian(self, endian: PySide6.QtCore.QSysInfo.Endian, /) -> None: ...
    def setDataFormat(self, format: PySide6.QtSerialBus.QtCanBus.DataFormat, /) -> None: ...
    def setDataSource(self, source: PySide6.QtSerialBus.QtCanBus.DataSource, /) -> None: ...
    def setFactor(self, factor: float, /) -> None: ...
    def setMultiplexSignals(self, multiplexorSignals: typing.Dict[str, collections.abc.Sequence[PySide6.QtSerialBus.QCanSignalDescription.MultiplexValueRange]], /) -> None: ...
    def setMultiplexState(self, state: PySide6.QtSerialBus.QtCanBus.MultiplexState, /) -> None: ...
    def setName(self, name: str, /) -> None: ...
    def setOffset(self, offset: float, /) -> None: ...
    def setPhysicalUnit(self, unit: str, /) -> None: ...
    def setRange(self, minimum: float, maximum: float, /) -> None: ...
    def setReceiver(self, receiver: str, /) -> None: ...
    def setScaling(self, scaling: float, /) -> None: ...
    def setStartBit(self, bit: int, /) -> None: ...
    def startBit(self, /) -> int: ...
    def swap(self, other: PySide6.QtSerialBus.QCanSignalDescription, /) -> None: ...


class QCanUniqueIdDescription(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, other: PySide6.QtSerialBus.QCanUniqueIdDescription, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def bitLength(self, /) -> int: ...
    def endian(self, /) -> PySide6.QtCore.QSysInfo.Endian: ...
    def isValid(self, /) -> bool: ...
    def setBitLength(self, length: int, /) -> None: ...
    def setEndian(self, endian: PySide6.QtCore.QSysInfo.Endian, /) -> None: ...
    def setSource(self, source: PySide6.QtSerialBus.QtCanBus.DataSource, /) -> None: ...
    def setStartBit(self, bit: int, /) -> None: ...
    def source(self, /) -> PySide6.QtSerialBus.QtCanBus.DataSource: ...
    def startBit(self, /) -> int: ...
    def swap(self, other: PySide6.QtSerialBus.QCanUniqueIdDescription, /) -> None: ...


class QIntList: ...


class QModbusClient(PySide6.QtSerialBus.QModbusDevice):

    timeoutChanged           : typing.ClassVar[Signal] = ... # timeoutChanged(int)

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def numberOfRetries(self, /) -> int: ...
    def processPrivateResponse(self, response: PySide6.QtSerialBus.QModbusResponse, data: PySide6.QtSerialBus.QModbusDataUnit, /) -> bool: ...
    def processResponse(self, response: PySide6.QtSerialBus.QModbusResponse, data: PySide6.QtSerialBus.QModbusDataUnit, /) -> bool: ...
    def sendRawRequest(self, request: PySide6.QtSerialBus.QModbusRequest, serverAddress: int, /) -> PySide6.QtSerialBus.QModbusReply: ...
    def sendReadRequest(self, read: PySide6.QtSerialBus.QModbusDataUnit, serverAddress: int, /) -> PySide6.QtSerialBus.QModbusReply: ...
    def sendReadWriteRequest(self, read: PySide6.QtSerialBus.QModbusDataUnit, write: PySide6.QtSerialBus.QModbusDataUnit, serverAddress: int, /) -> PySide6.QtSerialBus.QModbusReply: ...
    def sendWriteRequest(self, write: PySide6.QtSerialBus.QModbusDataUnit, serverAddress: int, /) -> PySide6.QtSerialBus.QModbusReply: ...
    def setNumberOfRetries(self, number: int, /) -> None: ...
    def setTimeout(self, newTimeout: int, /) -> None: ...
    def timeout(self, /) -> int: ...


class QModbusDataUnit(Shiboken.Object):

    class RegisterType(enum.Enum):

        Invalid                   = ...  # 0x0
        DiscreteInputs            = ...  # 0x1
        Coils                     = ...  # 0x2
        InputRegisters            = ...  # 0x3
        HoldingRegisters          = ...  # 0x4


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, QModbusDataUnit: PySide6.QtSerialBus.QModbusDataUnit, /) -> None: ...
    @typing.overload
    def __init__(self, type: PySide6.QtSerialBus.QModbusDataUnit.RegisterType, /) -> None: ...
    @typing.overload
    def __init__(self, type: PySide6.QtSerialBus.QModbusDataUnit.RegisterType, newStartAddress: int, newValues: collections.abc.Sequence[int], /) -> None: ...
    @typing.overload
    def __init__(self, type: PySide6.QtSerialBus.QModbusDataUnit.RegisterType, newStartAddress: int, newValueCount: int, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def isValid(self, /) -> bool: ...
    def registerType(self, /) -> PySide6.QtSerialBus.QModbusDataUnit.RegisterType: ...
    def setRegisterType(self, type: PySide6.QtSerialBus.QModbusDataUnit.RegisterType, /) -> None: ...
    def setStartAddress(self, newAddress: int, /) -> None: ...
    def setValue(self, index: int, newValue: int, /) -> None: ...
    def setValueCount(self, newCount: int, /) -> None: ...
    def setValues(self, newValues: collections.abc.Sequence[int], /) -> None: ...
    def startAddress(self, /) -> int: ...
    def value(self, index: int, /) -> int: ...
    def valueCount(self, /) -> int: ...
    def values(self, /) -> typing.List[int]: ...


class QModbusDevice(PySide6.QtCore.QObject):

    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(QModbusDevice::Error)
    stateChanged             : typing.ClassVar[Signal] = ... # stateChanged(QModbusDevice::State)

    class ConnectionParameter(enum.Enum):

        SerialPortNameParameter   = ...  # 0x0
        SerialParityParameter     = ...  # 0x1
        SerialBaudRateParameter   = ...  # 0x2
        SerialDataBitsParameter   = ...  # 0x3
        SerialStopBitsParameter   = ...  # 0x4
        NetworkPortParameter      = ...  # 0x5
        NetworkAddressParameter   = ...  # 0x6

    class Error(enum.Enum):

        NoError                   = ...  # 0x0
        ReadError                 = ...  # 0x1
        WriteError                = ...  # 0x2
        ConnectionError           = ...  # 0x3
        ConfigurationError        = ...  # 0x4
        TimeoutError              = ...  # 0x5
        ProtocolError             = ...  # 0x6
        ReplyAbortedError         = ...  # 0x7
        UnknownError              = ...  # 0x8
        InvalidResponseError      = ...  # 0x9

    class IntermediateError(enum.Enum):

        ResponseCrcError          = ...  # 0x0
        ResponseRequestMismatch   = ...  # 0x1

    class State(enum.Enum):

        UnconnectedState          = ...  # 0x0
        ConnectingState           = ...  # 0x1
        ConnectedState            = ...  # 0x2
        ClosingState              = ...  # 0x3


    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def close(self, /) -> None: ...
    def connectDevice(self, /) -> bool: ...
    def connectionParameter(self, parameter: PySide6.QtSerialBus.QModbusDevice.ConnectionParameter, /) -> typing.Any: ...
    def device(self, /) -> PySide6.QtCore.QIODevice: ...
    def disconnectDevice(self, /) -> None: ...
    def error(self, /) -> PySide6.QtSerialBus.QModbusDevice.Error: ...
    def errorString(self, /) -> str: ...
    def open(self, /) -> bool: ...
    def setConnectionParameter(self, parameter: PySide6.QtSerialBus.QModbusDevice.ConnectionParameter, value: typing.Any, /) -> None: ...
    def setError(self, errorText: str, error: PySide6.QtSerialBus.QModbusDevice.Error, /) -> None: ...
    def setState(self, newState: PySide6.QtSerialBus.QModbusDevice.State, /) -> None: ...
    def state(self, /) -> PySide6.QtSerialBus.QModbusDevice.State: ...


class QModbusDeviceIdentification(Shiboken.Object):

    class ConformityLevel(enum.Enum):

        BasicConformityLevel      = ...  # 0x1
        RegularConformityLevel    = ...  # 0x2
        ExtendedConformityLevel   = ...  # 0x3
        BasicIndividualConformityLevel = ...  # 0x81
        RegularIndividualConformityLevel = ...  # 0x82
        ExtendedIndividualConformityLevel = ...  # 0x83

    class ObjectId(enum.Enum):

        VendorNameObjectId        = ...  # 0x0
        ProductCodeObjectId       = ...  # 0x1
        MajorMinorRevisionObjectId = ...  # 0x2
        VendorUrlObjectId         = ...  # 0x3
        ProductNameObjectId       = ...  # 0x4
        ModelNameObjectId         = ...  # 0x5
        UserApplicationNameObjectId = ...  # 0x6
        ReservedObjectId          = ...  # 0x7
        ProductDependentObjectId  = ...  # 0x80
        UndefinedObjectId         = ...  # 0x100

    class ReadDeviceIdCode(enum.Enum):

        BasicReadDeviceIdCode     = ...  # 0x1
        RegularReadDeviceIdCode   = ...  # 0x2
        ExtendedReadDeviceIdCode  = ...  # 0x3
        IndividualReadDeviceIdCode = ...  # 0x4


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, QModbusDeviceIdentification: PySide6.QtSerialBus.QModbusDeviceIdentification, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def conformityLevel(self, /) -> PySide6.QtSerialBus.QModbusDeviceIdentification.ConformityLevel: ...
    def contains(self, objectId: int, /) -> bool: ...
    @staticmethod
    def fromByteArray(ba: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> PySide6.QtSerialBus.QModbusDeviceIdentification: ...
    def insert(self, objectId: int, data: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> bool: ...
    def isValid(self, /) -> bool: ...
    def objectIds(self, /) -> typing.List[int]: ...
    def remove(self, objectId: int, /) -> None: ...
    def setConformityLevel(self, level: PySide6.QtSerialBus.QModbusDeviceIdentification.ConformityLevel, /) -> None: ...
    def value(self, objectId: int, /) -> PySide6.QtCore.QByteArray: ...


class QModbusExceptionResponse(PySide6.QtSerialBus.QModbusResponse):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, fc: PySide6.QtSerialBus.QModbusPdu.FunctionCode, ec: PySide6.QtSerialBus.QModbusPdu.ExceptionCode, /) -> None: ...
    @typing.overload
    def __init__(self, pdu: PySide6.QtSerialBus.QModbusPdu, /) -> None: ...

    def setExceptionCode(self, ec: PySide6.QtSerialBus.QModbusPdu.ExceptionCode, /) -> None: ...
    def setFunctionCode(self, c: PySide6.QtSerialBus.QModbusPdu.FunctionCode, /) -> None: ...


class QModbusPdu(Shiboken.Object):

    class ExceptionCode(enum.Enum):

        IllegalFunction           = ...  # 0x1
        IllegalDataAddress        = ...  # 0x2
        IllegalDataValue          = ...  # 0x3
        ServerDeviceFailure       = ...  # 0x4
        Acknowledge               = ...  # 0x5
        ServerDeviceBusy          = ...  # 0x6
        NegativeAcknowledge       = ...  # 0x7
        MemoryParityError         = ...  # 0x8
        GatewayPathUnavailable    = ...  # 0xa
        GatewayTargetDeviceFailedToRespond = ...  # 0xb
        ExtendedException         = ...  # 0xff

    class FunctionCode(enum.Enum):

        Invalid                   = ...  # 0x0
        ReadCoils                 = ...  # 0x1
        ReadDiscreteInputs        = ...  # 0x2
        ReadHoldingRegisters      = ...  # 0x3
        ReadInputRegisters        = ...  # 0x4
        WriteSingleCoil           = ...  # 0x5
        WriteSingleRegister       = ...  # 0x6
        ReadExceptionStatus       = ...  # 0x7
        Diagnostics               = ...  # 0x8
        GetCommEventCounter       = ...  # 0xb
        GetCommEventLog           = ...  # 0xc
        WriteMultipleCoils        = ...  # 0xf
        WriteMultipleRegisters    = ...  # 0x10
        ReportServerId            = ...  # 0x11
        ReadFileRecord            = ...  # 0x14
        WriteFileRecord           = ...  # 0x15
        MaskWriteRegister         = ...  # 0x16
        ReadWriteMultipleRegisters = ...  # 0x17
        ReadFifoQueue             = ...  # 0x18
        EncapsulatedInterfaceTransport = ...  # 0x2b
        UndefinedFunctionCode     = ...  # 0x100


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, code: PySide6.QtSerialBus.QModbusPdu.FunctionCode, newData: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    @typing.overload
    def __init__(self, arg__1: PySide6.QtSerialBus.QModbusPdu, /) -> None: ...

    def __lshift__(self, stream: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def __repr__(self, /) -> str: ...
    def data(self, /) -> PySide6.QtCore.QByteArray: ...
    def dataSize(self, /) -> int: ...
    def exceptionCode(self, /) -> PySide6.QtSerialBus.QModbusPdu.ExceptionCode: ...
    def functionCode(self, /) -> PySide6.QtSerialBus.QModbusPdu.FunctionCode: ...
    def isException(self, /) -> bool: ...
    def isValid(self, /) -> bool: ...
    def setData(self, newData: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def setFunctionCode(self, code: PySide6.QtSerialBus.QModbusPdu.FunctionCode, /) -> None: ...
    def size(self, /) -> int: ...


class QModbusReply(PySide6.QtCore.QObject):

    errorOccurred            : typing.ClassVar[Signal] = ... # errorOccurred(QModbusDevice::Error)
    finished                 : typing.ClassVar[Signal] = ... # finished()
    intermediateErrorOccurred: typing.ClassVar[Signal] = ... # intermediateErrorOccurred(QModbusDevice::IntermediateError)

    class ReplyType(enum.Enum):

        Raw                       = ...  # 0x0
        Common                    = ...  # 0x1
        Broadcast                 = ...  # 0x2


    def __init__(self, type: PySide6.QtSerialBus.QModbusReply.ReplyType, serverAddress: int, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def addIntermediateError(self, error: PySide6.QtSerialBus.QModbusDevice.IntermediateError, /) -> None: ...
    def error(self, /) -> PySide6.QtSerialBus.QModbusDevice.Error: ...
    def errorString(self, /) -> str: ...
    def intermediateErrors(self, /) -> typing.List[PySide6.QtSerialBus.QModbusDevice.IntermediateError]: ...
    def isFinished(self, /) -> bool: ...
    def rawResult(self, /) -> PySide6.QtSerialBus.QModbusResponse: ...
    def result(self, /) -> PySide6.QtSerialBus.QModbusDataUnit: ...
    def serverAddress(self, /) -> int: ...
    def setError(self, error: PySide6.QtSerialBus.QModbusDevice.Error, errorText: str, /) -> None: ...
    def setFinished(self, isFinished: bool, /) -> None: ...
    def setRawResult(self, unit: PySide6.QtSerialBus.QModbusResponse, /) -> None: ...
    def setResult(self, unit: PySide6.QtSerialBus.QModbusDataUnit, /) -> None: ...
    def type(self, /) -> PySide6.QtSerialBus.QModbusReply.ReplyType: ...


class QModbusRequest(PySide6.QtSerialBus.QModbusPdu):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, code: PySide6.QtSerialBus.QModbusPdu.FunctionCode, /, newData: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview = ...) -> None: ...
    @typing.overload
    def __init__(self, pdu: PySide6.QtSerialBus.QModbusPdu, /) -> None: ...

    def __lshift__(self, stream: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def __rshift__(self, stream: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    @staticmethod
    def calculateDataSize(pdu: PySide6.QtSerialBus.QModbusRequest, /) -> int: ...
    @staticmethod
    def minimumDataSize(pdu: PySide6.QtSerialBus.QModbusRequest, /) -> int: ...


class QModbusResponse(PySide6.QtSerialBus.QModbusPdu):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, code: PySide6.QtSerialBus.QModbusPdu.FunctionCode, /, newData: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview = ...) -> None: ...
    @typing.overload
    def __init__(self, pdu: PySide6.QtSerialBus.QModbusPdu, /) -> None: ...

    def __lshift__(self, stream: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    def __rshift__(self, stream: PySide6.QtCore.QDataStream, /) -> PySide6.QtCore.QDataStream: ...
    @staticmethod
    def calculateDataSize(pdu: PySide6.QtSerialBus.QModbusResponse, /) -> int: ...
    @staticmethod
    def minimumDataSize(pdu: PySide6.QtSerialBus.QModbusResponse, /) -> int: ...


class QModbusRtuSerialClient(PySide6.QtSerialBus.QModbusClient):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def close(self, /) -> None: ...
    def interFrameDelay(self, /) -> int: ...
    def open(self, /) -> bool: ...
    def setInterFrameDelay(self, microseconds: int, /) -> None: ...
    def setTurnaroundDelay(self, turnaroundDelay: int, /) -> None: ...
    def turnaroundDelay(self, /) -> int: ...


class QModbusRtuSerialServer(PySide6.QtSerialBus.QModbusServer):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def close(self, /) -> None: ...
    def interFrameDelay(self, /) -> int: ...
    def open(self, /) -> bool: ...
    def processRequest(self, request: PySide6.QtSerialBus.QModbusPdu, /) -> PySide6.QtSerialBus.QModbusResponse: ...
    def processesBroadcast(self, /) -> bool: ...
    def setInterFrameDelay(self, microseconds: int, /) -> None: ...


class QModbusServer(PySide6.QtSerialBus.QModbusDevice):

    dataWritten              : typing.ClassVar[Signal] = ... # dataWritten(QModbusDataUnit::RegisterType,int,int)

    class Option(enum.Enum):

        DiagnosticRegister        = ...  # 0x0
        ExceptionStatusOffset     = ...  # 0x1
        DeviceBusy                = ...  # 0x2
        AsciiInputDelimiter       = ...  # 0x3
        ListenOnlyMode            = ...  # 0x4
        ServerIdentifier          = ...  # 0x5
        RunIndicatorStatus        = ...  # 0x6
        AdditionalData            = ...  # 0x7
        DeviceIdentification      = ...  # 0x8
        UserOption                = ...  # 0x100


    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    @typing.overload
    def data(self, newData: PySide6.QtSerialBus.QModbusDataUnit, /) -> bool: ...
    @typing.overload
    def data(self, table: PySide6.QtSerialBus.QModbusDataUnit.RegisterType, address: int, /) -> typing.Tuple[bool, int]: ...
    def processPrivateRequest(self, request: PySide6.QtSerialBus.QModbusPdu, /) -> PySide6.QtSerialBus.QModbusResponse: ...
    def processRequest(self, request: PySide6.QtSerialBus.QModbusPdu, /) -> PySide6.QtSerialBus.QModbusResponse: ...
    def processesBroadcast(self, /) -> bool: ...
    def readData(self, newData: PySide6.QtSerialBus.QModbusDataUnit, /) -> bool: ...
    def serverAddress(self, /) -> int: ...
    @typing.overload
    def setData(self, unit: PySide6.QtSerialBus.QModbusDataUnit, /) -> bool: ...
    @typing.overload
    def setData(self, table: PySide6.QtSerialBus.QModbusDataUnit.RegisterType, address: int, data: int, /) -> bool: ...
    def setMap(self, map: typing.Dict[PySide6.QtSerialBus.QModbusDataUnit.RegisterType, PySide6.QtSerialBus.QModbusDataUnit], /) -> bool: ...
    def setServerAddress(self, serverAddress: int, /) -> None: ...
    def setValue(self, option: int, value: typing.Any, /) -> bool: ...
    def value(self, option: int, /) -> typing.Any: ...
    def writeData(self, unit: PySide6.QtSerialBus.QModbusDataUnit, /) -> bool: ...


class QModbusTcpClient(PySide6.QtSerialBus.QModbusClient):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def close(self, /) -> None: ...
    def open(self, /) -> bool: ...


class QModbusTcpConnectionObserver(Shiboken.Object):

    def __init__(self, /) -> None: ...

    def acceptNewConnection(self, newClient: PySide6.QtNetwork.QTcpSocket, /) -> bool: ...


class QModbusTcpServer(PySide6.QtSerialBus.QModbusServer):

    modbusClientDisconnected : typing.ClassVar[Signal] = ... # modbusClientDisconnected(QTcpSocket*)

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def close(self, /) -> None: ...
    def installConnectionObserver(self, observer: PySide6.QtSerialBus.QModbusTcpConnectionObserver, /) -> None: ...
    def open(self, /) -> bool: ...
    def processRequest(self, request: PySide6.QtSerialBus.QModbusPdu, /) -> PySide6.QtSerialBus.QModbusResponse: ...


class QtCanBus(Shiboken.Object):

    class DataFormat(enum.Enum):

        SignedInteger             = ...  # 0x0
        UnsignedInteger           = ...  # 0x1
        Float                     = ...  # 0x2
        Double                    = ...  # 0x3
        AsciiString               = ...  # 0x4

    class DataSource(enum.Enum):

        Payload                   = ...  # 0x0
        FrameId                   = ...  # 0x1

    class MultiplexState(enum.Enum):

        None_                     = ...  # 0x0
        MultiplexorSwitch         = ...  # 0x1
        MultiplexedSignal         = ...  # 0x2
        SwitchAndSignal           = ...  # 0x3

    class UniqueId(enum.Enum): ...  # type: ignore[misc]


    @staticmethod
    def qbswap(src: PySide6.QtSerialBus.QtCanBus.UniqueId, /) -> PySide6.QtSerialBus.QtCanBus.UniqueId: ...


# eof
