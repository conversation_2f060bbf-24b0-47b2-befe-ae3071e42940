<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<typesystem package="PySide6.QtHttpServer"
            namespace-begin="QT_BEGIN_NAMESPACE" namespace-end="QT_END_NAMESPACE">
  <load-typesystem name="typesystem_concurrent.xml" generate="no"/>
  <load-typesystem name="typesystem_websockets.xml" generate="no"/>

  <typedef-type name="QFutureHttpServerResponse" source="QFuture&lt;QHttpServerResponse&gt;"/>
  <!-- virtual void missingHandler(const QHttpServerRequest &, QHttpServerResponder &&) = 0 -->
  <object-type name="QAbstractHttpServer" disable-wrapper="yes"/>
  <object-type name="QHttpServer">
      <extra-includes>
          <include file-name="QtHttpServer/QHttpServerRequest" location="global"/>
          <include file-name="QtHttpServer/QHttpServerRouterRule" location="global"/>
      </extra-includes>
      <add-function signature="route(const QString &amp;@rule@, PyCallable @callback@)"
                    return-type="bool">
        <inject-code class="target" position="beginning" file="../glue/qhttpserver.cpp"
                     snippet="qhttpserver-route"/>
      </add-function>
      <add-function signature="addAfterRequestHandler(QObject*@context@,PyCallable @callback@)">
        <inject-code class="target" position="beginning" file="../glue/qhttpserver.cpp"
                     snippet="qhttpserver-addafterrequesthandler"/>
      </add-function>
  </object-type>
  <object-type name="QHttpServerResponder">
      <enum-type name="StatusCode"/>
  </object-type>
  <object-type name="QHttpServerRequest">
      <enum-type name="Method" flags="Methods"/>
  </object-type>
  <object-type name="QHttpServerResponse"/>
  <object-type name="QHttpServerRouter"/>
  <object-type name="QHttpServerRouterRule"/>
  <object-type name="QHttpServerWebSocketUpgradeResponse" since="6.8">
      <enum-type name="ResponseType"/>
  </object-type>
</typesystem>
