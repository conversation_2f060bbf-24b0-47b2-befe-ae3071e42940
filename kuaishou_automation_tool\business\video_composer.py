#!/usr/bin/env python3
"""
图文视频合成模块
支持图片+文字讲解合成视频
"""

import os
import sys
import json
import tempfile
from typing import List, Dict, Optional, Tuple
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

try:
    from utils.logger import get_logger
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)

logger = get_logger(__name__)

class VideoComposer:
    """图文视频合成器"""
    
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp(prefix="video_composer_")
        self.supported_image_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.webp']
        self.supported_audio_formats = ['.mp3', '.wav', '.aac', '.m4a']
        
    def __del__(self):
        """清理临时文件"""
        try:
            import shutil
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
        except:
            pass
    
    def create_video_from_images_and_text(
        self,
        images: List[str],
        text_content: str,
        output_path: str,
        config: Optional[Dict] = None
    ) -> bool:
        """
        从图片和文字创建视频
        
        Args:
            images: 图片文件路径列表
            text_content: 文字内容（用于生成语音）
            output_path: 输出视频路径
            config: 配置参数
            
        Returns:
            bool: 是否成功
        """
        try:
            logger.info(f"开始创建图文视频，图片数量: {len(images)}")
            
            # 默认配置
            default_config = {
                'video_width': 1080,
                'video_height': 1920,  # 竖屏比例，适合快手
                'fps': 30,
                'image_duration': 3.0,  # 每张图片显示时间（秒）
                'transition_duration': 0.5,  # 转场时间
                'voice_speed': 1.0,  # 语音速度
                'voice_volume': 0.8,  # 语音音量
                'background_music': None,  # 背景音乐
                'subtitle_style': {
                    'font_size': 48,
                    'font_color': 'white',
                    'font_family': 'Arial',
                    'position': 'bottom',
                    'background_color': 'black',
                    'background_opacity': 0.7
                }
            }
            
            # 合并配置
            if config:
                default_config.update(config)
            config = default_config
            
            # 验证输入
            if not self._validate_inputs(images, text_content, output_path):
                return False
            
            # 生成语音
            audio_path = self._generate_speech(text_content, config)
            if not audio_path:
                logger.error("语音生成失败")
                return False
            
            # 处理图片
            processed_images = self._process_images(images, config)
            if not processed_images:
                logger.error("图片处理失败")
                return False
            
            # 生成字幕
            subtitle_path = self._generate_subtitles(text_content, config)
            
            # 合成视频
            success = self._compose_video(
                processed_images, 
                audio_path, 
                subtitle_path,
                output_path, 
                config
            )
            
            if success:
                logger.info(f"视频创建成功: {output_path}")
            else:
                logger.error("视频合成失败")
                
            return success
            
        except Exception as e:
            logger.error(f"创建图文视频失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _validate_inputs(self, images: List[str], text_content: str, output_path: str) -> bool:
        """验证输入参数"""
        try:
            # 检查图片
            if not images:
                logger.error("图片列表为空")
                return False
            
            for img_path in images:
                if not os.path.exists(img_path):
                    logger.error(f"图片文件不存在: {img_path}")
                    return False
                
                ext = os.path.splitext(img_path)[1].lower()
                if ext not in self.supported_image_formats:
                    logger.error(f"不支持的图片格式: {ext}")
                    return False
            
            # 检查文字内容
            if not text_content or not text_content.strip():
                logger.error("文字内容为空")
                return False
            
            # 检查输出路径
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)
            
            return True
            
        except Exception as e:
            logger.error(f"输入验证失败: {e}")
            return False
    
    def _clean_text_for_speech(self, text: str) -> str:
        """清理文本，移除不适合语音合成的内容"""
        import re

        # 移除网址
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        text = re.sub(r'www\.(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)

        # 移除邮箱地址
        text = re.sub(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', '', text)

        # 移除电话号码
        text = re.sub(r'\b\d{3,4}[-.\s]?\d{3,4}[-.\s]?\d{4}\b', '', text)
        text = re.sub(r'\b1[3-9]\d{9}\b', '', text)

        # 移除XML/HTML标签
        text = re.sub(r'<[^>]+>', '', text)

        # 移除XML命名空间和属性
        text = re.sub(r'xmlns\s*=\s*["\'][^"\']*["\']', '', text)
        text = re.sub(r'xml:\w+\s*=\s*["\'][^"\']*["\']', '', text)

        # 移除特殊符号和多余空格
        text = re.sub(r'[#@$%^&*()_+=\[\]{}|\\:";\'<>?,./]', ' ', text)
        text = re.sub(r'\s+', ' ', text)

        # 移除数字串（超过4位的纯数字）
        text = re.sub(r'\b\d{5,}\b', '', text)

        # 清理首尾空格
        text = text.strip()

        logger.info(f"文本清理完成，原长度: {len(text)}，清理后: {len(text)}")
        return text

    def _generate_speech(self, text: str, config: Dict) -> Optional[str]:
        """生成语音文件"""
        try:
            logger.info("开始生成语音...")

            # 清理文本
            cleaned_text = self._clean_text_for_speech(text)
            if not cleaned_text.strip():
                logger.error("清理后的文本为空")
                return None

            logger.info(f"清理后的文本: {cleaned_text[:100]}...")

            # 语音文件路径
            audio_path = os.path.join(self.temp_dir, "speech.wav")
            
            # 尝试使用不同的TTS引擎（按质量排序）
            success = False

            # 方法1: 使用Azure TTS（最自然，需要API密钥）
            if not success:
                success = self._generate_speech_azure(cleaned_text, audio_path, config)

            # 方法2: 使用Edge TTS（免费，质量很好）
            if not success:
                success = self._generate_speech_edge(cleaned_text, audio_path, config)

            # 方法3: 使用gTTS（在线，质量中等）
            if not success:
                success = self._generate_speech_gtts(cleaned_text, audio_path, config)

            # 方法4: 使用Windows SAPI（离线，质量一般）
            if not success and sys.platform == 'win32':
                success = self._generate_speech_sapi(cleaned_text, audio_path, config)

            # 方法5: 使用pyttsx3（离线备用）
            if not success:
                success = self._generate_speech_pyttsx3(cleaned_text, audio_path, config)
            
            if success and os.path.exists(audio_path):
                logger.info(f"语音生成成功: {audio_path}")
                return audio_path
            else:
                logger.error("所有语音生成方法都失败了")
                return None
                
        except Exception as e:
            logger.error(f"语音生成失败: {e}")
            return None
    
    def _generate_speech_pyttsx3(self, text: str, output_path: str, config: Dict) -> bool:
        """使用pyttsx3生成语音"""
        try:
            import pyttsx3
            
            engine = pyttsx3.init()
            
            # 设置语音参数
            voices = engine.getProperty('voices')
            if voices:
                # 优先选择中文语音
                for voice in voices:
                    if 'chinese' in voice.name.lower() or 'zh' in voice.id.lower():
                        engine.setProperty('voice', voice.id)
                        break
            
            # 设置语速和音量
            engine.setProperty('rate', int(200 * config.get('voice_speed', 1.0)))
            engine.setProperty('volume', config.get('voice_volume', 0.8))
            
            # 保存语音
            engine.save_to_file(text, output_path)
            engine.runAndWait()
            
            return os.path.exists(output_path)
            
        except ImportError:
            logger.warning("pyttsx3未安装，跳过此方法")
            return False
        except Exception as e:
            logger.warning(f"pyttsx3语音生成失败: {e}")
            return False
    
    def _generate_speech_gtts(self, text: str, output_path: str, config: Dict) -> bool:
        """使用gTTS生成语音"""
        try:
            from gtts import gTTS
            
            # 创建gTTS对象
            tts = gTTS(text=text, lang='zh', slow=False)
            
            # 保存语音文件
            tts.save(output_path)
            
            return os.path.exists(output_path)
            
        except ImportError:
            logger.warning("gTTS未安装，跳过此方法")
            return False
        except Exception as e:
            logger.warning(f"gTTS语音生成失败: {e}")
            return False
    
    def _generate_speech_sapi(self, text: str, output_path: str, config: Dict) -> bool:
        """使用Windows SAPI生成语音"""
        try:
            import win32com.client
            
            # 创建SAPI语音对象
            voice = win32com.client.Dispatch("SAPI.SpVoice")
            
            # 创建文件流
            file_stream = win32com.client.Dispatch("SAPI.SpFileStream")
            file_stream.Open(output_path, 3)
            voice.AudioOutputStream = file_stream
            
            # 生成语音
            voice.Speak(text)
            file_stream.Close()
            
            return os.path.exists(output_path)
            
        except ImportError:
            logger.warning("pywin32未安装，跳过此方法")
            return False
        except Exception as e:
            logger.warning(f"SAPI语音生成失败: {e}")
            return False

    def _generate_speech_azure(self, text: str, output_path: str, config: Dict) -> bool:
        """使用Azure认知服务TTS生成语音（最自然）"""
        try:
            import azure.cognitiveservices.speech as speechsdk

            # Azure配置（需要用户提供API密钥）
            speech_key = config.get('azure_speech_key')
            service_region = config.get('azure_region', 'eastus')

            if not speech_key:
                logger.info("Azure语音服务密钥未配置，跳过Azure TTS")
                return False

            # 配置语音服务
            speech_config = speechsdk.SpeechConfig(subscription=speech_key, region=service_region)

            # 选择中文语音（更自然的声音）
            voice_name = config.get('azure_voice', 'zh-CN-XiaoxiaoNeural')  # 女声
            speech_config.speech_synthesis_voice_name = voice_name

            # 设置输出格式
            speech_config.set_speech_synthesis_output_format(
                speechsdk.SpeechSynthesisOutputFormat.Audio16Khz32KBitRateMonoMp3
            )

            # 创建合成器
            synthesizer = speechsdk.SpeechSynthesizer(speech_config=speech_config, audio_config=None)

            # 生成语音
            result = synthesizer.speak_text_async(text).get()

            if result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted:
                # 保存音频文件
                with open(output_path, 'wb') as audio_file:
                    audio_file.write(result.audio_data)
                logger.info("Azure TTS语音生成成功")
                return True
            else:
                logger.warning(f"Azure TTS失败: {result.reason}")
                return False

        except ImportError:
            logger.info("Azure Speech SDK未安装，跳过Azure TTS")
            return False
        except Exception as e:
            logger.warning(f"Azure TTS语音生成失败: {e}")
            return False

    def _generate_speech_edge(self, text: str, output_path: str, config: Dict) -> bool:
        """使用Edge TTS生成语音（免费，质量很好）"""
        try:
            import edge_tts
            import asyncio

            # 选择中文语音
            voice = config.get('edge_voice', 'zh-CN-XiaoxiaoNeural')  # 晓晓 - 女声
            # 其他选项:
            # zh-CN-YunxiNeural - 云希 (男声)
            # zh-CN-XiaoyiNeural - 晓伊 (女声)
            # zh-CN-YunjianNeural - 云健 (男声)
            # zh-CN-XiaochenNeural - 晓辰 (女声)

            # 设置语速和音调
            rate = config.get('voice_speed', 1.0)
            pitch = config.get('voice_pitch', 0)  # -50到+50

            # 直接使用纯文本，不使用SSML
            # Edge TTS会自动处理语音参数

            async def generate_audio():
                # 使用简单的文本，让Edge TTS自动处理
                communicate = edge_tts.Communicate(text, voice)
                await communicate.save(output_path)

            # 运行异步函数
            asyncio.run(generate_audio())

            if os.path.exists(output_path):
                logger.info("Edge TTS语音生成成功")
                return True
            else:
                return False

        except ImportError:
            logger.info("edge-tts未安装，跳过Edge TTS")
            return False
        except Exception as e:
            logger.warning(f"Edge TTS语音生成失败: {e}")
            return False

    def _process_images(self, images: List[str], config: Dict) -> Optional[List[str]]:
        """处理图片（调整尺寸、格式等）"""
        try:
            logger.info("开始处理图片...")

            processed_images = []
            target_width = config['video_width']
            target_height = config['video_height']

            for i, img_path in enumerate(images):
                try:
                    from PIL import Image, ImageOps

                    # 打开图片
                    with Image.open(img_path) as img:
                        # 转换为RGB模式
                        if img.mode != 'RGB':
                            img = img.convert('RGB')

                        # 调整尺寸（保持比例，填充背景）
                        img = ImageOps.fit(img, (target_width, target_height), Image.Resampling.LANCZOS)

                        # 保存处理后的图片
                        processed_path = os.path.join(self.temp_dir, f"processed_{i:03d}.jpg")
                        img.save(processed_path, 'JPEG', quality=95)
                        processed_images.append(processed_path)

                except Exception as e:
                    logger.error(f"处理图片失败 {img_path}: {e}")
                    return None

            logger.info(f"图片处理完成，共处理 {len(processed_images)} 张")
            return processed_images

        except ImportError:
            logger.error("PIL/Pillow未安装，无法处理图片")
            return None
        except Exception as e:
            logger.error(f"图片处理失败: {e}")
            return None

    def _generate_subtitles(self, text: str, config: Dict) -> Optional[str]:
        """生成字幕文件"""
        try:
            logger.info("开始生成字幕...")

            subtitle_path = os.path.join(self.temp_dir, "subtitles.srt")

            # 简单的字幕分割（按句号分割）
            sentences = [s.strip() for s in text.split('。') if s.strip()]
            if not sentences:
                sentences = [text]

            # 计算每句话的时间
            total_duration = len(sentences) * config['image_duration']
            duration_per_sentence = total_duration / len(sentences)

            # 生成SRT字幕文件
            with open(subtitle_path, 'w', encoding='utf-8') as f:
                for i, sentence in enumerate(sentences):
                    start_time = i * duration_per_sentence
                    end_time = (i + 1) * duration_per_sentence

                    # SRT时间格式
                    start_srt = self._seconds_to_srt_time(start_time)
                    end_srt = self._seconds_to_srt_time(end_time)

                    f.write(f"{i + 1}\n")
                    f.write(f"{start_srt} --> {end_srt}\n")
                    f.write(f"{sentence}\n\n")

            logger.info(f"字幕生成完成: {subtitle_path}")
            return subtitle_path

        except Exception as e:
            logger.error(f"字幕生成失败: {e}")
            return None

    def _seconds_to_srt_time(self, seconds: float) -> str:
        """将秒数转换为SRT时间格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"

    def _compose_video(
        self,
        images: List[str],
        audio_path: str,
        subtitle_path: Optional[str],
        output_path: str,
        config: Dict
    ) -> bool:
        """合成最终视频"""
        try:
            logger.info("开始合成视频...")

            # 尝试使用不同的视频处理库
            success = False

            # 方法1: 使用moviepy
            if not success:
                success = self._compose_video_moviepy(
                    images, audio_path, subtitle_path, output_path, config
                )

            # 方法2: 使用ffmpeg-python
            if not success:
                success = self._compose_video_ffmpeg(
                    images, audio_path, subtitle_path, output_path, config
                )

            return success

        except Exception as e:
            logger.error(f"视频合成失败: {e}")
            return False

    def _compose_video_moviepy(
        self,
        images: List[str],
        audio_path: str,
        subtitle_path: Optional[str],
        output_path: str,
        config: Dict
    ) -> bool:
        """使用moviepy合成视频"""
        try:
            # 尝试多种导入方式
            try:
                from moviepy.editor import (
                    ImageClip, AudioFileClip, CompositeVideoClip,
                    concatenate_videoclips, TextClip
                )
            except ImportError:
                import moviepy.editor as mp
                ImageClip = mp.ImageClip
                AudioFileClip = mp.AudioFileClip
                CompositeVideoClip = mp.CompositeVideoClip
                concatenate_videoclips = mp.concatenate_videoclips
                TextClip = mp.TextClip

            # 创建图片剪辑
            image_clips = []
            image_duration = config['image_duration']

            for img_path in images:
                clip = ImageClip(img_path, duration=image_duration)
                clip = clip.set_fps(config['fps'])
                image_clips.append(clip)

            # 连接所有图片剪辑
            video_clip = concatenate_videoclips(image_clips, method="compose")

            # 添加音频
            if os.path.exists(audio_path):
                audio_clip = AudioFileClip(audio_path)
                # 调整视频长度以匹配音频
                if audio_clip.duration > video_clip.duration:
                    # 如果音频更长，循环播放视频
                    video_clip = video_clip.loop(duration=audio_clip.duration)
                else:
                    # 如果视频更长，截断音频
                    audio_clip = audio_clip.subclip(0, video_clip.duration)

                video_clip = video_clip.set_audio(audio_clip)

            # 添加字幕
            if subtitle_path and os.path.exists(subtitle_path) and config.get('enable_subtitles', True):
                try:
                    video_clip = self._add_subtitles_to_video(video_clip, subtitle_path, config)
                    logger.info("字幕添加成功")
                except Exception as e:
                    logger.warning(f"字幕添加失败: {e}")

            # 输出视频
            video_clip.write_videofile(
                output_path,
                fps=config['fps'],
                codec='libx264',
                audio_codec='aac',
                temp_audiofile=os.path.join(self.temp_dir, 'temp_audio.m4a'),
                remove_temp=True
            )

            return os.path.exists(output_path)

        except ImportError:
            logger.warning("moviepy未安装，跳过此方法")
            return False
        except Exception as e:
            logger.warning(f"moviepy视频合成失败: {e}")
            return False

    def _compose_video_ffmpeg(
        self,
        images: List[str],
        audio_path: str,
        subtitle_path: Optional[str],
        output_path: str,
        config: Dict
    ) -> bool:
        """使用ffmpeg合成视频"""
        try:
            import ffmpeg

            # 创建图片输入流
            image_duration = config['image_duration']
            fps = config['fps']

            # 创建图片列表文件
            image_list_path = os.path.join(self.temp_dir, "image_list.txt")
            with open(image_list_path, 'w') as f:
                for img_path in images:
                    f.write(f"file '{img_path}'\n")
                    f.write(f"duration {image_duration}\n")
                # 最后一张图片需要重复
                if images:
                    f.write(f"file '{images[-1]}'\n")

            # 创建视频流
            video_input = ffmpeg.input(image_list_path, format='concat', safe=0)
            video_stream = ffmpeg.filter(video_input, 'fps', fps=fps)

            # 添加音频
            if os.path.exists(audio_path):
                audio_input = ffmpeg.input(audio_path)
                output = ffmpeg.output(
                    video_stream, audio_input, output_path,
                    vcodec='libx264', acodec='aac',
                    pix_fmt='yuv420p'
                )
            else:
                output = ffmpeg.output(
                    video_stream, output_path,
                    vcodec='libx264',
                    pix_fmt='yuv420p'
                )

            # 运行ffmpeg
            ffmpeg.run(output, overwrite_output=True, quiet=True)

            return os.path.exists(output_path)

        except ImportError:
            logger.warning("ffmpeg-python未安装，跳过此方法")
            return False
        except Exception as e:
            logger.warning(f"ffmpeg视频合成失败: {e}")
            return False

    def _add_subtitles_to_video(self, video_clip, subtitle_path: str, config: Dict):
        """为视频添加字幕"""
        try:
            from moviepy.editor import TextClip, CompositeVideoClip

            # 读取SRT字幕文件
            subtitles = self._parse_srt_file(subtitle_path)
            if not subtitles:
                logger.warning("字幕文件为空或解析失败")
                return video_clip

            # 创建字幕剪辑列表
            subtitle_clips = []

            for subtitle in subtitles:
                start_time = subtitle['start']
                end_time = subtitle['end']
                text = subtitle['text']

                if not text.strip():
                    continue

                # 创建文字剪辑
                txt_clip = self._create_text_clip(text, config, video_clip.size)

                # 设置时间和位置
                txt_clip = txt_clip.set_start(start_time).set_duration(end_time - start_time)
                txt_clip = self._position_subtitle(txt_clip, config, video_clip.size)

                subtitle_clips.append(txt_clip)

            # 合成视频和字幕
            if subtitle_clips:
                final_clip = CompositeVideoClip([video_clip] + subtitle_clips)
                logger.info(f"添加了 {len(subtitle_clips)} 个字幕片段")
                return final_clip
            else:
                logger.warning("没有有效的字幕片段")
                return video_clip

        except Exception as e:
            logger.error(f"字幕添加过程中出错: {e}")
            import traceback
            traceback.print_exc()
            return video_clip

    def _parse_srt_file(self, subtitle_path: str) -> List[Dict]:
        """解析SRT字幕文件"""
        subtitles = []

        try:
            with open(subtitle_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()

            # 分割字幕块
            blocks = content.split('\n\n')

            for block in blocks:
                lines = block.strip().split('\n')
                if len(lines) >= 3:
                    # 解析时间
                    time_line = lines[1]
                    if ' --> ' in time_line:
                        start_str, end_str = time_line.split(' --> ')
                        start_time = self._srt_time_to_seconds(start_str)
                        end_time = self._srt_time_to_seconds(end_str)

                        # 合并文本行
                        text = ' '.join(lines[2:])

                        subtitles.append({
                            'start': start_time,
                            'end': end_time,
                            'text': text
                        })

            logger.info(f"解析了 {len(subtitles)} 个字幕")
            return subtitles

        except Exception as e:
            logger.error(f"SRT文件解析失败: {e}")
            return []

    def _srt_time_to_seconds(self, time_str: str) -> float:
        """将SRT时间格式转换为秒数"""
        try:
            # 格式: 00:00:10,500
            time_part, ms_part = time_str.split(',')
            h, m, s = map(int, time_part.split(':'))
            ms = int(ms_part)

            return h * 3600 + m * 60 + s + ms / 1000.0
        except:
            return 0.0

    def _create_text_clip(self, text: str, config: Dict, video_size: tuple):
        """创建文字剪辑"""
        try:
            from moviepy.editor import TextClip

            # 字体设置
            font_size = config.get('subtitle_font_size', 48)
            font_color = config.get('subtitle_font_color', 'white')

            # 创建文字剪辑
            txt_clip = TextClip(
                text,
                fontsize=font_size,
                color=font_color,
                font='Arial',  # 可以改为支持中文的字体
                method='caption',
                size=(video_size[0] * 0.8, None),  # 宽度为视频的80%
                align='center'
            )

            # 添加背景
            txt_clip = self._add_subtitle_background(txt_clip, config)

            return txt_clip

        except Exception as e:
            logger.error(f"创建文字剪辑失败: {e}")
            # 返回一个空的剪辑
            from moviepy.editor import ColorClip
            return ColorClip(size=(1, 1), color=(0, 0, 0), duration=0.1).set_opacity(0)

    def _add_subtitle_background(self, txt_clip, config: Dict):
        """为字幕添加背景"""
        try:
            from moviepy.editor import ColorClip, CompositeVideoClip

            bg_style = config.get('subtitle_background', '半透明黑色背景')

            if bg_style == "无背景":
                return txt_clip

            # 获取文字剪辑的尺寸
            txt_size = txt_clip.size

            # 创建背景
            if bg_style == "半透明黑色背景":
                bg_clip = ColorClip(
                    size=(txt_size[0] + 20, txt_size[1] + 10),
                    color=(0, 0, 0)
                ).set_opacity(0.7)
            elif bg_style == "纯色黑色背景":
                bg_clip = ColorClip(
                    size=(txt_size[0] + 20, txt_size[1] + 10),
                    color=(0, 0, 0)
                ).set_opacity(1.0)
            elif bg_style == "白色半透明背景":
                bg_clip = ColorClip(
                    size=(txt_size[0] + 20, txt_size[1] + 10),
                    color=(255, 255, 255)
                ).set_opacity(0.7)
            elif bg_style == "彩色渐变背景":
                # 简化为纯色背景（渐变需要更复杂的处理）
                bg_clip = ColorClip(
                    size=(txt_size[0] + 20, txt_size[1] + 10),
                    color=(50, 50, 150)
                ).set_opacity(0.8)
            else:
                # 默认半透明黑色
                bg_clip = ColorClip(
                    size=(txt_size[0] + 20, txt_size[1] + 10),
                    color=(0, 0, 0)
                ).set_opacity(0.7)

            # 合成背景和文字
            return CompositeVideoClip([
                bg_clip,
                txt_clip.set_position('center')
            ])

        except Exception as e:
            logger.warning(f"添加字幕背景失败: {e}")
            return txt_clip

    def _position_subtitle(self, txt_clip, config: Dict, video_size: tuple):
        """设置字幕位置"""
        try:
            position = config.get('subtitle_position', 'bottom_center')

            if position == 'bottom_center':
                return txt_clip.set_position(('center', video_size[1] - txt_clip.size[1] - 50))
            elif position == 'top_center':
                return txt_clip.set_position(('center', 50))
            elif position == 'center':
                return txt_clip.set_position('center')
            elif position == 'bottom_left':
                return txt_clip.set_position((50, video_size[1] - txt_clip.size[1] - 50))
            elif position == 'bottom_right':
                return txt_clip.set_position((video_size[0] - txt_clip.size[0] - 50, video_size[1] - txt_clip.size[1] - 50))
            else:
                # 默认底部居中
                return txt_clip.set_position(('center', video_size[1] - txt_clip.size[1] - 50))

        except Exception as e:
            logger.warning(f"设置字幕位置失败: {e}")
            return txt_clip.set_position(('center', 'bottom'))
