<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->

<typesystem package="PySide6.QtNetworkAuth"
            namespace-begin="QT_BEGIN_NAMESPACE" namespace-end="QT_END_NAMESPACE">
    <load-typesystem name="typesystem_network.xml" generate="no"/>
    <object-type name="QAbstractOAuth">
        <enum-type name="ContentType"/>
        <enum-type name="Error"/>
        <enum-type name="Stage"/>
        <enum-type name="Status"/>
        <inject-code class="native" position="beginning" file="../glue/qtnetworkauth.cpp"
                     snippet="qabstractoauth-lookuphost-functor"/>
        <modify-function signature="setReplyHandler(QAbstractOAuthReplyHandler*)">
            <modify-argument index="1">
                <define-ownership class="target" owner="c++"/>
            </modify-argument>
        </modify-function>
        <add-function signature="setModifyParametersFunction(PyObject* @modifyParametersFunction@)">
            <inject-code class="target" position="beginning" file="../glue/qtnetworkauth.cpp" snippet="qabstractoauth-setmodifyparametersfunction"/>
        </add-function>
    </object-type>
    <object-type name="QAbstractOAuth2">
        <enum-type name="NonceMode" since="6.9"/>
        <modify-function signature="head(const QUrl&amp;,const QMap&lt;QString,QVariant&gt;&amp;)">
            <modify-argument index="return">
                <define-ownership class="target" owner="default"/>
            </modify-argument>
        </modify-function>
        <modify-function signature="get(const QUrl&amp;,const QMap&lt;QString,QVariant&gt;&amp;)">
            <modify-argument index="return">
                <define-ownership class="target" owner="default"/>
            </modify-argument>
        </modify-function>
        <modify-function signature="post(const QUrl&amp;,const QMap&lt;QString,QVariant&gt;&amp;)">
            <modify-argument index="return">
                <define-ownership class="target" owner="default"/>
            </modify-argument>
        </modify-function>
        <modify-function signature="post(const QUrl&amp;,const QByteArray&amp;)">
            <modify-argument index="return">
                <define-ownership class="target" owner="default"/>
            </modify-argument>
        </modify-function>
        <modify-function signature="post(const QUrl&amp;,QHttpMultiPart*)">
            <modify-argument index="return">
                <define-ownership class="target" owner="default"/>
            </modify-argument>
        </modify-function>
        <modify-function signature="put(const QUrl&amp;,const QMap&lt;QString,QVariant&gt;&amp;)">
            <modify-argument index="return">
                <define-ownership class="target" owner="default"/>
            </modify-argument>
        </modify-function>
        <modify-function signature="put(const QUrl&amp;,const QByteArray&amp;)">
            <modify-argument index="return">
                <define-ownership class="target" owner="default"/>
            </modify-argument>
        </modify-function>
        <modify-function signature="put(const QUrl&amp;,QHttpMultiPart*)">
            <modify-argument index="return">
                <define-ownership class="target" owner="default"/>
            </modify-argument>
        </modify-function>
        <modify-function signature="deleteResource(const QUrl&amp;,const QMap&lt;QString,QVariant&gt;&amp;)">
            <modify-argument index="return">
                <define-ownership class="target" owner="default"/>
            </modify-argument>
        </modify-function>
    </object-type>
    <object-type name="QOAuth2DeviceAuthorizationFlow" since="6.9"/>
    <object-type name="QAbstractOAuthReplyHandler"/>
    <object-type name="QOAuth1">
        <enum-type name="SignatureMethod"/>
        <modify-function signature="head(const QUrl&amp;,const QMap&lt;QString,QVariant&gt;&amp;)">
            <modify-argument index="return">
                <define-ownership class="target" owner="default"/>
            </modify-argument>
        </modify-function>
        <modify-function signature="get(const QUrl&amp;,const QMap&lt;QString,QVariant&gt;&amp;)">
            <modify-argument index="return">
                <define-ownership class="target" owner="default"/>
            </modify-argument>
        </modify-function>
        <modify-function signature="post(const QUrl&amp;,const QMap&lt;QString,QVariant&gt;&amp;)">
            <modify-argument index="return">
                <define-ownership class="target" owner="default"/>
            </modify-argument>
        </modify-function>
        <modify-function signature="put(const QUrl&amp;,const QMap&lt;QString,QVariant&gt;&amp;)">
            <modify-argument index="return">
                <define-ownership class="target" owner="default"/>
            </modify-argument>
        </modify-function>
        <modify-function signature="deleteResource(const QUrl&amp;,const QMap&lt;QString,QVariant&gt;&amp;)">
            <modify-argument index="return">
                <define-ownership class="target" owner="default"/>
            </modify-argument>
        </modify-function>
    </object-type>
    <value-type name="QOAuth1Signature">
        <enum-type name="HttpRequestMethod"/>
    </value-type>
    <object-type name="QOAuth2AuthorizationCodeFlow">
        <enum-type name="PkceMethod" since="6.8"/>
    </object-type>
    <object-type name="QOAuthHttpServerReplyHandler"/>
    <object-type name="QOAuthOobReplyHandler"/>
    <object-type name="QOAuthUriSchemeReplyHandler" since="6.8"/>

    <suppress-warning text="^.*Typedef used on signal QAbstractOAuth.*$"/>
</typesystem>
