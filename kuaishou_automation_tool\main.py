"""
快手无人带货工具 - 主程序入口
Kuaishou Automation Tool - Main Entry Point

本模块是应用程序的主入口点，负责：
- 初始化应用程序环境
- 配置数据库连接
- 启动主窗口界面
- 处理全局异常
"""

import sys
import os
import traceback

# 动态导入Qt库，避免IDE警告
def import_qt_library():
    """动态导入Qt库"""
    try:
        from PySide6.QtWidgets import QApplication, QMessageBox
        from PySide6.QtCore import Qt
        from PySide6.QtGui import QFont
        print("✅ 使用PySide6库")
        return "PySide6", QApplication, QMessageBox, Qt, QFont
    except ImportError:
        try:
            # 动态导入PyQt6以避免IDE警告
            import importlib
            QtWidgets = importlib.import_module('PyQt6.QtWidgets')
            QtCore = importlib.import_module('PyQt6.QtCore')
            QtGui = importlib.import_module('PyQt6.QtGui')

            QApplication = QtWidgets.QApplication
            QMessageBox = QtWidgets.QMessageBox
            Qt = QtCore.Qt
            QFont = QtGui.QFont

            print("✅ 使用PyQt6库")
            return "PyQt6", QApplication, QMessageBox, Qt, QFont
        except ImportError as e:
            print("❌ 无法导入Qt库，请安装PySide6或PyQt6")
            print(f"错误详情: {e}")
            print("\n解决方案:")
            print("1. 安装PySide6: pip install PySide6")
            print("2. 或安装Visual C++ Redistributable后使用PyQt6")
            print("3. 或使用命令行版本: python main_cli.py")
            sys.exit(1)

# 执行动态导入
QT_LIBRARY, QApplication, QMessageBox, Qt, QFont = import_qt_library()

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 尝试导入模块，处理相对导入问题
try:
    from ui.main_window import MainWindow
    from data.models.base import DatabaseManager, ConfigManager
    from utils.logger import setup_logger
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    try:
        from kuaishou_automation_tool.ui.main_window import MainWindow
        from kuaishou_automation_tool.data.models.base import DatabaseManager, ConfigManager
        from kuaishou_automation_tool.utils.logger import setup_logger
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("💡 请使用 'python start.py --gui' 启动应用程序")
        sys.exit(1)


class KuaishouAutomationApp:
    """
    快手无人带货工具应用程序类
    
    负责应用程序的初始化、配置和启动。
    采用单例模式确保全局唯一的应用实例。
    
    Attributes:
        app (QApplication): Qt应用程序实例
        main_window (MainWindow): 主窗口实例
        config_manager (ConfigManager): 配置管理器
        db_manager (DatabaseManager): 数据库管理器
        logger: 日志记录器
    """
    
    def __init__(self):
        """初始化应用程序"""
        self.app = None
        self.main_window = None
        self.config_manager = None
        self.db_manager = None
        self.logger = None
        
    def initialize(self):
        """
        初始化应用程序环境
        
        包括配置管理、数据库连接、日志系统等。
        """
        try:
            # 初始化配置管理器
            self.config_manager = ConfigManager()
            
            # 初始化日志系统
            self.logger = setup_logger(
                log_level=self.config_manager.get('logging.level', 'INFO'),
                log_file=self.config_manager.get('logging.file_path', 'logs/app.log')
            )
            self.logger.info("应用程序启动中...")
            
            # 初始化数据库
            self.db_manager = DatabaseManager()
            self.db_manager.initialize()
            self.logger.info("数据库初始化完成")
            
            # 创建Qt应用程序
            self.app = QApplication(sys.argv)
            self.setup_application()
            
            # 创建主窗口
            self.main_window = MainWindow()
            self.setup_main_window()
            
            self.logger.info("应用程序初始化完成")
            
        except Exception as e:
            self.handle_initialization_error(e)
            
    def setup_application(self):
        """设置Qt应用程序属性"""
        # 设置应用程序信息
        self.app.setApplicationName(self.config_manager.get('app.name', '快手无人带货工具'))
        self.app.setApplicationVersion(self.config_manager.get('app.version', '1.0.0'))
        self.app.setOrganizationName("Kuaishou Automation")
        
        # 设置应用程序图标（如果有的话）
        # self.app.setWindowIcon(QIcon('resources/icons/app_icon.png'))
        
        # 设置全局字体
        font = QFont("Microsoft YaHei", 9)
        self.app.setFont(font)
        
        # 设置高DPI支持 (PySide6中这些属性已弃用，Qt6默认启用)
        try:
            if hasattr(Qt.ApplicationAttribute, 'AA_EnableHighDpiScaling'):
                self.app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
            if hasattr(Qt.ApplicationAttribute, 'AA_UseHighDpiPixmaps'):
                self.app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
        except AttributeError:
            # PySide6中这些属性可能不存在，忽略即可
            pass
        
    def setup_main_window(self):
        """设置主窗口属性"""
        if self.main_window:
            # 从配置中恢复窗口位置和大小
            window_size = self.config_manager.get('ui.window_size', [1200, 800])
            window_position = self.config_manager.get('ui.window_position', [100, 100])
            
            self.main_window.resize(window_size[0], window_size[1])
            self.main_window.move(window_position[0], window_position[1])
            
            # 连接窗口关闭信号
            self.main_window.closeEvent = self.on_main_window_close
            
    def run(self):
        """
        运行应用程序
        
        显示主窗口并启动事件循环。
        
        Returns:
            int: 应用程序退出代码
        """
        try:
            if self.main_window:
                self.main_window.show()
                self.logger.info("主窗口已显示")
                
                # 启动事件循环
                return self.app.exec()
            else:
                self.logger.error("主窗口未初始化")
                return 1
                
        except Exception as e:
            self.logger.error(f"应用程序运行时错误: {e}")
            self.logger.error(traceback.format_exc())
            return 1
            
    def on_main_window_close(self, event):
        """
        处理主窗口关闭事件
        
        Args:
            event: 关闭事件对象
        """
        try:
            # 保存窗口位置和大小
            if self.main_window and self.config_manager:
                geometry = self.main_window.geometry()
                self.config_manager.set('ui.window_size', [geometry.width(), geometry.height()])
                self.config_manager.set('ui.window_position', [geometry.x(), geometry.y()])
                self.config_manager.save_config()
                
            # 清理资源
            self.cleanup()
            
            self.logger.info("应用程序正常退出")
            event.accept()
            
        except Exception as e:
            self.logger.error(f"关闭应用程序时发生错误: {e}")
            event.accept()
            
    def cleanup(self):
        """清理应用程序资源"""
        try:
            # 关闭数据库连接
            if self.db_manager:
                self.db_manager.close()
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"清理资源时发生错误: {e}")
                
    def handle_initialization_error(self, error):
        """
        处理初始化错误
        
        Args:
            error: 错误对象
        """
        error_msg = f"应用程序初始化失败: {error}"
        print(error_msg)
        print(traceback.format_exc())
        
        # 如果Qt应用已创建，显示错误对话框
        if self.app:
            QMessageBox.critical(
                None, "初始化错误", 
                f"应用程序初始化失败:\n\n{error}\n\n请检查配置和依赖环境。"
            )
        
        sys.exit(1)


def main():
    """
    主函数 - 应用程序入口点
    
    创建应用程序实例并运行。
    """
    # 设置异常处理
    def handle_exception(exc_type, exc_value, exc_traceback):
        """全局异常处理器"""
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
            
        error_msg = "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        print(f"未处理的异常: {error_msg}")
        
        # 如果有Qt应用程序实例，显示错误对话框
        app = QApplication.instance()
        if app:
            QMessageBox.critical(
                None, "程序错误",
                f"程序遇到未处理的错误:\n\n{exc_value}\n\n程序将退出。"
            )
    
    sys.excepthook = handle_exception
    
    # 创建并运行应用程序
    kuaishou_app = KuaishouAutomationApp()
    kuaishou_app.initialize()
    exit_code = kuaishou_app.run()
    
    sys.exit(exit_code)


if __name__ == "__main__":
    main()
