#!/usr/bin/env python3
"""
测试修复后的Edge TTS功能
"""

import os
import sys
import asyncio

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_edge_tts_simple():
    """测试简化后的Edge TTS"""
    print("🎤 测试修复后的Edge TTS...")
    
    try:
        import edge_tts
        
        # 测试文本
        test_text = "欢迎来到我们的直播间！今天为大家推荐这款超值好物。这个产品质量优良、价格实惠、性价比超高。"
        
        # 不同的语音角色
        voices = {
            "晓晓-女声": "zh-CN-XiaoxiaoNeural",
            "云希-男声": "zh-CN-YunxiNeural"
        }
        
        async def generate_simple_voice():
            for name, voice in voices.items():
                output_path = f"test_fixed_{name}.mp3"
                print(f"  生成 {name} 语音样本...")
                
                # 使用简单的文本，不使用SSML
                communicate = edge_tts.Communicate(test_text, voice)
                await communicate.save(output_path)
                
                if os.path.exists(output_path):
                    file_size = os.path.getsize(output_path) / 1024  # KB
                    print(f"  ✅ {name}: {file_size:.1f} KB")
                    
                    # 播放测试（可选）
                    print(f"  📁 文件保存在: {output_path}")
                else:
                    print(f"  ❌ {name}: 生成失败")
        
        # 运行异步函数
        asyncio.run(generate_simple_voice())
        
        print("✅ Edge TTS修复测试完成")
        return True
        
    except ImportError:
        print("❌ edge-tts未安装")
        return False
    except Exception as e:
        print(f"❌ Edge TTS测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_text_cleaning_with_xml():
    """测试包含XML标签的文本清理"""
    print("\n🧹 测试XML标签清理...")
    
    # 模拟可能出现的SSML内容
    test_texts = [
        # 正常文本
        "欢迎来到我们的直播间！今天为大家推荐这款超值好物。",
        
        # 包含XML标签的文本
        '<speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis">欢迎来到我们的直播间！</speak>',
        
        # 包含复杂SSML的文本
        '''<speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="zh-CN">
            <voice name="zh-CN-XiaoxiaoNeural">
                <prosody rate="+0%" pitch="+0Hz">
                    欢迎来到我们的直播间！今天为大家推荐这款超值好物。
                </prosody>
            </voice>
        </speak>''',
        
        # 包含网址和XML的混合文本
        '访问 https://www.example.com <voice name="test">这是测试文本</voice> 联系我们'
    ]
    
    from business.video_composer import VideoComposer
    composer = VideoComposer()
    
    for i, text in enumerate(test_texts, 1):
        print(f"\n📝 测试文本 {i}:")
        print(f"原始: {text[:50]}{'...' if len(text) > 50 else ''}")
        
        cleaned = composer._clean_text_for_speech(text)
        print(f"清理后: {cleaned}")
        
        # 检查是否还有XML标签
        has_xml = '<' in cleaned or '>' in cleaned
        has_xmlns = 'xmlns' in cleaned.lower()
        
        print(f"XML标签清理: {'❌ 仍有残留' if has_xml else '✅ 完全清理'}")
        print(f"命名空间清理: {'❌ 仍有残留' if has_xmlns else '✅ 完全清理'}")

def compare_before_after():
    """对比修复前后的效果"""
    print("\n📊 修复前后对比:")
    print("=" * 50)
    
    print("❌ 修复前的问题:")
    print("• 语音会读出: 'XMLNS=HTTP冒号斜杠斜杠...'")
    print("• 语音会读出: 'voice name=ZHCNZXPRO...'")
    print("• 语音会读出: 'rate等于+0%配等于加0...'")
    print("• 包含大量XML标签内容")
    
    print("\n✅ 修复后的效果:")
    print("• 移除了所有SSML标签")
    print("• 使用纯文本进行语音合成")
    print("• 语音只读产品介绍内容")
    print("• 声音自然流畅")
    
    print("\n🔧 技术改进:")
    print("• 不再使用复杂的SSML格式")
    print("• Edge TTS自动处理语音参数")
    print("• 增强的文本清理功能")
    print("• 移除XML/HTML标签")

def main():
    """主函数"""
    print("🎤 Edge TTS修复验证工具")
    print("=" * 50)
    print("这个工具用于验证Edge TTS不再读出XML标签的修复效果。")
    print()
    
    # 测试文本清理
    test_text_cleaning_with_xml()
    
    # 测试Edge TTS
    edge_ok = test_edge_tts_simple()
    
    # 显示对比
    compare_before_after()
    
    print("\n" + "=" * 50)
    print("📋 修复总结:")
    if edge_ok:
        print("✅ Edge TTS现在使用纯文本，不会读出XML标签")
        print("✅ 语音合成效果更加自然")
        print("✅ 文本清理功能增强，支持XML标签移除")
    else:
        print("❌ Edge TTS测试失败，请检查安装")
    
    print("\n💡 使用建议:")
    print("• 现在可以放心使用Edge TTS语音合成")
    print("• 语音只会读出产品介绍内容")
    print("• 不会再有奇怪的XML标签读音")
    
    # 询问是否清理测试文件
    try:
        choice = input("\n是否删除测试语音文件？(y/n): ").lower()
        if choice in ['y', 'yes', '是']:
            test_files = ["test_fixed_晓晓-女声.mp3", "test_fixed_云希-男声.mp3"]
            for file in test_files:
                if os.path.exists(file):
                    try:
                        os.remove(file)
                        print(f"删除: {file}")
                    except:
                        pass
    except:
        pass
    
    return edge_ok

if __name__ == "__main__":
    try:
        success = main()
        input("\n按回车键退出...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
        sys.exit(1)
