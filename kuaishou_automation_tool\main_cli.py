"""
快手无人带货工具 - 命令行版本
Kuaishou Automation Tool - Command Line Interface

本模块提供命令行界面版本，不依赖GUI组件，
可以在任何Python环境中运行核心功能。
"""

import sys
import os
import json
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from data.models.base import DatabaseManager, ConfigManager
from data.models.account import Account, AccountStatus, AccountType
from data.repositories.account_repository import AccountRepository
from utils.logger import setup_logger, get_logger


class KuaishouAutomationCLI:
    """
    快手无人带货工具命令行界面
    
    提供完整的账号管理功能，通过命令行交互。
    """
    
    def __init__(self):
        """初始化CLI应用"""
        self.logger = setup_logger("KuaishouCLI", "INFO")
        self.config_manager = ConfigManager()
        self.db_manager = DatabaseManager()
        self.account_repository = AccountRepository()
        
        # 初始化数据库
        self.db_manager.initialize()
        self.logger.info("数据库初始化完成")
        
    def show_banner(self):
        """显示应用横幅"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                    快手无人带货自动化工具                      ║
║                  Kuaishou Automation Tool                    ║
║                      命令行版本 v1.0.0                       ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
        
    def show_main_menu(self):
        """显示主菜单"""
        menu = """
┌─────────────────── 主菜单 ───────────────────┐
│  1. 账号管理                                 │
│  2. 查看统计信息                             │
│  3. 系统设置                                 │
│  4. 运行测试                                 │
│  0. 退出程序                                 │
└─────────────────────────────────────────────┘
        """
        print(menu)
        
    def show_account_menu(self):
        """显示账号管理菜单"""
        menu = """
┌─────────────────── 账号管理 ─────────────────┐
│  1. 查看所有账号                             │
│  2. 添加新账号                               │
│  3. 编辑账号                                 │
│  4. 删除账号                                 │
│  5. 搜索账号                                 │
│  6. 更新账号状态                             │
│  0. 返回主菜单                               │
└─────────────────────────────────────────────┘
        """
        print(menu)
        
    def list_accounts(self):
        """列出所有账号"""
        print("\n" + "="*80)
        print("                              账号列表")
        print("="*80)
        
        accounts = self.account_repository.get_all()
        
        if not accounts:
            print("暂无账号数据")
            return
            
        # 表头
        print(f"{'ID':<4} {'用户名':<15} {'昵称':<20} {'状态':<8} {'类型':<10} {'最后登录':<20}")
        print("-" * 80)
        
        # 账号数据
        for account in accounts:
            print(f"{account.id:<4} {account.username:<15} {account.nickname:<20} "
                  f"{account.get_status_display():<8} {account.get_type_display():<10} "
                  f"{account.get_last_login_display():<20}")
                  
        print("-" * 80)
        print(f"总计: {len(accounts)} 个账号")
        
    def add_account(self):
        """添加新账号"""
        print("\n" + "="*50)
        print("                添加新账号")
        print("="*50)
        
        try:
            # 收集账号信息
            username = input("请输入用户名: ").strip()
            if not username:
                print("❌ 用户名不能为空")
                return
                
            # 检查用户名是否已存在
            if self.account_repository.exists_username(username):
                print("❌ 用户名已存在")
                return
                
            nickname = input("请输入昵称: ").strip()
            if not nickname:
                print("❌ 昵称不能为空")
                return
                
            phone = input("请输入手机号 (可选): ").strip()
            email = input("请输入邮箱 (可选): ").strip()
            remarks = input("请输入备注 (可选): ").strip()
            
            # 选择账号类型
            print("\n账号类型:")
            print("1. 主账号")
            print("2. 备用账号")
            print("3. 测试账号")
            
            type_choice = input("请选择账号类型 (1-3): ").strip()
            account_type_map = {
                "1": AccountType.MAIN,
                "2": AccountType.BACKUP,
                "3": AccountType.TEST
            }
            account_type = account_type_map.get(type_choice, AccountType.MAIN)
            
            # 创建账号数据
            account_data = {
                'username': username,
                'nickname': nickname,
                'phone': phone if phone else None,
                'email': email if email else None,
                'remarks': remarks if remarks else None,
                'account_type': account_type,
                'status': AccountStatus.OFFLINE
            }
            
            # 保存账号
            created_account = self.account_repository.create(account_data)
            if created_account:
                print(f"✅ 账号创建成功！ID: {created_account.id}")
                self.logger.info(f"创建账号: {username}")
            else:
                print("❌ 账号创建失败")
                
        except ValueError as e:
            print(f"❌ 输入错误: {e}")
        except Exception as e:
            print(f"❌ 创建失败: {e}")
            self.logger.error(f"创建账号失败: {e}")
            
    def search_accounts(self):
        """搜索账号"""
        print("\n" + "="*50)
        print("                搜索账号")
        print("="*50)
        
        keyword = input("请输入搜索关键词 (用户名或昵称): ").strip()
        if not keyword:
            print("❌ 搜索关键词不能为空")
            return
            
        accounts = self.account_repository.search(keyword)
        
        if not accounts:
            print("未找到匹配的账号")
            return
            
        print(f"\n找到 {len(accounts)} 个匹配的账号:")
        print("-" * 80)
        print(f"{'ID':<4} {'用户名':<15} {'昵称':<20} {'状态':<8} {'类型':<10}")
        print("-" * 80)
        
        for account in accounts:
            print(f"{account.id:<4} {account.username:<15} {account.nickname:<20} "
                  f"{account.get_status_display():<8} {account.get_type_display():<10}")
                  
    def show_statistics(self):
        """显示统计信息"""
        print("\n" + "="*60)
        print("                        统计信息")
        print("="*60)
        
        stats = self.account_repository.get_statistics()
        
        print(f"📊 账号总数: {stats['total_count']}")
        print("\n📈 状态分布:")
        for status, count in stats['status_count'].items():
            print(f"   {status}: {count}")
            
        print("\n📋 类型分布:")
        for account_type, count in stats['type_count'].items():
            print(f"   {account_type}: {count}")
            
        # 获取在线账号
        online_accounts = self.account_repository.get_online_accounts()
        print(f"\n🟢 当前在线账号: {len(online_accounts)}")
        
        if online_accounts:
            for account in online_accounts[:5]:  # 显示前5个
                print(f"   - {account.nickname} ({account.username})")
            if len(online_accounts) > 5:
                print(f"   ... 还有 {len(online_accounts) - 5} 个")
                
    def update_account_status(self):
        """更新账号状态"""
        print("\n" + "="*50)
        print("              更新账号状态")
        print("="*50)
        
        try:
            account_id = int(input("请输入账号ID: "))
            account = self.account_repository.get_by_id(account_id)
            
            if not account:
                print("❌ 账号不存在")
                return
                
            print(f"\n当前账号: {account.nickname} ({account.username})")
            print(f"当前状态: {account.get_status_display()}")
            
            print("\n新状态选项:")
            print("1. 在线")
            print("2. 离线")
            print("3. 已封禁")
            print("4. 待验证")
            print("5. 异常")
            
            status_choice = input("请选择新状态 (1-5): ").strip()
            status_map = {
                "1": AccountStatus.ONLINE,
                "2": AccountStatus.OFFLINE,
                "3": AccountStatus.SUSPENDED,
                "4": AccountStatus.PENDING,
                "5": AccountStatus.ERROR
            }
            
            new_status = status_map.get(status_choice)
            if not new_status:
                print("❌ 无效的状态选择")
                return
                
            # 更新状态
            success = self.account_repository.update_status(account_id, new_status)
            if success:
                print(f"✅ 状态更新成功: {new_status.value}")
                self.logger.info(f"更新账号状态: {account.username} -> {new_status.value}")
            else:
                print("❌ 状态更新失败")
                
        except ValueError:
            print("❌ 请输入有效的账号ID")
        except Exception as e:
            print(f"❌ 更新失败: {e}")
            
    def run_tests(self):
        """运行系统测试"""
        print("\n" + "="*60)
        print("                      系统测试")
        print("="*60)
        
        print("🧪 正在运行核心功能测试...")
        
        # 导入并运行测试
        try:
            from test_core import run_core_tests
            success = run_core_tests()
            
            if success:
                print("\n🎉 所有测试通过！系统运行正常。")
            else:
                print("\n⚠️ 部分测试失败，请检查系统状态。")
                
        except Exception as e:
            print(f"❌ 测试运行失败: {e}")
            
    def run(self):
        """运行CLI应用"""
        self.show_banner()
        
        while True:
            self.show_main_menu()
            choice = input("请选择操作 (0-4): ").strip()
            
            if choice == "0":
                print("\n👋 感谢使用快手无人带货工具！")
                break
            elif choice == "1":
                self.handle_account_management()
            elif choice == "2":
                self.show_statistics()
            elif choice == "3":
                print("\n⚙️ 系统设置功能开发中...")
            elif choice == "4":
                self.run_tests()
            else:
                print("❌ 无效的选择，请重新输入")
                
            input("\n按回车键继续...")
            
    def handle_account_management(self):
        """处理账号管理"""
        while True:
            self.show_account_menu()
            choice = input("请选择操作 (0-6): ").strip()
            
            if choice == "0":
                break
            elif choice == "1":
                self.list_accounts()
            elif choice == "2":
                self.add_account()
            elif choice == "3":
                print("\n✏️ 编辑账号功能开发中...")
            elif choice == "4":
                print("\n🗑️ 删除账号功能开发中...")
            elif choice == "5":
                self.search_accounts()
            elif choice == "6":
                self.update_account_status()
            else:
                print("❌ 无效的选择，请重新输入")
                
            if choice != "0":
                input("\n按回车键继续...")


def main():
    """主函数"""
    try:
        cli = KuaishouAutomationCLI()
        cli.run()
    except KeyboardInterrupt:
        print("\n\n👋 程序已退出")
    except Exception as e:
        print(f"\n❌ 程序运行错误: {e}")
        

if __name__ == "__main__":
    main()
