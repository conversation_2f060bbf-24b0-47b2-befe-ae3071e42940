"""
WebDriver环境检查工具
检查系统中可用的WebDriver和浏览器
"""

import sys
import subprocess
import os
from pathlib import Path

def check_browser_version(browser_name, command):
    """检查浏览器版本"""
    try:
        result = subprocess.run(command, capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ {browser_name}: {version}")
            return True
        else:
            print(f"❌ {browser_name}: 未安装或无法访问")
            return False
    except Exception as e:
        print(f"❌ {browser_name}: 检查失败 - {e}")
        return False

def check_webdriver(driver_name, command):
    """检查WebDriver"""
    try:
        result = subprocess.run(command, capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ {driver_name}: {version}")
            return True
        else:
            print(f"❌ {driver_name}: 未安装或不在PATH中")
            return False
    except Exception as e:
        print(f"❌ {driver_name}: 检查失败 - {e}")
        return False

def test_selenium_webdriver():
    """测试Selenium WebDriver"""
    print("\n🔍 测试Selenium WebDriver...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options as ChromeOptions
        from selenium.webdriver.firefox.options import Options as FirefoxOptions
        from selenium.webdriver.edge.options import Options as EdgeOptions
        print("✅ Selenium库导入成功")
    except ImportError as e:
        print(f"❌ Selenium库导入失败: {e}")
        return False
    
    # 测试Chrome
    print("\n📋 测试Chrome WebDriver...")
    try:
        options = ChromeOptions()
        options.add_argument("--headless")  # 无头模式测试
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        
        driver = webdriver.Chrome(options=options)
        driver.get("https://www.baidu.com")
        title = driver.title
        driver.quit()
        print(f"✅ Chrome WebDriver测试成功 - 页面标题: {title}")
        return True
    except Exception as e:
        print(f"❌ Chrome WebDriver测试失败: {e}")
    
    # 测试Firefox
    print("\n📋 测试Firefox WebDriver...")
    try:
        options = FirefoxOptions()
        options.add_argument("--headless")  # 无头模式测试
        
        driver = webdriver.Firefox(options=options)
        driver.get("https://www.baidu.com")
        title = driver.title
        driver.quit()
        print(f"✅ Firefox WebDriver测试成功 - 页面标题: {title}")
        return True
    except Exception as e:
        print(f"❌ Firefox WebDriver测试失败: {e}")
    
    # 测试Edge
    print("\n📋 测试Edge WebDriver...")
    try:
        options = EdgeOptions()
        options.add_argument("--headless")  # 无头模式测试
        
        driver = webdriver.Edge(options=options)
        driver.get("https://www.baidu.com")
        title = driver.title
        driver.quit()
        print(f"✅ Edge WebDriver测试成功 - 页面标题: {title}")
        return True
    except Exception as e:
        print(f"❌ Edge WebDriver测试失败: {e}")
    
    return False

def check_path_environment():
    """检查PATH环境变量"""
    print("\n🔍 检查PATH环境变量...")
    
    path_dirs = os.environ.get('PATH', '').split(os.pathsep)
    webdriver_files = ['chromedriver.exe', 'chromedriver', 'geckodriver.exe', 'geckodriver', 'msedgedriver.exe', 'msedgedriver']
    
    found_drivers = []
    for path_dir in path_dirs:
        if os.path.exists(path_dir):
            for file in os.listdir(path_dir):
                if file in webdriver_files:
                    full_path = os.path.join(path_dir, file)
                    found_drivers.append(full_path)
                    print(f"✅ 找到WebDriver: {full_path}")
    
    if not found_drivers:
        print("❌ 在PATH中未找到任何WebDriver")
    
    return found_drivers

def main():
    """主函数"""
    print("=" * 70)
    print("🔍 WebDriver环境检查工具")
    print("=" * 70)
    
    # 检查浏览器版本
    print("\n📋 检查已安装的浏览器...")
    browsers = [
        ("Chrome", "chrome --version"),
        ("Firefox", "firefox --version"),
        ("Edge", "msedge --version")
    ]
    
    browser_available = False
    for name, cmd in browsers:
        if check_browser_version(name, cmd):
            browser_available = True
    
    if not browser_available:
        print("\n⚠️ 警告：未检测到任何支持的浏览器")
    
    # 检查WebDriver
    print("\n📋 检查WebDriver...")
    webdrivers = [
        ("ChromeDriver", "chromedriver --version"),
        ("GeckoDriver", "geckodriver --version"),
        ("EdgeDriver", "msedgedriver --version")
    ]
    
    webdriver_available = False
    for name, cmd in webdrivers:
        if check_webdriver(name, cmd):
            webdriver_available = True
    
    if not webdriver_available:
        print("\n⚠️ 警告：未检测到任何WebDriver")
    
    # 检查PATH环境变量
    found_drivers = check_path_environment()
    
    # 测试Selenium
    selenium_works = test_selenium_webdriver()
    
    # 总结
    print("\n" + "=" * 70)
    print("📊 检查结果总结")
    print("=" * 70)
    
    if browser_available:
        print("✅ 浏览器: 至少有一个支持的浏览器可用")
    else:
        print("❌ 浏览器: 未检测到支持的浏览器")
    
    if webdriver_available:
        print("✅ WebDriver: 至少有一个WebDriver可用")
    else:
        print("❌ WebDriver: 未检测到WebDriver")
    
    if found_drivers:
        print(f"✅ PATH配置: 找到 {len(found_drivers)} 个WebDriver")
    else:
        print("❌ PATH配置: 未在PATH中找到WebDriver")
    
    if selenium_works:
        print("✅ Selenium测试: WebDriver功能正常")
    else:
        print("❌ Selenium测试: WebDriver功能异常")
    
    # 建议
    print("\n💡 建议:")
    if not browser_available:
        print("   - 请安装Chrome、Firefox或Edge浏览器")
    
    if not webdriver_available:
        print("   - 请下载并安装对应的WebDriver")
        print("   - Chrome: https://chromedriver.chromium.org/")
        print("   - Firefox: https://github.com/mozilla/geckodriver/releases")
        print("   - Edge: https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/")
    
    if not found_drivers:
        print("   - 请将WebDriver添加到系统PATH环境变量中")
        print("   - 或将WebDriver文件复制到系统目录（如C:\\Windows\\System32\\）")
    
    if not selenium_works:
        print("   - 请检查WebDriver版本是否与浏览器版本匹配")
        print("   - 尝试重新下载最新版本的WebDriver")
    
    print("\n📖 详细配置指南请参考: WEBDRIVER_SETUP.md")
    print("=" * 70)
    
    # 返回状态
    if browser_available and webdriver_available and selenium_works:
        print("🎉 环境检查通过！自动化登录功能可以正常使用。")
        return True
    else:
        print("⚠️ 环境检查未完全通过，请根据建议进行配置。")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ 检查已取消")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 检查过程中发生错误: {e}")
        sys.exit(1)
