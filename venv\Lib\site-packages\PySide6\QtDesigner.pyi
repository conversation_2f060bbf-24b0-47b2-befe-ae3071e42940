# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations
"""
This file contains the exact signatures for all functions in module
PySide6.QtDesigner, except for defaults which are replaced by "...".

# mypy: disable-error-code="override, overload-overlap"
"""

# Module `PySide6.QtDesigner`

import PySide6.QtDesigner
import PySide6.QtCore
import PySide6.QtGui
import PySide6.QtWidgets

import enum
import typing
import collections.abc
from PySide6.QtCore import Signal
from shiboken6 import Shiboken


class QAbstractExtensionFactory(Shiboken.Object):

    def __init__(self, /) -> None: ...

    def extension(self, object: PySide6.QtCore.QObject, iid: str, /) -> PySide6.QtCore.QObject: ...


class QAbstractExtensionManager(Shiboken.Object):

    def __init__(self, /) -> None: ...

    def extension(self, object: PySide6.QtCore.QObject, iid: str, /) -> PySide6.QtCore.QObject: ...
    def registerExtensions(self, factory: PySide6.QtDesigner.QAbstractExtensionFactory, iid: str, /) -> None: ...
    def unregisterExtensions(self, factory: PySide6.QtDesigner.QAbstractExtensionFactory, iid: str, /) -> None: ...


class QAbstractFormBuilder(Shiboken.Object):

    def __init__(self, /) -> None: ...

    def addMenuAction(self, action: PySide6.QtGui.QAction, /) -> None: ...
    def applyPropertyInternally(self, o: PySide6.QtCore.QObject, propertyName: str, value: typing.Any, /) -> bool: ...
    def checkProperty(self, obj: PySide6.QtCore.QObject, prop: str, /) -> bool: ...
    def createAction(self, parent: PySide6.QtCore.QObject, name: str, /) -> PySide6.QtGui.QAction: ...
    def createActionGroup(self, parent: PySide6.QtCore.QObject, name: str, /) -> PySide6.QtGui.QActionGroup: ...
    def createLayout(self, layoutName: str, parent: PySide6.QtCore.QObject, name: str, /) -> PySide6.QtWidgets.QLayout: ...
    def createWidget(self, widgetName: str, parentWidget: PySide6.QtWidgets.QWidget, name: str, /) -> PySide6.QtWidgets.QWidget: ...
    def errorString(self, /) -> str: ...
    def load(self, dev: PySide6.QtCore.QIODevice, /, parentWidget: PySide6.QtWidgets.QWidget | None = ...) -> PySide6.QtWidgets.QWidget: ...
    def reset(self, /) -> None: ...
    def save(self, dev: PySide6.QtCore.QIODevice, widget: PySide6.QtWidgets.QWidget, /) -> None: ...
    def setWorkingDirectory(self, directory: PySide6.QtCore.QDir, /) -> None: ...
    @staticmethod
    def toolBarAreaMetaEnum() -> PySide6.QtCore.QMetaEnum: ...
    def workingDirectory(self, /) -> PySide6.QtCore.QDir: ...


class QDesignerActionEditorInterface(PySide6.QtWidgets.QWidget):

    def __init__(self, parent: PySide6.QtWidgets.QWidget, /, flags: PySide6.QtCore.Qt.WindowType = ...) -> None: ...

    def core(self, /) -> PySide6.QtDesigner.QDesignerFormEditorInterface: ...
    def manageAction(self, action: PySide6.QtGui.QAction, /) -> None: ...
    def setFormWindow(self, formWindow: PySide6.QtDesigner.QDesignerFormWindowInterface, /) -> None: ...
    def unmanageAction(self, action: PySide6.QtGui.QAction, /) -> None: ...


class QDesignerContainerExtension(Shiboken.Object):

    def __init__(self, /) -> None: ...

    def addWidget(self, widget: PySide6.QtWidgets.QWidget, /) -> None: ...
    def canAddWidget(self, /) -> bool: ...
    def canRemove(self, index: int, /) -> bool: ...
    def count(self, /) -> int: ...
    def currentIndex(self, /) -> int: ...
    def insertWidget(self, index: int, widget: PySide6.QtWidgets.QWidget, /) -> None: ...
    def remove(self, index: int, /) -> None: ...
    def setCurrentIndex(self, index: int, /) -> None: ...
    def widget(self, index: int, /) -> PySide6.QtWidgets.QWidget: ...


class QDesignerCustomWidgetCollectionInterface(Shiboken.Object):

    def __init__(self, /) -> None: ...

    def customWidgets(self, /) -> typing.List[PySide6.QtDesigner.QDesignerCustomWidgetInterface]: ...


class QDesignerCustomWidgetInterface(Shiboken.Object):

    def __init__(self, /) -> None: ...

    def codeTemplate(self, /) -> str: ...
    def createWidget(self, parent: PySide6.QtWidgets.QWidget, /) -> PySide6.QtWidgets.QWidget: ...
    def domXml(self, /) -> str: ...
    def group(self, /) -> str: ...
    def icon(self, /) -> PySide6.QtGui.QIcon: ...
    def includeFile(self, /) -> str: ...
    def initialize(self, core: PySide6.QtDesigner.QDesignerFormEditorInterface, /) -> None: ...
    def isContainer(self, /) -> bool: ...
    def isInitialized(self, /) -> bool: ...
    def name(self, /) -> str: ...
    def toolTip(self, /) -> str: ...
    def whatsThis(self, /) -> str: ...


class QDesignerDnDItemInterface(Shiboken.Object):

    class DropType(enum.Enum):

        MoveDrop                  = ...  # 0x0
        CopyDrop                  = ...  # 0x1


    def __init__(self, /) -> None: ...

    def decoration(self, /) -> PySide6.QtWidgets.QWidget: ...
    def hotSpot(self, /) -> PySide6.QtCore.QPoint: ...
    def source(self, /) -> PySide6.QtWidgets.QWidget: ...
    def type(self, /) -> PySide6.QtDesigner.QDesignerDnDItemInterface.DropType: ...
    def widget(self, /) -> PySide6.QtWidgets.QWidget: ...


class QDesignerDynamicPropertySheetExtension(Shiboken.Object):

    def __init__(self, /) -> None: ...

    def addDynamicProperty(self, propertyName: str, value: typing.Any, /) -> int: ...
    def canAddDynamicProperty(self, propertyName: str, /) -> bool: ...
    def dynamicPropertiesAllowed(self, /) -> bool: ...
    def isDynamicProperty(self, index: int, /) -> bool: ...
    def removeDynamicProperty(self, index: int, /) -> bool: ...


class QDesignerFormEditorInterface(PySide6.QtCore.QObject):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def actionEditor(self, /) -> PySide6.QtDesigner.QDesignerActionEditorInterface: ...
    @staticmethod
    def createIcon(name: str, /) -> PySide6.QtGui.QIcon: ...
    def extensionManager(self, /) -> PySide6.QtDesigner.QExtensionManager: ...
    def formWindowManager(self, /) -> PySide6.QtDesigner.QDesignerFormWindowManagerInterface: ...
    def objectInspector(self, /) -> PySide6.QtDesigner.QDesignerObjectInspectorInterface: ...
    def pluginInstances(self, /) -> typing.List[PySide6.QtCore.QObject]: ...
    def propertyEditor(self, /) -> PySide6.QtDesigner.QDesignerPropertyEditorInterface: ...
    def resourceLocation(self, /) -> str: ...
    def setActionEditor(self, actionEditor: PySide6.QtDesigner.QDesignerActionEditorInterface, /) -> None: ...
    def setExtensionManager(self, extensionManager: PySide6.QtDesigner.QExtensionManager, /) -> None: ...
    def setFormManager(self, formWindowManager: PySide6.QtDesigner.QDesignerFormWindowManagerInterface, /) -> None: ...
    def setObjectInspector(self, objectInspector: PySide6.QtDesigner.QDesignerObjectInspectorInterface, /) -> None: ...
    def setPropertyEditor(self, propertyEditor: PySide6.QtDesigner.QDesignerPropertyEditorInterface, /) -> None: ...
    def setTopLevel(self, topLevel: PySide6.QtWidgets.QWidget, /) -> None: ...
    def setWidgetBox(self, widgetBox: PySide6.QtDesigner.QDesignerWidgetBoxInterface, /) -> None: ...
    def topLevel(self, /) -> PySide6.QtWidgets.QWidget: ...
    def widgetBox(self, /) -> PySide6.QtDesigner.QDesignerWidgetBoxInterface: ...


class QDesignerFormWindowCursorInterface(Shiboken.Object):

    class MoveMode(enum.Enum):

        MoveAnchor                = ...  # 0x0
        KeepAnchor                = ...  # 0x1

    class MoveOperation(enum.Enum):

        NoMove                    = ...  # 0x0
        Start                     = ...  # 0x1
        End                       = ...  # 0x2
        Next                      = ...  # 0x3
        Prev                      = ...  # 0x4
        Left                      = ...  # 0x5
        Right                     = ...  # 0x6
        Up                        = ...  # 0x7
        Down                      = ...  # 0x8


    def __init__(self, /) -> None: ...

    def current(self, /) -> PySide6.QtWidgets.QWidget: ...
    def formWindow(self, /) -> PySide6.QtDesigner.QDesignerFormWindowInterface: ...
    def hasSelection(self, /) -> bool: ...
    def isWidgetSelected(self, widget: PySide6.QtWidgets.QWidget, /) -> bool: ...
    def movePosition(self, op: PySide6.QtDesigner.QDesignerFormWindowCursorInterface.MoveOperation, /, mode: PySide6.QtDesigner.QDesignerFormWindowCursorInterface.MoveMode = ...) -> bool: ...
    def position(self, /) -> int: ...
    def resetWidgetProperty(self, widget: PySide6.QtWidgets.QWidget, name: str, /) -> None: ...
    def selectedWidget(self, index: int, /) -> PySide6.QtWidgets.QWidget: ...
    def selectedWidgetCount(self, /) -> int: ...
    def setPosition(self, pos: int, /, mode: PySide6.QtDesigner.QDesignerFormWindowCursorInterface.MoveMode = ...) -> None: ...
    def setProperty(self, name: str, value: typing.Any, /) -> None: ...
    def setWidgetProperty(self, widget: PySide6.QtWidgets.QWidget, name: str, value: typing.Any, /) -> None: ...
    def widget(self, index: int, /) -> PySide6.QtWidgets.QWidget: ...
    def widgetCount(self, /) -> int: ...


class QDesignerFormWindowInterface(PySide6.QtWidgets.QWidget):

    aboutToUnmanageWidget    : typing.ClassVar[Signal] = ... # aboutToUnmanageWidget(QWidget*)
    activated                : typing.ClassVar[Signal] = ... # activated(QWidget*)
    changed                  : typing.ClassVar[Signal] = ... # changed()
    featureChanged           : typing.ClassVar[Signal] = ... # featureChanged(Feature)
    fileNameChanged          : typing.ClassVar[Signal] = ... # fileNameChanged(QString)
    geometryChanged          : typing.ClassVar[Signal] = ... # geometryChanged()
    mainContainerChanged     : typing.ClassVar[Signal] = ... # mainContainerChanged(QWidget*)
    objectRemoved            : typing.ClassVar[Signal] = ... # objectRemoved(QObject*)
    resourceFilesChanged     : typing.ClassVar[Signal] = ... # resourceFilesChanged()
    selectionChanged         : typing.ClassVar[Signal] = ... # selectionChanged()
    toolChanged              : typing.ClassVar[Signal] = ... # toolChanged(int)
    widgetManaged            : typing.ClassVar[Signal] = ... # widgetManaged(QWidget*)
    widgetRemoved            : typing.ClassVar[Signal] = ... # widgetRemoved(QWidget*)
    widgetUnmanaged          : typing.ClassVar[Signal] = ... # widgetUnmanaged(QWidget*)

    class FeatureFlag(enum.Flag):

        EditFeature               = ...  # 0x1
        GridFeature               = ...  # 0x2
        DefaultFeature            = ...  # 0x3
        TabOrderFeature           = ...  # 0x4

    class ResourceFileSaveMode(enum.Enum):

        SaveAllResourceFiles      = ...  # 0x0
        SaveOnlyUsedResourceFiles = ...  # 0x1
        DontSaveResourceFiles     = ...  # 0x2


    def __init__(self, /, parent: PySide6.QtWidgets.QWidget | None = ..., flags: PySide6.QtCore.Qt.WindowType = ...) -> None: ...

    def absoluteDir(self, /) -> PySide6.QtCore.QDir: ...
    def activateResourceFilePaths(self, paths: collections.abc.Sequence[str], /) -> typing.Tuple[int, str]: ...
    def activeResourceFilePaths(self, /) -> typing.List[str]: ...
    def addResourceFile(self, path: str, /) -> None: ...
    def author(self, /) -> str: ...
    def beginCommand(self, description: str, /) -> None: ...
    def checkContents(self, /) -> typing.List[str]: ...
    def clearSelection(self, /, changePropertyDisplay: bool = ...) -> None: ...
    def commandHistory(self, /) -> PySide6.QtGui.QUndoStack: ...
    def comment(self, /) -> str: ...
    def contents(self, /) -> str: ...
    def core(self, /) -> PySide6.QtDesigner.QDesignerFormEditorInterface: ...
    def currentTool(self, /) -> int: ...
    def cursor(self, /) -> PySide6.QtDesigner.QDesignerFormWindowCursorInterface: ...
    def editWidgets(self, /) -> None: ...
    def emitSelectionChanged(self, /) -> None: ...
    def endCommand(self, /) -> None: ...
    def ensureUniqueObjectName(self, object: PySide6.QtCore.QObject, /) -> None: ...
    def exportMacro(self, /) -> str: ...
    def features(self, /) -> PySide6.QtDesigner.QDesignerFormWindowInterface.FeatureFlag: ...
    def fileName(self, /) -> str: ...
    @typing.overload
    @staticmethod
    def findFormWindow(w: PySide6.QtWidgets.QWidget, /) -> PySide6.QtDesigner.QDesignerFormWindowInterface: ...
    @typing.overload
    @staticmethod
    def findFormWindow(obj: PySide6.QtCore.QObject, /) -> PySide6.QtDesigner.QDesignerFormWindowInterface: ...
    def formContainer(self, /) -> PySide6.QtWidgets.QWidget: ...
    def grid(self, /) -> PySide6.QtCore.QPoint: ...
    def hasFeature(self, f: PySide6.QtDesigner.QDesignerFormWindowInterface.FeatureFlag, /) -> bool: ...
    def includeHints(self, /) -> typing.List[str]: ...
    def isDirty(self, /) -> bool: ...
    def isManaged(self, widget: PySide6.QtWidgets.QWidget, /) -> bool: ...
    def layoutDefault(self, /) -> typing.Tuple[int, int]: ...
    def layoutFunction(self, /) -> typing.Tuple[str, str]: ...
    def mainContainer(self, /) -> PySide6.QtWidgets.QWidget: ...
    def manageWidget(self, widget: PySide6.QtWidgets.QWidget, /) -> None: ...
    def pixmapFunction(self, /) -> str: ...
    def registerTool(self, tool: PySide6.QtDesigner.QDesignerFormWindowToolInterface, /) -> None: ...
    def removeResourceFile(self, path: str, /) -> None: ...
    def resourceFileSaveMode(self, /) -> PySide6.QtDesigner.QDesignerFormWindowInterface.ResourceFileSaveMode: ...
    def resourceFiles(self, /) -> typing.List[str]: ...
    def selectWidget(self, w: PySide6.QtWidgets.QWidget, /, select: bool = ...) -> None: ...
    def setAuthor(self, author: str, /) -> None: ...
    def setComment(self, comment: str, /) -> None: ...
    @typing.overload
    def setContents(self, dev: PySide6.QtCore.QIODevice, /) -> typing.Tuple[bool, str]: ...
    @typing.overload
    def setContents(self, contents: str, /) -> bool: ...
    def setCurrentTool(self, index: int, /) -> None: ...
    def setDirty(self, dirty: bool, /) -> None: ...
    def setExportMacro(self, exportMacro: str, /) -> None: ...
    def setFeatures(self, f: PySide6.QtDesigner.QDesignerFormWindowInterface.FeatureFlag, /) -> None: ...
    def setFileName(self, fileName: str, /) -> None: ...
    def setGrid(self, grid: PySide6.QtCore.QPoint, /) -> None: ...
    def setIncludeHints(self, includeHints: collections.abc.Sequence[str], /) -> None: ...
    def setLayoutDefault(self, margin: int, spacing: int, /) -> None: ...
    def setLayoutFunction(self, margin: str, spacing: str, /) -> None: ...
    def setMainContainer(self, mainContainer: PySide6.QtWidgets.QWidget, /) -> None: ...
    def setPixmapFunction(self, pixmapFunction: str, /) -> None: ...
    def setResourceFileSaveMode(self, behaviour: PySide6.QtDesigner.QDesignerFormWindowInterface.ResourceFileSaveMode, /) -> None: ...
    def simplifySelection(self, widgets: collections.abc.Sequence[PySide6.QtWidgets.QWidget], /) -> None: ...
    def tool(self, index: int, /) -> PySide6.QtDesigner.QDesignerFormWindowToolInterface: ...
    def toolCount(self, /) -> int: ...
    def unmanageWidget(self, widget: PySide6.QtWidgets.QWidget, /) -> None: ...


class QDesignerFormWindowManagerInterface(PySide6.QtCore.QObject):

    activeFormWindowChanged  : typing.ClassVar[Signal] = ... # activeFormWindowChanged(QDesignerFormWindowInterface*)
    formWindowAdded          : typing.ClassVar[Signal] = ... # formWindowAdded(QDesignerFormWindowInterface*)
    formWindowRemoved        : typing.ClassVar[Signal] = ... # formWindowRemoved(QDesignerFormWindowInterface*)
    formWindowSettingsChanged: typing.ClassVar[Signal] = ... # formWindowSettingsChanged(QDesignerFormWindowInterface*)

    class Action(enum.Enum):

        CutAction                 = ...  # 0x64
        CopyAction                = ...  # 0x65
        PasteAction               = ...  # 0x66
        DeleteAction              = ...  # 0x67
        SelectAllAction           = ...  # 0x68
        LowerAction               = ...  # 0xc8
        RaiseAction               = ...  # 0xc9
        UndoAction                = ...  # 0x12c
        RedoAction                = ...  # 0x12d
        HorizontalLayoutAction    = ...  # 0x190
        VerticalLayoutAction      = ...  # 0x191
        SplitHorizontalAction     = ...  # 0x192
        SplitVerticalAction       = ...  # 0x193
        GridLayoutAction          = ...  # 0x194
        FormLayoutAction          = ...  # 0x195
        BreakLayoutAction         = ...  # 0x196
        AdjustSizeAction          = ...  # 0x197
        SimplifyLayoutAction      = ...  # 0x198
        DefaultPreviewAction      = ...  # 0x1f4
        FormWindowSettingsDialogAction = ...  # 0x258

    class ActionGroup(enum.Enum):

        StyledPreviewActionGroup  = ...  # 0x64


    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def action(self, action: PySide6.QtDesigner.QDesignerFormWindowManagerInterface.Action, /) -> PySide6.QtGui.QAction: ...
    def actionAdjustSize(self, /) -> PySide6.QtGui.QAction: ...
    def actionBreakLayout(self, /) -> PySide6.QtGui.QAction: ...
    def actionCopy(self, /) -> PySide6.QtGui.QAction: ...
    def actionCut(self, /) -> PySide6.QtGui.QAction: ...
    def actionDelete(self, /) -> PySide6.QtGui.QAction: ...
    def actionFormLayout(self, /) -> PySide6.QtGui.QAction: ...
    def actionGridLayout(self, /) -> PySide6.QtGui.QAction: ...
    def actionGroup(self, actionGroup: PySide6.QtDesigner.QDesignerFormWindowManagerInterface.ActionGroup, /) -> PySide6.QtGui.QActionGroup: ...
    def actionHorizontalLayout(self, /) -> PySide6.QtGui.QAction: ...
    def actionLower(self, /) -> PySide6.QtGui.QAction: ...
    def actionPaste(self, /) -> PySide6.QtGui.QAction: ...
    def actionRaise(self, /) -> PySide6.QtGui.QAction: ...
    def actionRedo(self, /) -> PySide6.QtGui.QAction: ...
    def actionSelectAll(self, /) -> PySide6.QtGui.QAction: ...
    def actionSimplifyLayout(self, /) -> PySide6.QtGui.QAction: ...
    def actionSplitHorizontal(self, /) -> PySide6.QtGui.QAction: ...
    def actionSplitVertical(self, /) -> PySide6.QtGui.QAction: ...
    def actionUndo(self, /) -> PySide6.QtGui.QAction: ...
    def actionVerticalLayout(self, /) -> PySide6.QtGui.QAction: ...
    def activeFormWindow(self, /) -> PySide6.QtDesigner.QDesignerFormWindowInterface: ...
    def addFormWindow(self, formWindow: PySide6.QtDesigner.QDesignerFormWindowInterface, /) -> None: ...
    def closeAllPreviews(self, /) -> None: ...
    def core(self, /) -> PySide6.QtDesigner.QDesignerFormEditorInterface: ...
    def createFormWindow(self, /, parentWidget: PySide6.QtWidgets.QWidget | None = ..., flags: PySide6.QtCore.Qt.WindowType = ...) -> PySide6.QtDesigner.QDesignerFormWindowInterface: ...
    def createPreviewPixmap(self, /) -> PySide6.QtGui.QPixmap: ...
    def dragItems(self, item_list: collections.abc.Sequence[PySide6.QtDesigner.QDesignerDnDItemInterface], /) -> None: ...
    def formWindow(self, index: int, /) -> PySide6.QtDesigner.QDesignerFormWindowInterface: ...
    def formWindowCount(self, /) -> int: ...
    def removeFormWindow(self, formWindow: PySide6.QtDesigner.QDesignerFormWindowInterface, /) -> None: ...
    def setActiveFormWindow(self, formWindow: PySide6.QtDesigner.QDesignerFormWindowInterface, /) -> None: ...
    def showPluginDialog(self, /) -> None: ...
    def showPreview(self, /) -> None: ...


class QDesignerFormWindowToolInterface(PySide6.QtCore.QObject):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def action(self, /) -> PySide6.QtGui.QAction: ...
    def activated(self, /) -> None: ...
    def core(self, /) -> PySide6.QtDesigner.QDesignerFormEditorInterface: ...
    def deactivated(self, /) -> None: ...
    def editor(self, /) -> PySide6.QtWidgets.QWidget: ...
    def formWindow(self, /) -> PySide6.QtDesigner.QDesignerFormWindowInterface: ...
    def handleEvent(self, widget: PySide6.QtWidgets.QWidget, managedWidget: PySide6.QtWidgets.QWidget, event: PySide6.QtCore.QEvent, /) -> bool: ...


class QDesignerMemberSheetExtension(Shiboken.Object):

    def __init__(self, /) -> None: ...

    def count(self, /) -> int: ...
    def declaredInClass(self, index: int, /) -> str: ...
    def indexOf(self, name: str, /) -> int: ...
    def inheritedFromWidget(self, index: int, /) -> bool: ...
    def isSignal(self, index: int, /) -> bool: ...
    def isSlot(self, index: int, /) -> bool: ...
    def isVisible(self, index: int, /) -> bool: ...
    def memberGroup(self, index: int, /) -> str: ...
    def memberName(self, index: int, /) -> str: ...
    def parameterNames(self, index: int, /) -> typing.List[PySide6.QtCore.QByteArray]: ...
    def parameterTypes(self, index: int, /) -> typing.List[PySide6.QtCore.QByteArray]: ...
    def setMemberGroup(self, index: int, group: str, /) -> None: ...
    def setVisible(self, index: int, b: bool, /) -> None: ...
    def signature(self, index: int, /) -> str: ...


class QDesignerObjectInspectorInterface(PySide6.QtWidgets.QWidget):

    def __init__(self, parent: PySide6.QtWidgets.QWidget, /, flags: PySide6.QtCore.Qt.WindowType = ...) -> None: ...

    def core(self, /) -> PySide6.QtDesigner.QDesignerFormEditorInterface: ...
    def setFormWindow(self, formWindow: PySide6.QtDesigner.QDesignerFormWindowInterface, /) -> None: ...


class QDesignerPropertyEditorInterface(PySide6.QtWidgets.QWidget):

    propertyChanged          : typing.ClassVar[Signal] = ... # propertyChanged(QString,QVariant)

    def __init__(self, parent: PySide6.QtWidgets.QWidget, /, flags: PySide6.QtCore.Qt.WindowType = ...) -> None: ...

    def core(self, /) -> PySide6.QtDesigner.QDesignerFormEditorInterface: ...
    def currentPropertyName(self, /) -> str: ...
    def isReadOnly(self, /) -> bool: ...
    def object(self, /) -> PySide6.QtCore.QObject: ...
    def setObject(self, object: PySide6.QtCore.QObject, /) -> None: ...
    def setPropertyValue(self, name: str, value: typing.Any, /, changed: bool = ...) -> None: ...
    def setReadOnly(self, readOnly: bool, /) -> None: ...


class QDesignerPropertySheetExtension(Shiboken.Object):

    def __init__(self, /) -> None: ...

    def count(self, /) -> int: ...
    def hasReset(self, index: int, /) -> bool: ...
    def indexOf(self, name: str, /) -> int: ...
    def isAttribute(self, index: int, /) -> bool: ...
    def isChanged(self, index: int, /) -> bool: ...
    def isEnabled(self, index: int, /) -> bool: ...
    def isVisible(self, index: int, /) -> bool: ...
    def property(self, index: int, /) -> typing.Any: ...
    def propertyGroup(self, index: int, /) -> str: ...
    def propertyName(self, index: int, /) -> str: ...
    def reset(self, index: int, /) -> bool: ...
    def setAttribute(self, index: int, b: bool, /) -> None: ...
    def setChanged(self, index: int, changed: bool, /) -> None: ...
    def setProperty(self, index: int, value: typing.Any, /) -> None: ...
    def setPropertyGroup(self, index: int, group: str, /) -> None: ...
    def setVisible(self, index: int, b: bool, /) -> None: ...


class QDesignerTaskMenuExtension(Shiboken.Object):

    def __init__(self, /) -> None: ...

    def preferredEditAction(self, /) -> PySide6.QtGui.QAction: ...
    def taskActions(self, /) -> typing.List[PySide6.QtGui.QAction]: ...


class QDesignerWidgetBoxInterface(PySide6.QtWidgets.QWidget):

    class Category(Shiboken.Object):

        class Type(enum.Enum):

            Default                   = ...  # 0x0
            Scratchpad                = ...  # 0x1


        @typing.overload
        def __init__(self, Category: PySide6.QtDesigner.QDesignerWidgetBoxInterface.Category, /) -> None: ...
        @typing.overload
        def __init__(self, /, aname: str = ..., atype: PySide6.QtDesigner.QDesignerWidgetBoxInterface.Category.Type = ...) -> None: ...

        def __copy__(self, /) -> typing.Self: ...
        def addWidget(self, awidget: PySide6.QtDesigner.QDesignerWidgetBoxInterface.Widget, /) -> None: ...
        def isNull(self, /) -> bool: ...
        def name(self, /) -> str: ...
        def removeWidget(self, idx: int, /) -> None: ...
        def setName(self, aname: str, /) -> None: ...
        def setType(self, atype: PySide6.QtDesigner.QDesignerWidgetBoxInterface.Category.Type, /) -> None: ...
        def type(self, /) -> PySide6.QtDesigner.QDesignerWidgetBoxInterface.Category.Type: ...
        def widget(self, idx: int, /) -> PySide6.QtDesigner.QDesignerWidgetBoxInterface.Widget: ...
        def widgetCount(self, /) -> int: ...

    class Widget(Shiboken.Object):

        class Type(enum.Enum):

            Default                   = ...  # 0x0
            Custom                    = ...  # 0x1


        @typing.overload
        def __init__(self, w: PySide6.QtDesigner.QDesignerWidgetBoxInterface.Widget, /) -> None: ...
        @typing.overload
        def __init__(self, /, aname: str = ..., xml: str = ..., icon_name: str = ..., atype: PySide6.QtDesigner.QDesignerWidgetBoxInterface.Widget.Type = ...) -> None: ...

        def __copy__(self, /) -> typing.Self: ...
        def domXml(self, /) -> str: ...
        def iconName(self, /) -> str: ...
        def isNull(self, /) -> bool: ...
        def name(self, /) -> str: ...
        def setDomXml(self, xml: str, /) -> None: ...
        def setIconName(self, icon_name: str, /) -> None: ...
        def setName(self, aname: str, /) -> None: ...
        def setType(self, atype: PySide6.QtDesigner.QDesignerWidgetBoxInterface.Widget.Type, /) -> None: ...
        def type(self, /) -> PySide6.QtDesigner.QDesignerWidgetBoxInterface.Widget.Type: ...


    def __init__(self, /, parent: PySide6.QtWidgets.QWidget | None = ..., flags: PySide6.QtCore.Qt.WindowType = ...) -> None: ...

    def addCategory(self, cat: PySide6.QtDesigner.QDesignerWidgetBoxInterface.Category, /) -> None: ...
    def addWidget(self, cat_idx: int, wgt: PySide6.QtDesigner.QDesignerWidgetBoxInterface.Widget, /) -> None: ...
    def category(self, cat_idx: int, /) -> PySide6.QtDesigner.QDesignerWidgetBoxInterface.Category: ...
    def categoryCount(self, /) -> int: ...
    def dropWidgets(self, item_list: collections.abc.Sequence[PySide6.QtDesigner.QDesignerDnDItemInterface], global_mouse_pos: PySide6.QtCore.QPoint, /) -> None: ...
    def fileName(self, /) -> str: ...
    def findOrInsertCategory(self, categoryName: str, /) -> int: ...
    def load(self, /) -> bool: ...
    def removeCategory(self, cat_idx: int, /) -> None: ...
    def removeWidget(self, cat_idx: int, wgt_idx: int, /) -> None: ...
    def save(self, /) -> bool: ...
    def setFileName(self, file_name: str, /) -> None: ...
    def widget(self, cat_idx: int, wgt_idx: int, /) -> PySide6.QtDesigner.QDesignerWidgetBoxInterface.Widget: ...
    def widgetCount(self, cat_idx: int, /) -> int: ...


class QExtensionFactory(PySide6.QtCore.QObject, PySide6.QtDesigner.QAbstractExtensionFactory):

    def __init__(self, /, parent: PySide6.QtDesigner.QExtensionManager | None = ...) -> None: ...

    def createExtension(self, object: PySide6.QtCore.QObject, iid: str, parent: PySide6.QtCore.QObject, /) -> PySide6.QtCore.QObject: ...
    def extension(self, object: PySide6.QtCore.QObject, iid: str, /) -> PySide6.QtCore.QObject: ...
    def extensionManager(self, /) -> PySide6.QtDesigner.QExtensionManager: ...


class QExtensionManager(PySide6.QtCore.QObject, PySide6.QtDesigner.QAbstractExtensionManager):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def extension(self, object: PySide6.QtCore.QObject, iid: str, /) -> PySide6.QtCore.QObject: ...
    def registerExtensions(self, factory: PySide6.QtDesigner.QAbstractExtensionFactory, /, iid: str = ...) -> None: ...
    def unregisterExtensions(self, factory: PySide6.QtDesigner.QAbstractExtensionFactory, /, iid: str = ...) -> None: ...


class QFormBuilder(PySide6.QtDesigner.QAbstractFormBuilder):

    def __init__(self, /) -> None: ...

    def addPluginPath(self, pluginPath: str, /) -> None: ...
    def clearPluginPaths(self, /) -> None: ...
    def createLayout(self, layoutName: str, parent: PySide6.QtCore.QObject, name: str, /) -> PySide6.QtWidgets.QLayout: ...
    def createWidget(self, widgetName: str, parentWidget: PySide6.QtWidgets.QWidget, name: str, /) -> PySide6.QtWidgets.QWidget: ...
    def customWidgets(self, /) -> typing.List[PySide6.QtDesigner.QDesignerCustomWidgetInterface]: ...
    def pluginPaths(self, /) -> typing.List[str]: ...
    def setPluginPath(self, pluginPaths: collections.abc.Sequence[str], /) -> None: ...
    def updateCustomWidgets(self, /) -> None: ...
    @staticmethod
    def widgetByName(topLevel: PySide6.QtWidgets.QWidget, name: str, /) -> PySide6.QtWidgets.QWidget: ...


class QIntList: ...


class QPyDesignerContainerExtension(PySide6.QtCore.QObject, PySide6.QtDesigner.QDesignerContainerExtension):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...


class QPyDesignerCustomWidgetCollection(PySide6.QtDesigner.QDesignerCustomWidgetCollectionInterface):
    @staticmethod
    def addCustomWidget(c: PySide6.QtDesigner.QDesignerCustomWidgetInterface, /) -> None: ...
    def customWidgets(self, /) -> typing.List[PySide6.QtDesigner.QDesignerCustomWidgetInterface]: ...
    @staticmethod
    def instance() -> PySide6.QtDesigner.QPyDesignerCustomWidgetCollection: ...
    @staticmethod
    def registerCustomWidget(customWidgetType: object, /, xml: str = ..., tool_tip: str = ..., group: str = ..., module: str = ..., container: bool = ..., icon: str = ...) -> None: ...


class QPyDesignerMemberSheetExtension(PySide6.QtCore.QObject, PySide6.QtDesigner.QDesignerMemberSheetExtension):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...


class QPyDesignerPropertySheetExtension(PySide6.QtCore.QObject, PySide6.QtDesigner.QDesignerPropertySheetExtension):  # type: ignore[misc]

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...


class QPyDesignerTaskMenuExtension(PySide6.QtCore.QObject, PySide6.QtDesigner.QDesignerTaskMenuExtension):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...


# eof
