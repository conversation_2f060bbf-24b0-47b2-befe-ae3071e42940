"""
测试账号对话框功能
"""

import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from PySide6.QtWidgets import QApplication
    from PySide6.QtCore import Qt
    QT_LIBRARY = "PySide6"
except ImportError:
    try:
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        QT_LIBRARY = "PyQt6"
    except ImportError as e:
        print("❌ 无法导入Qt库")
        sys.exit(1)

from ui.components.account_widget import AccountDialog

def test_dialog():
    """测试对话框创建和显示"""
    app = QApplication(sys.argv)
    
    try:
        print("正在创建对话框...")
        dialog = AccountDialog()
        print("对话框创建成功")
        
        print("正在显示对话框...")
        result = dialog.exec()
        print(f"对话框关闭，结果: {result}")
        
        if result == 1:  # Accepted
            account_data = dialog.get_account_data()
            print(f"获取的账号数据: {account_data}")
        
    except Exception as e:
        print(f"❌ 对话框测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = test_dialog()
    if success:
        print("✅ 对话框测试成功")
    else:
        print("❌ 对话框测试失败")
