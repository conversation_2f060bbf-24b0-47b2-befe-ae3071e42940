"""
快手无人带货工具 - 账号管理组件
Kuaishou Automation Tool - Account Management Widget

本模块实现账号管理界面组件，包括：
- 账号列表显示（表格形式）
- 添加/编辑/删除账号功能
- 账号状态监控
- 批量操作支持
"""

# 尝试导入Qt库，优先使用PySide6，回退到PyQt6
try:
    from PySide6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
        QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox,
        QDialog, QLineEdit, QComboBox, QTextEdit, QFormLayout,
        QDialogButtonBox, QGroupBox, QCheckBox, QSpacerItem, QSizePolicy
    )
    from PySide6.QtCore import Qt, QTimer, Signal as pyqtSignal, QTimer
    from PySide6.QtGui import QFont, QIcon
    QT_LIBRARY = "PySide6"
except ImportError:
    try:
        from PyQt6.QtWidgets import (
            QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
            QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox,
            QDialog, QLineEdit, QComboBox, QTextEdit, QFormLayout,
            QDialogButtonBox, QGroupBox, QCheckBox, QSpacerItem, QSizePolicy
        )
        from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QTimer
        from PyQt6.QtGui import QFont, QIcon
        QT_LIBRARY = "PyQt6"
    except ImportError as e:
        print("❌ 无法导入Qt库，请使用命令行版本: python main_cli.py")
        import sys
        sys.exit(1)
from datetime import datetime
import threading
import os
import json


class AccountWidget(QWidget):
    """
    账号管理组件类
    
    实现账号管理的完整功能，包括账号列表显示、增删改查操作、
    状态监控等。采用表格形式展示账号信息。
    
    Signals:
        account_added: 账号添加信号
        account_updated: 账号更新信号
        account_deleted: 账号删除信号
        
    Attributes:
        account_table (QTableWidget): 账号列表表格
        accounts_data (list): 账号数据列表
    """
    
    # 定义信号
    account_added = pyqtSignal(dict)
    account_updated = pyqtSignal(str, dict)
    account_deleted = pyqtSignal(str)
    
    def __init__(self, parent=None):
        """
        初始化账号管理组件
        
        Args:
            parent: 父组件，默认为None
        """
        super().__init__(parent)
        self.accounts_data = []
        self.init_ui()
        self.load_accounts_from_database()
        
    def init_ui(self):
        """
        初始化用户界面
        
        创建账号管理界面的完整布局：
        - 页面标题和描述
        - 操作按钮区域
        - 账号列表表格
        - 状态统计区域
        """
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(20)
        
        # 创建各个区域
        self.create_header_section(main_layout)
        self.create_toolbar_section(main_layout)
        self.create_table_section(main_layout)
        self.create_status_section(main_layout)

        # 设置操作按钮样式
        self.setup_button_styles()

    def setup_button_styles(self):
        """设置操作按钮样式"""
        button_styles = """
            /* 登录按钮 - 现代绿色风格 */
            QPushButton#loginButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #52c41a, stop:1 #389e0d);
                color: white;
                border: 1px solid #389e0d;
                border-radius: 5px;
                padding: 4px 8px;
                font-size: 11px;
                font-weight: 600;
                min-width: 50px;
            }
            QPushButton#loginButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #73d13d, stop:1 #52c41a);
                border-color: #52c41a;
            }
            QPushButton#loginButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #389e0d, stop:1 #237804);
                border-color: #237804;
            }

            /* 编辑按钮 - 现代蓝色风格 */
            QPushButton#editButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1890ff, stop:1 #096dd9);
                color: white;
                border: 1px solid #096dd9;
                border-radius: 5px;
                padding: 4px 8px;
                font-size: 11px;
                font-weight: 600;
                min-width: 50px;
            }
            QPushButton#editButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #40a9ff, stop:1 #1890ff);
                border-color: #1890ff;
            }
            QPushButton#editButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #096dd9, stop:1 #0050b3);
                border-color: #0050b3;
            }

            /* 删除按钮 - 现代红色风格 */
            QPushButton#deleteButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ff4d4f, stop:1 #cf1322);
                color: white;
                border: 1px solid #cf1322;
                border-radius: 5px;
                padding: 4px 8px;
                font-size: 11px;
                font-weight: 600;
                min-width: 50px;
            }
            QPushButton#deleteButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ff7875, stop:1 #ff4d4f);
                border-color: #ff4d4f;
            }
            QPushButton#deleteButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #cf1322, stop:1 #a8071a);
                border-color: #a8071a;
            }

            /* 开始任务按钮 - 现代橙色风格 */
            QPushButton#startTaskButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ff6b35, stop:1 #e55a2b);
                color: white;
                border: 1px solid #e55a2b;
                border-radius: 5px;
                padding: 4px 8px;
                font-size: 11px;
                font-weight: 600;
                min-width: 50px;
            }
            QPushButton#startTaskButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ff8555, stop:1 #ff6b35);
                border-color: #ff6b35;
            }
            QPushButton#startTaskButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e55a2b, stop:1 #cc4125);
                border-color: #cc4125;
            }

            /* 选品Cookie按钮 - 蓝色风格 */
            QPushButton#selectionCookieButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1890ff, stop:1 #096dd9);
                color: white;
                border: 1px solid #096dd9;
                border-radius: 5px;
                padding: 4px 8px;
                font-size: 11px;
                font-weight: 600;
                min-width: 60px;
            }
            QPushButton#selectionCookieButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #40a9ff, stop:1 #1890ff);
                border-color: #1890ff;
            }
            QPushButton#selectionCookieButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #096dd9, stop:1 #0050b3);
                border-color: #0050b3;
            }

            /* Cookie转换按钮 - 紫色风格 */
            QPushButton#specialButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #722ed1, stop:1 #531dab);
                color: white;
                border: 1px solid #531dab;
                border-radius: 5px;
                padding: 4px 8px;
                font-size: 11px;
                font-weight: 600;
                min-width: 80px;
            }
            QPushButton#specialButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #9254de, stop:1 #722ed1);
                border-color: #722ed1;
            }
            QPushButton#specialButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #531dab, stop:1 #391085);
                border-color: #391085;
            }
            QPushButton#specialButton:disabled {
                background: #434343;
                color: #888888;
                border-color: #434343;
            }
        """
        self.setStyleSheet(button_styles)

    def create_header_section(self, parent_layout):
        """
        创建页面标题区域
        
        Args:
            parent_layout: 父布局对象
        """
        # 页面标题
        title = QLabel("账号管理")
        title.setObjectName("pageTitle")
        parent_layout.addWidget(title)
        
        # 功能描述
        description = QLabel("管理快手账号信息，包括账号添加、编辑、删除和状态监控")
        description.setStyleSheet("color: #cccccc; font-size: 14px; margin-bottom: 10px;")
        parent_layout.addWidget(description)
        
    def create_toolbar_section(self, parent_layout):
        """
        创建工具栏区域
        
        Args:
            parent_layout: 父布局对象
        """
        toolbar_layout = QHBoxLayout()
        
        # 操作按钮
        self.add_button = QPushButton("➕ 添加账号")
        self.add_button.clicked.connect(self.add_account)
        
        self.edit_button = QPushButton("✏️ 编辑账号")
        self.edit_button.clicked.connect(self.edit_account)
        self.edit_button.setEnabled(False)
        
        self.delete_button = QPushButton("🗑️ 删除账号")
        self.delete_button.setObjectName("dangerButton")
        self.delete_button.clicked.connect(self.delete_account)
        self.delete_button.setEnabled(False)
        
        self.refresh_button = QPushButton("🔄 刷新状态")
        self.refresh_button.setObjectName("secondaryButton")
        self.refresh_button.clicked.connect(self.refresh_accounts)

        # Cookie转换按钮
        self.convert_cookie_button = QPushButton("🔄 Cookie转换")
        self.convert_cookie_button.setObjectName("specialButton")
        self.convert_cookie_button.clicked.connect(self.convert_cookie_domain)
        self.convert_cookie_button.setEnabled(False)
        self.convert_cookie_button.setToolTip("将选中账号的Cookie转换到对应域名\n(创作者中心 ↔ 选品中心)")

        # 添加按钮到布局
        toolbar_layout.addWidget(self.add_button)
        toolbar_layout.addWidget(self.edit_button)
        toolbar_layout.addWidget(self.delete_button)
        toolbar_layout.addWidget(self.refresh_button)
        toolbar_layout.addWidget(self.convert_cookie_button)
        toolbar_layout.addStretch()
        
        parent_layout.addLayout(toolbar_layout)
        
    def create_table_section(self, parent_layout):
        """
        创建表格区域
        
        Args:
            parent_layout: 父布局对象
        """
        # 创建表格
        self.account_table = QTableWidget()
        self.setup_table()
        
        parent_layout.addWidget(self.account_table)
        
    def setup_table(self):
        """设置表格属性和列标题"""
        # 设置列数和列标题
        columns = ["用户名", "昵称", "手机号", "状态", "自动化", "最后登录", "备注", "操作"]
        self.account_table.setColumnCount(len(columns))
        self.account_table.setHorizontalHeaderLabels(columns)
        
        # 设置表格属性
        self.account_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.account_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.account_table.setAlternatingRowColors(True)

        # 设置行高以适应现代化按钮
        self.account_table.verticalHeader().setDefaultSectionSize(50)  # 增加行高适应新按钮
        
        # 设置列宽
        header = self.account_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)  # 用户名
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # 昵称
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)  # 手机号
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)  # 状态
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)  # 自动化
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Fixed)  # 最后登录
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.Stretch)  # 备注
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.Fixed)  # 操作

        self.account_table.setColumnWidth(0, 100)  # 用户名
        self.account_table.setColumnWidth(2, 100)  # 手机号
        self.account_table.setColumnWidth(3, 70)   # 状态
        self.account_table.setColumnWidth(4, 80)   # 自动化
        self.account_table.setColumnWidth(5, 130)  # 最后登录
        self.account_table.setColumnWidth(7, 400)  # 操作（增加宽度以容纳选品Cookie按钮）
        
        # 连接选择变化信号
        self.account_table.selectionModel().selectionChanged.connect(self.on_selection_changed)
        
    def create_status_section(self, parent_layout):
        """
        创建状态统计区域
        
        Args:
            parent_layout: 父布局对象
        """
        status_group = QGroupBox("账号统计")
        status_layout = QHBoxLayout(status_group)
        
        # 统计标签
        self.total_label = QLabel("总计: 0")
        self.online_label = QLabel("在线: 0")
        self.online_label.setObjectName("statusOnline")
        self.offline_label = QLabel("离线: 0")
        self.offline_label.setObjectName("statusOffline")
        
        status_layout.addWidget(self.total_label)
        status_layout.addWidget(self.online_label)
        status_layout.addWidget(self.offline_label)
        status_layout.addStretch()
        
        parent_layout.addWidget(status_group)
        
    def load_accounts_from_database(self):
        """从数据库加载账号数据"""
        try:
            # 导入账号仓库
            try:
                from data.repositories.account_repository import AccountRepository
            except ImportError:
                from kuaishou_automation_tool.data.repositories.account_repository import AccountRepository

            # 创建仓库实例并获取所有账号
            account_repo = AccountRepository()
            accounts = account_repo.get_all()

            # 清空现有数据
            self.accounts_data = []

            # 转换数据格式
            for account in accounts:
                account_data = {
                    "id": account.id,
                    "username": account.username,
                    "nickname": account.nickname,
                    "phone": account.phone or "",
                    "status": account.get_status_display(),
                    "automation_status": account.automation_status or "未配置",
                    "last_login": account.get_last_login_display(),
                    "remarks": account.remarks or ""
                }
                self.accounts_data.append(account_data)

            print(f"✅ 从数据库加载了 {len(self.accounts_data)} 个账号")

        except Exception as e:
            print(f"❌ 从数据库加载账号失败: {e}")
            import traceback
            traceback.print_exc()
            # 如果数据库加载失败，加载示例数据
            self.load_sample_data()

        self.refresh_table()

    def load_sample_data(self):
        """加载示例数据（作为备用）"""
        print("⚠️ 使用示例数据")
        sample_accounts = [
            {
                "id": None,
                "username": "demo_user_001",
                "nickname": "测试账号1",
                "phone": "***********",
                "status": "在线",
                "automation_status": "已配置",
                "last_login": "2024-01-27 10:30:00",
                "remarks": "主要账号，用于日常直播"
            },
            {
                "id": None,
                "username": "demo_user_002",
                "nickname": "测试账号2",
                "phone": "***********",
                "status": "离线",
                "automation_status": "未配置",
                "last_login": "2024-01-26 15:45:00",
                "remarks": "备用账号"
            }
        ]

        for account in sample_accounts:
            self.accounts_data.append(account)
        
    def refresh_table(self):
        """刷新表格显示"""
        self.account_table.setRowCount(len(self.accounts_data))

        for row, account in enumerate(self.accounts_data):
            # 用户名
            self.account_table.setItem(row, 0, QTableWidgetItem(account["username"]))

            # 昵称
            self.account_table.setItem(row, 1, QTableWidgetItem(account["nickname"]))

            # 手机号
            phone = account.get("phone", "未设置")
            self.account_table.setItem(row, 2, QTableWidgetItem(phone))

            # 状态
            status_item = QTableWidgetItem(account["status"])
            if account["status"] == "在线":
                status_item.setForeground(Qt.GlobalColor.green)
            else:
                status_item.setForeground(Qt.GlobalColor.red)
            self.account_table.setItem(row, 3, status_item)

            # 自动化状态
            automation_status = account.get("automation_status", "未配置")
            automation_item = QTableWidgetItem(self._get_automation_display(automation_status))
            self.account_table.setItem(row, 4, automation_item)

            # 最后登录
            self.account_table.setItem(row, 5, QTableWidgetItem(account["last_login"]))

            # 备注
            remarks = account.get("remarks", "")
            self.account_table.setItem(row, 6, QTableWidgetItem(remarks))

            # 操作按钮
            self.create_action_buttons(row, account)

        self.update_statistics()

    def create_action_buttons(self, row, account):
        """为指定行创建操作按钮"""
        # 创建按钮容器
        button_widget = QWidget()
        button_widget.setFixedHeight(40)  # 保持容器高度
        button_layout = QHBoxLayout(button_widget)
        button_layout.setContentsMargins(8, 1, 8, 1)  # 进一步减少上下边距
        button_layout.setSpacing(6)  # 适中的按钮间距
        button_layout.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignTop)  # 左对齐+顶部对齐

        # 登录按钮
        login_button = QPushButton("登录")
        login_button.setObjectName("loginButton")
        login_button.setToolTip("使用保存的Cookie自动登录到快手创作者中心")
        login_button.setFixedSize(60, 28)  # 缩小尺寸
        login_button.setCursor(Qt.CursorShape.PointingHandCursor)
        login_button.clicked.connect(lambda: self.login_account(account))

        # 编辑按钮
        edit_button = QPushButton("编辑")
        edit_button.setObjectName("editButton")
        edit_button.setToolTip("编辑账号信息和配置")
        edit_button.setFixedSize(60, 28)  # 缩小尺寸
        edit_button.setCursor(Qt.CursorShape.PointingHandCursor)
        edit_button.clicked.connect(lambda: self.edit_account_by_data(account))

        # 删除按钮
        delete_button = QPushButton("删除")
        delete_button.setObjectName("deleteButton")
        delete_button.setToolTip("永久删除此账号（不可恢复）")
        delete_button.setFixedSize(60, 28)  # 缩小尺寸
        delete_button.setCursor(Qt.CursorShape.PointingHandCursor)
        delete_button.clicked.connect(lambda: self.delete_account_by_data(account))

        # 开始任务按钮
        start_task_button = QPushButton("开始任务")
        start_task_button.setObjectName("startTaskButton")
        start_task_button.setToolTip("开始执行自动化任务")
        start_task_button.setFixedSize(70, 28)  # 稍微宽一点以容纳文字
        start_task_button.setCursor(Qt.CursorShape.PointingHandCursor)
        start_task_button.clicked.connect(lambda: self.start_task(account))

        # 选品Cookie按钮
        selection_cookie_button = QPushButton("选品Cookie")
        selection_cookie_button.setObjectName("selectionCookieButton")
        selection_cookie_button.setToolTip("获取选品中心Cookie")
        selection_cookie_button.setFixedSize(80, 28)
        selection_cookie_button.setCursor(Qt.CursorShape.PointingHandCursor)
        selection_cookie_button.clicked.connect(lambda: self.get_selection_cookie(account))

        # 添加按钮到布局
        button_layout.addWidget(login_button)
        button_layout.addWidget(edit_button)
        button_layout.addWidget(delete_button)
        button_layout.addWidget(start_task_button)
        button_layout.addWidget(selection_cookie_button)
        # 不添加stretch，让按钮紧凑排列

        # 将按钮容器添加到表格
        self.account_table.setCellWidget(row, 7, button_widget)

    def _get_automation_display(self, status):
        """获取自动化状态显示文本"""
        status_map = {
            "已配置": "✅ 已配置",
            "配置中": "🔄 配置中",
            "配置失败": "❌ 失败",
            "未配置": "⚪ 未配置"
        }
        return status_map.get(status, "⚪ 未配置")
        
    def update_statistics(self):
        """更新统计信息"""
        total = len(self.accounts_data)
        online = sum(1 for account in self.accounts_data if account["status"] == "在线")
        offline = total - online
        
        self.total_label.setText(f"总计: {total}")
        self.online_label.setText(f"在线: {online}")
        self.offline_label.setText(f"离线: {offline}")
        
    def on_selection_changed(self):
        """处理表格选择变化"""
        has_selection = bool(self.account_table.selectedItems())
        self.edit_button.setEnabled(has_selection)
        self.delete_button.setEnabled(has_selection)
        self.convert_cookie_button.setEnabled(has_selection)
        
    def add_account(self):
        """添加账号"""
        try:
            print("🔍 开始创建添加账号对话框...")
            dialog = AccountDialog(self)
            print("✅ 对话框创建成功，正在显示...")

            result = dialog.exec()
            print(f"📋 对话框结果: {result}")

            if result == QDialog.DialogCode.Accepted:
                print("✅ 用户确认添加账号")
                account_data = dialog.get_account_data()
                print(f"📊 获取的账号数据: {account_data}")

                # 保存到数据库
                saved_account = self.save_account_to_database(account_data)
                if saved_account:
                    print("✅ 账号保存到数据库成功")
                    # 重新从数据库加载数据
                    self.load_accounts_from_database()
                    self.account_added.emit(account_data)
                    print("✅ 账号添加完成")
                else:
                    print("❌ 账号保存到数据库失败")
                    QMessageBox.critical(self, "错误", "保存账号到数据库失败")
            else:
                print("❌ 用户取消添加账号")

        except Exception as e:
            print(f"❌ 添加账号时发生错误: {e}")
            import traceback
            traceback.print_exc()

            # 显示错误消息给用户
            try:
                QMessageBox.critical(self, "错误", f"添加账号时发生错误：\n{e}")
            except:
                pass

    def save_account_to_database(self, account_data):
        """保存账号到数据库"""
        try:
            # 导入账号仓库
            try:
                from data.repositories.account_repository import AccountRepository
            except ImportError:
                from kuaishou_automation_tool.data.repositories.account_repository import AccountRepository

            # 创建仓库实例
            account_repo = AccountRepository()

            # 准备数据库数据
            db_data = {
                "username": account_data["username"],
                "nickname": account_data["nickname"],
                "phone": account_data.get("phone", ""),
                "remarks": account_data.get("remarks", ""),
                "automation_status": "未配置"
            }

            # 保存到数据库
            saved_account = account_repo.create(db_data)
            return saved_account

        except Exception as e:
            print(f"❌ 保存账号到数据库失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def edit_account(self):
        """编辑账号"""
        current_row = self.account_table.currentRow()
        if current_row >= 0:
            account_data = self.accounts_data[current_row]
            dialog = AccountDialog(self, account_data)
            
            if dialog.exec() == QDialog.DialogCode.Accepted:
                updated_data = dialog.get_account_data()
                # 保留状态和登录时间
                updated_data["status"] = account_data["status"]
                updated_data["last_login"] = account_data["last_login"]
                
                self.accounts_data[current_row] = updated_data
                self.refresh_table()
                self.account_updated.emit(account_data["username"], updated_data)
                
    def delete_account(self):
        """删除账号"""
        current_row = self.account_table.currentRow()
        if current_row >= 0:
            account_data = self.accounts_data[current_row]
            
            reply = QMessageBox.question(
                self, "确认删除",
                f"确定要删除账号 '{account_data['username']}' 吗？\n此操作不可撤销。",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                username = account_data["username"]
                account_id = account_data.get("id")

                # 从数据库删除
                if account_id and self.delete_account_from_database(account_id):
                    print("✅ 账号从数据库删除成功")
                    # 重新从数据库加载数据
                    self.load_accounts_from_database()
                    self.account_deleted.emit(username)
                    print("✅ 账号删除完成")
                else:
                    print("❌ 账号从数据库删除失败")
                    QMessageBox.critical(self, "错误", "从数据库删除账号失败")

    def delete_account_from_database(self, account_id):
        """从数据库删除账号"""
        try:
            # 导入账号仓库
            try:
                from data.repositories.account_repository import AccountRepository
            except ImportError:
                from kuaishou_automation_tool.data.repositories.account_repository import AccountRepository

            # 创建仓库实例
            account_repo = AccountRepository()

            # 从数据库删除
            success = account_repo.delete(account_id)
            return success

        except Exception as e:
            print(f"❌ 从数据库删除账号失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def refresh_accounts(self):
        """刷新账号状态"""
        # 模拟状态刷新
        import random
        for account in self.accounts_data:
            if random.choice([True, False]):
                account["status"] = "在线"
                account["last_login"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            else:
                account["status"] = "离线"

        self.refresh_table()

    def convert_cookie_domain(self):
        """转换选中账号的Cookie到对应域名"""
        try:
            # 获取选中的账号
            selected_rows = self.account_table.selectionModel().selectedRows()
            if not selected_rows:
                QMessageBox.warning(self, "提示", "请先选择要转换Cookie的账号")
                return

            # 获取选中账号的信息
            row = selected_rows[0].row()
            account_data = self.accounts_data[row]
            phone = account_data.get('phone', account_data.get('username', ''))

            if not phone:
                QMessageBox.warning(self, "错误", "无法获取账号标识信息")
                return

            print(f"🔄 开始转换Cookie: {phone}")

            # 检查Cookie文件是否存在
            from business.cookie_manager import CookieManager
            cookie_manager = CookieManager()

            # 检查Cookie是否存在和可转换性
            domain_info = cookie_manager.get_cookie_domain_info(phone)
            if not domain_info:
                QMessageBox.warning(
                    self, "Cookie不存在",
                    f"账号 {phone} 的Cookie文件不存在\n\n"
                    f"请先进行自动化登录配置以获取Cookie。"
                )
                return

            if not domain_info.get('conversion_capable', False):
                QMessageBox.warning(
                    self, "无法转换",
                    f"账号 {phone} 的Cookie不支持域名转换\n\n"
                    f"可能原因:\n"
                    f"• Cookie格式过旧\n"
                    f"• 缺少必要的转换信息\n"
                    f"• Cookie已损坏\n\n"
                    f"建议重新进行自动化登录配置。"
                )
                return

            # 显示转换选项对话框
            self._show_cookie_conversion_dialog(phone, domain_info)

        except Exception as e:
            error_msg = f"Cookie转换过程中发生错误:\n{e}"
            QMessageBox.critical(self, "转换错误", error_msg)
            print(f"❌ Cookie转换异常: {e}")
            import traceback
            traceback.print_exc()

    def _show_cookie_conversion_dialog(self, phone: str, domain_info: dict):
        """显示Cookie转换选项对话框"""
        try:
            current_domain = domain_info.get('primary_domain', 'kuaishou.com')

            # 确定转换目标
            if 'kuaishou.com' in current_domain:
                target_domain = 'kwaixiaodian.com'
                target_name = '选品中心'
            else:
                target_domain = 'kuaishou.com'
                target_name = '创作者中心'

            dialog_msg = f"账号: {phone}\n"
            dialog_msg += f"当前域名: {current_domain}\n\n"
            dialog_msg += f"将转换到: {target_name} ({target_domain})\n\n"
            dialog_msg += f"转换后将生成新的Cookie文件，原文件保持不变。\n"
            dialog_msg += f"是否继续？"

            reply = QMessageBox.question(
                self, "Cookie域名转换", dialog_msg,
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.Yes
            )

            if reply == QMessageBox.StandardButton.Yes:
                self._execute_cookie_conversion(phone, target_domain, target_name)

        except Exception as e:
            error_msg = f"显示转换对话框时发生错误:\n{e}"
            QMessageBox.critical(self, "对话框错误", error_msg)
            print(f"❌ 转换对话框异常: {e}")

    def _execute_cookie_conversion(self, phone: str, target_domain: str, target_name: str):
        """执行Cookie转换"""
        try:
            print(f"🔄 执行Cookie转换: {phone} -> {target_domain}")

            from business.cookie_manager import CookieManager
            cookie_manager = CookieManager()

            # 执行转换
            converted_file_path = cookie_manager.auto_convert_cookies_for_domain(phone, target_domain)

            if converted_file_path:
                success_msg = f"🎉 Cookie转换成功！\n\n"
                success_msg += f"原始账号: {phone}\n"
                success_msg += f"目标域名: {target_name} ({target_domain})\n"
                success_msg += f"转换后文件: {converted_file_path}\n\n"
                success_msg += f"现在您可以使用转换后的Cookie在{target_name}进行自动化操作了！\n\n"
                success_msg += f"是否要立即测试转换后的Cookie？"

                test_reply = QMessageBox.question(
                    self, "转换成功", success_msg,
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.No
                )

                if test_reply == QMessageBox.StandardButton.Yes:
                    self._test_converted_cookies(converted_file_path, target_domain, target_name)

                print(f"✅ Cookie转换成功: {converted_file_path}")

            else:
                error_msg = f"❌ Cookie转换失败\n\n"
                error_msg += f"可能原因:\n"
                error_msg += f"• Cookie格式不兼容\n"
                error_msg += f"• 缺少必要的转换信息\n"
                error_msg += f"• 文件保存权限问题\n\n"
                error_msg += f"请检查日志获取详细错误信息。"

                QMessageBox.warning(self, "转换失败", error_msg)
                print(f"❌ Cookie转换失败")

        except Exception as e:
            error_msg = f"执行Cookie转换时发生错误:\n{e}"
            QMessageBox.critical(self, "转换错误", error_msg)
            print(f"❌ Cookie转换执行异常: {e}")
            import traceback
            traceback.print_exc()

    def login_account(self, account):
        """登录指定账号"""
        print(f"🔑 开始登录账号: {account['username']}")

        # 在新线程中执行登录操作，避免阻塞UI
        login_thread = threading.Thread(
            target=self._perform_login,
            args=(account,),
            daemon=True
        )
        login_thread.start()

    def start_task(self, account):
        """开始执行自动化任务"""
        print(f"🚀 开始执行任务: {account['username']}")

        # 获取选品设置配置
        product_settings = self._get_product_selection_settings()

        # 显示任务开始的消息框，包含选品设置信息
        selected_categories = product_settings.get('selected_categories', [])
        category_text = "、".join(selected_categories) if selected_categories else "无"

        reply = QMessageBox.question(
            self, "开始任务",
            f"确定要为账号 '{account['username']}' 开始执行自动化任务吗？\n\n"
            f"任务将包括：\n"
            f"• 自动登录账号\n"
            f"• 跳转到选品中心\n"
            f"• 根据选品设置筛选商品\n"
            f"• 执行图文视频生成\n"
            f"• 自动发布内容\n\n"
            f"当前选品类目: {category_text}",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.Yes
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 在新线程中执行任务，避免阻塞UI
            task_thread = threading.Thread(
                target=self._execute_automation_task,
                args=(account, product_settings),
                daemon=True
            )
            task_thread.start()

            # 显示任务开始提示
            QMessageBox.information(
                self, "任务已启动",
                f"账号 '{account['username']}' 的自动化任务已在后台启动。\n\n"
                f"您可以在控制台查看任务执行进度。"
            )

    def _get_product_selection_settings(self):
        """获取选品设置配置"""
        try:
            import json
            import os

            # 获取配置文件路径
            config_file = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'video_settings.json')

            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                return {
                    'selected_categories': settings.get('selected_categories', []),
                    'selected_attributes': settings.get('selected_attributes', []),
                    'min_price': settings.get('min_price', 0.0),
                    'max_price': settings.get('max_price', 999.0),
                    'min_sales': settings.get('min_sales', 0),
                    'min_rating': settings.get('min_rating', 90.0)
                }
            else:
                print("⚠️ 选品设置配置文件不存在，使用默认设置")
                return {
                    'selected_categories': ['all'],
                    'selected_attributes': [],
                    'min_price': 0.0,
                    'max_price': 999.0,
                    'min_sales': 0,
                    'min_rating': 90.0
                }

        except Exception as e:
            print(f"❌ 读取选品设置失败: {e}")
            return {
                'selected_categories': ['all'],
                'selected_attributes': [],
                'min_price': 0.0,
                'max_price': 999.0,
                'min_sales': 0,
                'min_rating': 90.0
            }

    def _execute_automation_task(self, account, product_settings):
        """执行自动化任务（在后台线程中运行）"""
        driver = None
        try:
            print(f"📋 开始执行账号 {account['username']} 的自动化任务")
            print(f"🎯 选品设置: {product_settings}")

            # 保存当前账号信息，供其他方法使用
            self._current_account = account

            # 步骤1：登录账号
            print("🔑 步骤1: 登录账号...")
            driver = self._login_account_for_task(account)
            if not driver:
                print("❌ 账号登录失败，任务终止")
                return

            # 步骤2：跳转到选品中心
            print("🛍️ 步骤2: 跳转到选品中心...")
            self._navigate_to_selection_center(driver)

            # 步骤3：根据选品设置筛选商品
            print("🎯 步骤3: 根据选品设置筛选商品...")
            self._apply_product_filters(driver, product_settings)

            # 步骤4：生成图文视频内容
            print("🎬 步骤4: 准备生成图文视频内容...")
            # 这里可以调用图文视频生成逻辑

            # 步骤5：发布内容
            print("📤 步骤5: 准备发布内容...")
            # 这里可以调用内容发布逻辑

            # 步骤6：收集数据统计
            print("📊 步骤6: 收集数据统计...")
            # 这里可以调用数据统计逻辑

            print(f"✅ 账号 {account['username']} 的自动化任务执行完成")

        except Exception as e:
            print(f"❌ 账号 {account['username']} 的自动化任务执行失败: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # 清理浏览器资源
            if driver:
                try:
                    print("🧹 清理浏览器资源...")
                    driver.quit()
                except:
                    pass

    def get_selection_cookie(self, account):
        """获取选品中心Cookie"""
        print(f"🍪 开始获取选品中心Cookie: {account['username']}")

        # 显示获取Cookie的说明对话框
        reply = QMessageBox.question(
            self, "获取选品中心Cookie",
            f"将为账号 '{account['username']}' 获取选品中心Cookie。\n\n"
            f"操作步骤：\n"
            f"1. 系统将打开选品中心登录页面\n"
            f"2. 请手动登录选品中心\n"
            f"3. 登录成功后，系统将自动保存Cookie\n"
            f"4. 保存的Cookie将用于后续的自动化任务\n\n"
            f"是否继续？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.Yes
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 在新线程中执行Cookie获取，避免阻塞UI
            cookie_thread = threading.Thread(
                target=self._get_selection_cookie_process,
                args=(account,),
                daemon=True
            )
            cookie_thread.start()

            # 显示提示
            QMessageBox.information(
                self, "Cookie获取已启动",
                f"选品中心Cookie获取已启动。\n\n"
                f"请在打开的浏览器中手动登录选品中心，\n"
                f"登录成功后系统将自动保存Cookie。"
            )

    def _get_selection_cookie_process(self, account):
        """获取选品中心Cookie的处理过程"""
        driver = None
        try:
            print(f"🍪 开始获取账号 {account['username']} 的选品中心Cookie")

            # 启动浏览器
            print("🌐 启动浏览器...")
            driver = self._create_browser_driver()

            # 直接跳转到选品中心登录页面
            print("🛍️ 跳转到选品中心...")
            driver.get("https://cps.kwaixiaodian.com/pc/promoter/selection-center/home")

            # 等待用户手动登录
            print("⏳ 等待用户手动登录...")
            print("💡 请在浏览器中手动登录选品中心")

            # 监控登录状态
            login_success = False
            max_wait_time = 300  # 最多等待5分钟
            check_interval = 5   # 每5秒检查一次

            for i in range(0, max_wait_time, check_interval):
                import time
                time.sleep(check_interval)

                try:
                    current_url = driver.current_url
                    print(f"🔍 当前URL: {current_url}")

                    # 检查是否登录成功（不在登录页面）
                    if "login" not in current_url.lower() and "cps.kwaixiaodian.com" in current_url:
                        # 检查页面内容
                        page_source = driver.page_source
                        if "登录" not in page_source or "选品中心" in page_source:
                            print("✅ 检测到登录成功")
                            login_success = True
                            break

                except Exception as e:
                    print(f"⚠️ 检查登录状态时出错: {e}")
                    continue

                print(f"⏳ 等待登录中... ({i+check_interval}/{max_wait_time}秒)")

            if login_success:
                # 获取Cookie
                print("🍪 获取选品中心Cookie...")
                cookies = driver.get_cookies()

                if cookies:
                    # 保存Cookie到专门的选品中心Cookie文件
                    selection_cookie_file = self._get_selection_cookie_file_path(account)
                    self._save_selection_cookies_to_file(cookies, selection_cookie_file)
                    print(f"✅ 选品中心Cookie已保存: {selection_cookie_file}")
                    print(f"🍪 共保存 {len(cookies)} 个Cookie")
                else:
                    print("❌ 未获取到Cookie")
            else:
                print("❌ 登录超时或失败")

        except Exception as e:
            print(f"❌ 获取选品中心Cookie失败: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # 询问是否关闭浏览器
            if driver:
                try:
                    print("💡 Cookie获取完成，浏览器将在10秒后自动关闭")
                    import time
                    time.sleep(10)
                    driver.quit()
                except:
                    pass

    def _get_selection_cookie_file_path(self, account):
        """获取选品中心Cookie文件路径"""
        try:
            # 获取Cookie存储路径
            cookies_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'cookies')
            os.makedirs(cookies_dir, exist_ok=True)

            # 选品中心Cookie文件名
            filename = f"{account['username']}_selection_cookies.json"
            return os.path.join(cookies_dir, filename)

        except Exception as e:
            print(f"❌ 获取选品中心Cookie文件路径失败: {e}")
            return None

    def _login_account_for_task(self, account):
        """为任务执行登录账号，返回浏览器驱动"""
        try:
            print(f"🔍 检查Cookie文件...")

            # 检查Cookie文件是否存在
            cookie_file_path = self._get_cookie_file_path(account)
            if not os.path.exists(cookie_file_path):
                print(f"❌ Cookie文件不存在: {cookie_file_path}")
                return None

            # 读取Cookie文件
            print("📖 读取Cookie文件...")
            cookies = self._load_cookies_from_file(cookie_file_path)
            if not cookies:
                print("❌ Cookie文件为空或格式错误")
                return None

            # 启动浏览器
            print("🌐 启动浏览器...")
            driver = self._create_browser_driver()

            # 先访问快手主域名
            print("🏠 访问快手主页...")
            driver.get("https://www.kuaishou.com")

            # 等待页面加载
            import time
            time.sleep(2)

            # 注入Cookie（自动适应域名）
            print("🍪 注入Cookie...")
            self._inject_cookies(driver, cookies)

            # 跳转到创作者中心验证登录（与单独登录保持一致）
            print("🔍 跳转到创作者中心验证登录...")
            driver.get("https://cp.kuaishou.com/profile")

            # 等待页面加载（与单独登录保持一致）
            time.sleep(3)

            # 验证登录状态（使用与单独登录相同的逻辑）
            print("🔍 验证登录状态...")
            if self._check_login_success(driver):
                print("✅ 账号登录成功")
            else:
                print("❌ 账号登录失败")
                driver.quit()
                return None

            print("✅ 账号登录成功，Cookie已生效")
            return driver

        except Exception as e:
            print(f"❌ 登录过程中发生错误: {e}")
            return None

    def _navigate_to_selection_center(self, driver):
        """跳转到选品中心"""
        try:
            print("🎯 跳转到选品中心...")

            # 先验证当前会话是否有效
            try:
                current_url = driver.current_url
                print(f"🔍 登录验证后的当前页面: {current_url}")
            except Exception as e:
                print(f"❌ 浏览器会话已断开: {e}")
                raise Exception("浏览器会话无效，无法继续操作")

            # 现在跳转到选品中心
            print("🛍️ 正在跳转到选品中心...")
            driver.get("https://cps.kwaixiaodian.com/pc/promoter/selection-center/home")

            # 等待页面初始加载
            import time
            print("⏳ 等待选品中心页面初始加载...")
            time.sleep(5)

            # 检查是否需要重新注入Cookie（可能跳转到了新域名）
            current_url = driver.current_url
            print(f"🔍 选品中心页面URL: {current_url}")

            if "login" in current_url.lower() or "cps.kwaixiaodian.com" not in current_url:
                print("🍪 检测到需要选品中心登录...")

                # 智能选择Cookie（根据当前域名）
                try:
                    account = getattr(self, '_current_account', None)
                    if account:
                        current_url = driver.current_url

                        if "kwaixiaodian.com" in current_url:
                            # 在选品中心，优先使用选品中心Cookie
                            print("🍪 在选品中心，优先使用选品中心Cookie...")
                            selection_cookie_file = self._get_selection_cookie_file_path(account)
                            if os.path.exists(selection_cookie_file):
                                selection_cookies = self._load_cookies_from_file(selection_cookie_file)
                                if selection_cookies:
                                    self._inject_cookies(driver, selection_cookies)
                                    print("✅ 选品中心Cookie注入成功")
                                else:
                                    print("⚠️ 选品中心Cookie文件为空，尝试创作者Cookie")
                                    self._fallback_to_creator_cookies(account, driver)
                            else:
                                print("⚠️ 选品中心Cookie不存在，尝试创作者Cookie")
                                self._fallback_to_creator_cookies(account, driver)
                        else:
                            # 在其他域名，使用创作者中心Cookie
                            print("🍪 使用创作者中心Cookie...")
                            cookie_file_path = self._get_cookie_file_path(account)
                            cookies = self._load_cookies_from_file(cookie_file_path)
                            if cookies:
                                self._inject_cookies(driver, cookies)
                                print("✅ 创作者中心Cookie注入成功")

                        # 刷新页面确保Cookie生效
                        print("🔄 刷新页面确保Cookie生效...")
                        driver.refresh()
                        time.sleep(5)
                    else:
                        print("⚠️ 无法获取当前账号信息")

                except Exception as e:
                    print(f"⚠️ 重新注入Cookie失败: {e}")

            # 再次检查页面状态
            print("⏳ 等待选品中心页面完全加载...")
            time.sleep(10)

            # 验证页面是否成功加载
            try:
                final_url = driver.current_url
                print(f"🔍 选品中心最终页面: {final_url}")

                if "selection-center" in final_url or "cps.kwaixiaodian.com" in final_url:
                    print("✅ 成功跳转到选品中心")

                    # 等待页面元素加载
                    print("⏳ 等待页面元素加载...")
                    time.sleep(5)

                elif "login" in final_url.lower():
                    print("❌ 跳转到了登录页面，可能需要重新登录")
                    raise Exception("需要重新登录")
                else:
                    print(f"⚠️ 页面跳转到了意外的URL: {final_url}")
                    print("🔄 尝试继续执行选品操作...")

            except Exception as e:
                print(f"❌ 验证选品中心页面失败: {e}")
                print("🔄 尝试继续执行，可能页面已经加载但验证失败")

        except Exception as e:
            print(f"❌ 跳转选品中心失败: {e}")
            raise

    def _apply_product_filters(self, driver, product_settings):
        """根据选品设置应用商品筛选"""
        try:
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            import time

            print("🎯 开始应用商品筛选...")

            # 首先验证浏览器会话是否有效
            try:
                current_url = driver.current_url
                print(f"🔍 准备执行筛选的当前页面URL: {current_url}")

                if not current_url or "about:blank" in current_url:
                    raise Exception("浏览器会话无效")

                # 检查是否在选品中心页面
                if "cps.kwaixiaodian.com" not in current_url and "selection-center" not in current_url:
                    print(f"⚠️ 当前不在选品中心页面: {current_url}")
                    print("🔄 尝试重新跳转到选品中心...")
                    driver.get("https://cps.kwaixiaodian.com/pc/promoter/selection-center/home")
                    time.sleep(10)

                    # 再次检查
                    current_url = driver.current_url
                    print(f"🔍 重新跳转后的页面URL: {current_url}")

            except Exception as e:
                print(f"❌ 浏览器会话检查失败: {e}")
                print("⚠️ 跳过商品筛选步骤")
                return

            # 等待页面加载完成
            wait = WebDriverWait(driver, 15)

            # 获取选中的类目
            selected_categories = product_settings.get('selected_categories', [])
            print(f"📂 需要选择的类目: {selected_categories}")

            if not selected_categories:
                print("⚠️ 没有选择任何类目，跳过筛选")
                return

            # 使用更灵活的选择器策略
            category_selectors = {
                'all': ['span:contains("全部")', 'span[data-category="all"]', '//span[text()="全部"]'],
                'food_drink': ['span:contains("食品饮料")', 'span[data-category="food"]', '//span[text()="食品饮料"]'],
                'home': ['span:contains("家居用品")', 'span[data-category="home"]', '//span[text()="家居用品"]'],
                'women_fashion': ['span:contains("女装女鞋")', 'span[data-category="women"]', '//span[text()="女装女鞋"]'],
                'beauty': ['span:contains("美妆护肤")', 'span[data-category="beauty"]', '//span[text()="美妆护肤"]'],
                'personal_care': ['span:contains("个护清洁")', 'span[data-category="care"]', '//span[text()="个护清洁"]'],
                'medical': ['span:contains("医疗保健")', 'span[data-category="medical"]', '//span[text()="医疗保健"]'],
                'baby': ['span:contains("母婴玩具")', 'span[data-category="baby"]', '//span[text()="母婴玩具"]'],
                'tea_fresh': ['span:contains("茶酒生鲜")', 'span[data-category="fresh"]', '//span[text()="茶酒生鲜"]'],
                'men_fashion': ['span:contains("男装男鞋")', 'span[data-category="men"]', '//span[text()="男装男鞋"]'],
                'sports': ['span:contains("运动户外")', 'span[data-category="sports"]', '//span[text()="运动户外"]'],
                'more': ['span:contains("更多")', 'span[data-category="more"]', '//span[text()="更多"]']
            }

            # 尝试点击对应的类目元素
            success_count = 0
            for category in selected_categories:
                if category in category_selectors:
                    selectors = category_selectors[category]
                    clicked = False

                    for selector in selectors:
                        try:
                            print(f"🖱️ 尝试点击类目: {category} (选择器: {selector})")

                            if selector.startswith('//'):
                                # XPath选择器
                                element = wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                            else:
                                # CSS选择器
                                element = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, selector)))

                            element.click()
                            print(f"✅ 成功点击类目: {category}")
                            success_count += 1
                            clicked = True
                            time.sleep(2)  # 等待页面响应
                            break

                        except Exception as e:
                            print(f"⚠️ 选择器 {selector} 失败: {str(e)[:100]}...")
                            continue

                    if not clicked:
                        print(f"❌ 所有选择器都失败，无法点击类目: {category}")
                else:
                    print(f"⚠️ 未知的类目: {category}")

            print(f"✅ 商品筛选应用完成，成功点击 {success_count}/{len(selected_categories)} 个类目")

        except Exception as e:
            print(f"❌ 应用商品筛选失败: {e}")
            print("⚠️ 继续执行后续步骤")

    def _perform_login(self, account):
        """执行登录操作（在后台线程中运行）"""
        try:
            # 显示进度提示
            self._show_login_progress("正在检查Cookie文件...")

            # 检查Cookie文件是否存在
            cookie_file_path = self._get_cookie_file_path(account)
            if not os.path.exists(cookie_file_path):
                self._show_login_error(f"Cookie文件不存在: {cookie_file_path}\n\n该账号尚未保存Cookie，请先进行自动化登录配置")
                return

            # 读取Cookie文件
            self._show_login_progress("正在读取Cookie文件...")
            cookies = self._load_cookies_from_file(cookie_file_path)
            if not cookies:
                self._show_login_error("Cookie文件为空或格式错误")
                return

            # 检查Cookie的双域名支持能力
            self._show_login_progress("正在分析Cookie兼容性...")
            try:
                from business.cookie_manager import CookieManager
                cookie_manager = CookieManager()
                domain_info = cookie_manager.get_cookie_domain_info(account.get('phone', account.get('username', 'unknown')))
                if domain_info:
                    supported_domains = domain_info.get('supported_domains', ['kuaishou.com'])
                    conversion_capable = domain_info.get('conversion_capable', False)
                    print(f"🔍 Cookie域名支持: {supported_domains}, 可转换: {conversion_capable}")
                    if conversion_capable:
                        self._show_login_progress("检测到智能双域名Cookie，支持kuaishou.com和kwaixiaodian.com互转...")
                    else:
                        self._show_login_progress(f"Cookie支持域名: {', '.join(supported_domains)}")
            except Exception as e:
                print(f"⚠️ Cookie兼容性检查失败: {e}")

            # 启动浏览器
            self._show_login_progress("正在启动浏览器...")
            driver = self._create_browser_driver()

            # 先访问快手主域名以设置正确的域名上下文
            self._show_login_progress("正在打开快手主页...")
            driver.get("https://www.kuaishou.com")

            # 等待页面加载
            import time
            time.sleep(2)

            # 智能注入Cookie（支持双域名转换）
            self._show_login_progress("正在智能注入Cookie...")
            self._inject_cookies(driver, cookies)

            # 导航到目标页面
            self._show_login_progress("正在跳转到创作者中心...")
            target_url = "https://cp.kuaishou.com/profile"
            driver.get(target_url)
            time.sleep(3)

            # 检查登录状态
            self._show_login_progress("正在验证登录状态...")
            if self._check_login_success(driver):
                self._show_login_success(f"账号 {account['username']} 登录成功！")
                # 更新账号状态
                self._update_account_status(account, "在线")
            else:
                self._show_login_error("Cookie可能已过期，请重新进行自动化登录配置")
                # 不关闭浏览器，让用户手动登录

        except Exception as e:
            print(f"❌ 登录失败: {e}")
            import traceback
            traceback.print_exc()
            self._show_login_error(f"登录过程中发生错误: {e}")

    def edit_account_by_data(self, account):
        """根据账号数据编辑账号"""
        # 找到账号在列表中的索引
        for i, acc in enumerate(self.accounts_data):
            if acc['username'] == account['username']:
                # 设置当前选中行
                self.account_table.selectRow(i)
                # 调用原有的编辑方法
                self.edit_account()
                break

    def delete_account_by_data(self, account):
        """根据账号数据删除账号"""
        # 找到账号在列表中的索引
        for i, acc in enumerate(self.accounts_data):
            if acc['username'] == account['username']:
                # 设置当前选中行
                self.account_table.selectRow(i)
                # 调用原有的删除方法
                self.delete_account()
                break

    def _get_cookie_file_path(self, account):
        """获取Cookie文件路径"""
        try:
            # 获取Cookie存储路径
            try:
                from data.models.base import ConfigManager
            except ImportError:
                from kuaishou_automation_tool.data.models.base import ConfigManager

            config_manager = ConfigManager()
            storage_path = config_manager.get('automation.cookie_storage_path')

            if not storage_path:
                # 使用默认路径
                project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                storage_path = os.path.join(project_root, 'cookies')

            # 确定文件名
            phone = account.get('phone', '').strip()
            filename = f"{phone}.txt" if phone else f"{account['username']}.txt"

            return os.path.join(storage_path, filename)

        except Exception as e:
            print(f"❌ 获取Cookie文件路径失败: {e}")
            # 回退到默认路径
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            storage_path = os.path.join(project_root, 'cookies')
            phone = account.get('phone', '').strip()
            filename = f"{phone}.txt" if phone else f"{account['username']}.txt"
            return os.path.join(storage_path, filename)

    def _load_cookies_from_file(self, file_path):
        """从文件加载Cookie"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                import json
                cookie_data = json.load(f)
                return cookie_data.get('cookies', [])
        except Exception as e:
            print(f"❌ 读取Cookie文件失败: {e}")
            return None

    def _create_browser_driver(self):
        """创建浏览器驱动，增强稳定性和会话管理"""
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        import warnings

        # 设置Chrome选项
        options = Options()
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("--start-maximized")

        # 增加稳定性选项
        options.add_argument("--disable-extensions")
        options.add_argument("--disable-plugins")
        options.add_argument("--disable-images")  # 禁用图片加载以提高速度
        options.add_argument("--no-first-run")
        options.add_argument("--disable-default-apps")
        # 注意：不禁用JavaScript，因为Cookie注入和页面验证需要JS支持

        # 设置页面加载策略
        options.page_load_strategy = 'normal'  # 等待页面完全加载

        # 设置User-Agent
        user_agent = ("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
                     "(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        options.add_argument(f"--user-agent={user_agent}")

        driver = None
        try:
            # 方法1: 直接使用系统环境中的chromedriver
            print("🌐 尝试使用系统PATH中的ChromeDriver...")
            driver = webdriver.Chrome(options=options)
            print("✅ 使用系统ChromeDriver成功")
        except Exception as chrome_error:
            print(f"⚠️ 系统ChromeDriver失败: {chrome_error}")

            # 方法2: 使用ChromeDriverManager自动下载
            try:
                print("🌐 尝试使用ChromeDriverManager自动下载...")
                from webdriver_manager.chrome import ChromeDriverManager
                # 禁用版本检查警告
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")
                    service = Service(ChromeDriverManager().install())
                driver = webdriver.Chrome(service=service, options=options)
                print("✅ 使用ChromeDriverManager成功")
            except Exception as manager_error:
                print(f"⚠️ ChromeDriverManager失败: {manager_error}")

                # 方法3: 尝试其他浏览器
                try:
                    print("🌐 尝试使用Firefox...")
                    from selenium.webdriver.firefox.options import Options as FirefoxOptions
                    firefox_options = FirefoxOptions()
                    firefox_options.add_argument("--start-maximized")
                    driver = webdriver.Firefox(options=firefox_options)
                    print("✅ 使用Firefox成功")
                except Exception as firefox_error:
                    try:
                        print("🌐 尝试使用Edge...")
                        from selenium.webdriver.edge.options import Options as EdgeOptions
                        edge_options = EdgeOptions()
                        edge_options.add_argument("--start-maximized")
                        driver = webdriver.Edge(options=edge_options)
                        print("✅ 使用Edge成功")
                    except Exception as edge_error:
                        raise Exception(f"所有浏览器驱动都失败！Chrome: {chrome_error}, Manager: {manager_error}, Firefox: {firefox_error}, Edge: {edge_error}")

        if driver:
            try:
                # 设置超时时间，避免会话过早断开
                driver.implicitly_wait(10)  # 隐式等待10秒
                driver.set_page_load_timeout(30)  # 页面加载超时30秒
                driver.set_script_timeout(30)  # 脚本执行超时30秒

                # 执行反检测脚本
                driver.execute_script(
                    "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})"
                )
                print("✅ WebDriver配置完成，反检测脚本已执行")

            except Exception as script_error:
                print(f"⚠️ WebDriver配置失败: {script_error}")
                # 即使脚本失败，也继续使用driver

        return driver

    def _inject_cookies(self, driver, cookies):
        """智能注入Cookie到浏览器（支持双域名自动转换）"""
        try:
            print(f"🍪 开始智能注入 {len(cookies)} 个Cookie")
            current_url = driver.current_url
            print(f"🍪 当前页面URL: {current_url}")

            # 自动检测当前域名
            target_domain = ".kuaishou.com"  # 默认域名
            if "kwaixiaodian.com" in current_url:
                target_domain = ".kwaixiaodian.com"
            elif "kuaishou.com" in current_url:
                target_domain = ".kuaishou.com"

            print(f"🍪 自动检测目标域名: {target_domain}")

            # 使用智能双域名Cookie转换
            converted_cookies = self._convert_cookies_to_dual_domain(cookies, target_domain)

            success_count = 0
            total_cookies = len(converted_cookies)

            for i, cookie in enumerate(converted_cookies):
                if isinstance(cookie, dict) and 'name' in cookie and 'value' in cookie:
                    print(f"🍪 处理Cookie {i+1}/{total_cookies}: {cookie['name']}")

                    # 创建清理后的Cookie
                    clean_cookie = {
                        'name': cookie['name'],
                        'value': cookie['value'],
                        'domain': cookie['domain'],  # 使用转换后的域名
                        'path': cookie.get('path', '/'),
                    }

                    # 只添加有效的可选字段
                    if 'secure' in cookie:
                        clean_cookie['secure'] = cookie['secure']
                    if 'httpOnly' in cookie:
                        clean_cookie['httpOnly'] = cookie['httpOnly']

                    try:
                        driver.add_cookie(clean_cookie)
                        success_count += 1
                        print(f"✅ Cookie添加成功: {cookie['name']} -> {cookie['domain']}")
                    except Exception as e:
                        print(f"⚠️ 添加Cookie失败: {cookie.get('name', 'unknown')} - {e}")
                        print(f"   Cookie详情: {clean_cookie}")
                else:
                    print(f"⚠️ 跳过无效Cookie: {cookie}")

            print(f"🍪 智能Cookie注入完成，成功: {success_count}/{total_cookies} (目标域名: {target_domain})")

        except Exception as e:
            print(f"❌ 注入Cookie失败: {e}")
            import traceback
            traceback.print_exc()
            raise

    def _inject_cookies_for_domain(self, driver, cookies, target_domain):
        """为特定域名注入Cookie"""
        try:
            print(f"🍪 开始为域名 {target_domain} 注入 {len(cookies)} 个Cookie")
            current_url = driver.current_url
            print(f"🍪 当前页面URL: {current_url}")

            success_count = 0
            for i, cookie in enumerate(cookies):
                # 确保Cookie格式正确
                if isinstance(cookie, dict) and 'name' in cookie and 'value' in cookie:
                    print(f"🍪 处理Cookie {i+1}: {cookie['name']}")

                    # 为目标域名设置Cookie
                    clean_cookie = {
                        'name': cookie['name'],
                        'value': cookie['value'],
                        'domain': f".{target_domain}",  # 使用目标域名
                        'path': cookie.get('path', '/'),
                    }

                    # 只添加有效的可选字段
                    if 'secure' in cookie:
                        clean_cookie['secure'] = cookie['secure']
                    if 'httpOnly' in cookie:
                        clean_cookie['httpOnly'] = cookie['httpOnly']

                    try:
                        driver.add_cookie(clean_cookie)
                        success_count += 1
                        print(f"✅ Cookie添加成功: {cookie['name']} (域名: {target_domain})")
                    except Exception as e:
                        print(f"⚠️ 添加Cookie失败: {cookie.get('name', 'unknown')} - {e}")
                        print(f"   Cookie详情: {clean_cookie}")
                else:
                    print(f"⚠️ 跳过无效Cookie: {cookie}")

            print(f"🍪 Cookie注入完成，成功: {success_count}/{len(cookies)}")

        except Exception as e:
            print(f"❌ 注入Cookie失败: {e}")
            import traceback
            traceback.print_exc()

    def _convert_cookies_to_dual_domain(self, cookies, target_domain):
        """智能双域名Cookie转换 - 支持kuaishou.com和kwaixiaodian.com互转"""
        try:
            print(f"🔄 开始智能双域名Cookie转换，目标域名: {target_domain}")
            converted_cookies = []

            # 定义Cookie分类和转换规则
            cookie_rules = {
                # 通用Cookie - 可以在两个域名间互转
                'universal': {
                    'userId', 'bUserId', 'did', '_did', 'clientid', 'client_key'
                },

                # kuaishou.com 特有Cookie
                'kuaishou_specific': {
                    'kuaishou.web.cp.api_ph', 'kuaishou.web.cp.api_st',
                    'kuaishou.web.st', 'kuaishou.web.ph'
                },

                # kwaixiaodian.com 特有Cookie
                'kwaixiaodian_specific': {
                    'sid', 'kuaishou.shop.b_ph', 'kuaishou.shop.b_st',
                    'kuaishou.shop.st', 'kuaishou.shop.ph'
                },

                # Cookie转换映射 - 域名特有Cookie的对应关系
                'mapping': {
                    # kuaishou.com -> kwaixiaodian.com
                    'kuaishou.web.cp.api_ph': 'kuaishou.shop.b_ph',
                    'kuaishou.web.cp.api_st': 'kuaishou.shop.b_st',
                    'kuaishou.web.st': 'kuaishou.shop.st',
                    'kuaishou.web.ph': 'kuaishou.shop.ph',

                    # kwaixiaodian.com -> kuaishou.com
                    'kuaishou.shop.b_ph': 'kuaishou.web.cp.api_ph',
                    'kuaishou.shop.b_st': 'kuaishou.web.cp.api_st',
                    'kuaishou.shop.st': 'kuaishou.web.st',
                    'kuaishou.shop.ph': 'kuaishou.web.ph',
                    'sid': 'kuaishou.web.cp.api_st'  # sid可以映射为api_st
                }
            }

            # 确定目标域名（去掉前导点）
            clean_target_domain = target_domain.lstrip('.')

            for cookie in cookies:
                if isinstance(cookie, dict) and 'name' in cookie and 'value' in cookie:
                    cookie_name = cookie['name']

                    # 1. 处理通用Cookie - 直接转换域名
                    if cookie_name in cookie_rules['universal']:
                        converted_cookie = self._create_converted_cookie(cookie, target_domain)
                        converted_cookies.append(converted_cookie)
                        print(f"🔄 转换通用Cookie: {cookie_name} -> {target_domain}")

                    # 2. 处理域名特有Cookie
                    elif cookie_name in cookie_rules['kuaishou_specific']:
                        if 'kuaishou.com' in clean_target_domain:
                            # 目标是kuaishou.com，直接使用
                            converted_cookie = self._create_converted_cookie(cookie, target_domain)
                            converted_cookies.append(converted_cookie)
                            print(f"✅ 保留kuaishou特有Cookie: {cookie_name}")
                        elif 'kwaixiaodian.com' in clean_target_domain:
                            # 目标是kwaixiaodian.com，尝试转换
                            mapped_name = cookie_rules['mapping'].get(cookie_name)
                            if mapped_name:
                                converted_cookie = self._create_converted_cookie(cookie, target_domain, mapped_name)
                                converted_cookies.append(converted_cookie)
                                print(f"🔄 转换kuaishou Cookie: {cookie_name} -> {mapped_name} (域名: {target_domain})")
                            else:
                                print(f"⚠️ 无法转换kuaishou特有Cookie: {cookie_name}")

                    elif cookie_name in cookie_rules['kwaixiaodian_specific']:
                        if 'kwaixiaodian.com' in clean_target_domain:
                            # 目标是kwaixiaodian.com，直接使用
                            converted_cookie = self._create_converted_cookie(cookie, target_domain)
                            converted_cookies.append(converted_cookie)
                            print(f"✅ 保留kwaixiaodian特有Cookie: {cookie_name}")
                        elif 'kuaishou.com' in clean_target_domain:
                            # 目标是kuaishou.com，尝试转换
                            mapped_name = cookie_rules['mapping'].get(cookie_name)
                            if mapped_name:
                                converted_cookie = self._create_converted_cookie(cookie, target_domain, mapped_name)
                                converted_cookies.append(converted_cookie)
                                print(f"🔄 转换kwaixiaodian Cookie: {cookie_name} -> {mapped_name} (域名: {target_domain})")
                            else:
                                print(f"⚠️ 无法转换kwaixiaodian特有Cookie: {cookie_name}")

                    # 3. 处理其他Cookie - 尝试直接转换
                    else:
                        converted_cookie = self._create_converted_cookie(cookie, target_domain)
                        converted_cookies.append(converted_cookie)
                        print(f"🔄 转换其他Cookie: {cookie_name} -> {target_domain}")

            print(f"🔄 双域名Cookie转换完成，原始: {len(cookies)}，转换后: {len(converted_cookies)}")
            return converted_cookies

        except Exception as e:
            print(f"❌ Cookie转换失败: {e}")
            import traceback
            traceback.print_exc()
            return cookies  # 转换失败时返回原始Cookie

    def _create_converted_cookie(self, original_cookie, target_domain, new_name=None):
        """创建转换后的Cookie"""
        converted_cookie = {
            'name': new_name or original_cookie['name'],
            'value': original_cookie['value'],
            'domain': target_domain,
            'path': original_cookie.get('path', '/'),
        }

        # 保留其他属性
        for attr in ['secure', 'httpOnly', 'sameSite', 'expiry']:
            if attr in original_cookie:
                converted_cookie[attr] = original_cookie[attr]

        return converted_cookie

    def _convert_cookies_for_domain(self, cookies, target_domain):
        """智能转换Cookie域名，只转换通用Cookie（保持向后兼容）"""
        try:
            print(f"🔄 开始智能转换Cookie域名到: {target_domain}")
            converted_cookies = []

            # 定义通用Cookie（可以跨域使用）
            universal_cookies = {
                'userId', 'bUserId', 'did', '_did'
            }

            # 定义域名特有Cookie映射
            domain_specific_cookies = {
                'kuaishou.com': {
                    'kuaishou.web.cp.api_ph', 'kuaishou.web.cp.api_st'
                },
                'kwaixiaodian.com': {
                    'sid', 'kuaishou.shop.b_ph', 'kuaishou.shop.b_st'
                }
            }

            for cookie in cookies:
                if isinstance(cookie, dict) and 'name' in cookie and 'value' in cookie:
                    cookie_name = cookie['name']

                    # 只转换通用Cookie
                    if cookie_name in universal_cookies:
                        # 创建转换后的Cookie
                        converted_cookie = {
                            'name': cookie['name'],
                            'value': cookie['value'],
                            'domain': f".{target_domain}",  # 转换域名
                            'path': cookie.get('path', '/'),
                        }

                        # 保留其他属性
                        if 'secure' in cookie:
                            converted_cookie['secure'] = cookie['secure']
                        if 'httpOnly' in cookie:
                            converted_cookie['httpOnly'] = cookie['httpOnly']
                        if 'sameSite' in cookie:
                            converted_cookie['sameSite'] = cookie['sameSite']
                        if 'expiry' in cookie:
                            converted_cookie['expiry'] = cookie['expiry']

                        converted_cookies.append(converted_cookie)
                        print(f"🔄 转换通用Cookie: {cookie['name']} -> 域名: {target_domain}")
                    else:
                        print(f"⚠️ 跳过域名特有Cookie: {cookie['name']} (不适用于 {target_domain})")

            print(f"✅ 智能Cookie转换完成: {len(converted_cookies)}/{len(cookies)} (只转换通用Cookie)")
            return converted_cookies

        except Exception as e:
            print(f"❌ Cookie域名转换失败: {e}")
            return []  # 返回空列表，避免注入无效Cookie

    def _merge_cookies_for_domain(self, account, target_domain):
        """合并两套Cookie，获取适用于目标域名的完整Cookie"""
        try:
            print(f"🔄 开始为域名 {target_domain} 合并Cookie...")

            # 获取创作者中心Cookie
            creator_cookie_file = self._get_cookie_file_path(account)
            creator_cookies = []
            if os.path.exists(creator_cookie_file):
                creator_cookies = self._load_cookies_from_file(creator_cookie_file)
                print(f"📁 加载创作者中心Cookie: {len(creator_cookies)}个")

            # 获取选品中心Cookie
            selection_cookie_file = self._get_selection_cookie_file_path(account)
            selection_cookies = []
            if os.path.exists(selection_cookie_file):
                selection_cookies = self._load_cookies_from_file(selection_cookie_file)
                print(f"📁 加载选品中心Cookie: {len(selection_cookies)}个")

            # 根据目标域名选择主要Cookie和补充Cookie
            if target_domain == "kuaishou.com":
                primary_cookies = creator_cookies
                secondary_cookies = self._convert_cookies_for_domain(selection_cookies, target_domain)
                print("🎯 目标: kuaishou.com，使用创作者Cookie + 转换的选品Cookie")
            elif target_domain == "kwaixiaodian.com":
                primary_cookies = selection_cookies
                secondary_cookies = self._convert_cookies_for_domain(creator_cookies, target_domain)
                print("🎯 目标: kwaixiaodian.com，使用选品Cookie + 转换的创作者Cookie")
            else:
                print(f"⚠️ 未知域名: {target_domain}，使用创作者Cookie")
                primary_cookies = creator_cookies
                secondary_cookies = []

            # 合并Cookie（主要Cookie优先，避免重复）
            merged_cookies = []
            cookie_names = set()

            # 先添加主要Cookie
            for cookie in primary_cookies:
                if isinstance(cookie, dict) and 'name' in cookie:
                    merged_cookies.append(cookie)
                    cookie_names.add(cookie['name'])

            # 再添加补充Cookie（避免重复）
            for cookie in secondary_cookies:
                if isinstance(cookie, dict) and 'name' in cookie and cookie['name'] not in cookie_names:
                    merged_cookies.append(cookie)
                    cookie_names.add(cookie['name'])

            print(f"✅ Cookie合并完成: {len(merged_cookies)}个 (主要: {len(primary_cookies)}, 补充: {len(secondary_cookies)})")
            return merged_cookies

        except Exception as e:
            print(f"❌ Cookie合并失败: {e}")
            # 失败时返回原始Cookie
            cookie_file = self._get_cookie_file_path(account)
            if os.path.exists(cookie_file):
                return self._load_cookies_from_file(cookie_file)
            return []

    def _fallback_to_creator_cookies(self, account, driver):
        """回退到使用创作者中心Cookie"""
        try:
            print("🔄 回退使用创作者中心Cookie...")
            cookie_file_path = self._get_cookie_file_path(account)
            cookies = self._load_cookies_from_file(cookie_file_path)
            if cookies:
                self._inject_cookies(driver, cookies)
                print("✅ 创作者中心Cookie注入成功")
            else:
                print("❌ 创作者中心Cookie也不存在")
        except Exception as e:
            print(f"❌ 回退Cookie注入失败: {e}")

    def _check_login_success(self, driver):
        """检查登录是否成功，增强错误处理和会话管理"""
        try:
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.common.exceptions import InvalidSessionIdException, WebDriverException

            # 首先检查WebDriver会话是否有效
            try:
                # 尝试获取当前URL来验证会话有效性
                current_url = driver.current_url
                print(f"🔍 检查登录状态，当前URL: {current_url}")
            except InvalidSessionIdException:
                print("❌ WebDriver会话已断开，无法验证登录状态")
                return False
            except WebDriverException as e:
                if "invalid session id" in str(e).lower():
                    print("❌ WebDriver会话无效，无法验证登录状态")
                    return False
                else:
                    print(f"❌ WebDriver错误: {e}")
                    return False

            # 如果URL包含login，说明跳转到了登录页面，登录失败
            if "login" in current_url.lower():
                print("❌ 页面跳转到登录页面，登录失败")
                return False

            # 如果不在快手创作者中心域名，登录失败
            if "cp.kuaishou.com" not in current_url:
                print(f"❌ 不在创作者中心域名: {current_url}")
                return False

            # 检查页面标题或特定元素来确认登录状态
            wait = WebDriverWait(driver, 10)
            try:
                # 检查页面标题
                page_title = driver.title
                print(f"🔍 页面标题: {page_title}")

                # 如果标题包含登录相关字样，说明未登录
                if any(keyword in page_title.lower() for keyword in ["登录", "login", "sign in"]):
                    print("❌ 页面标题显示未登录")
                    return False

                # 检查页面内容来判断登录状态
                page_source = driver.page_source

                # 如果页面包含明确的登录提示，说明未登录
                login_indicators = ["请登录", "立即登录", "登录快手", "sign in", "log in"]
                if any(indicator in page_source for indicator in login_indicators):
                    print("❌ 页面内容显示需要登录")
                    return False

                # 如果页面包含用户相关信息，说明已登录
                user_indicators = ["个人资料", "创作者", "数据概览", "作品管理", "粉丝", "profile"]
                if any(indicator in page_source for indicator in user_indicators):
                    print("✅ 页面内容显示已登录")
                    return True

                # 如果都没有明确指示，但URL正确且不包含login，认为登录成功
                print("⚠️ 无法明确判断登录状态，但URL检查通过")
                return True

            except Exception as e:
                print(f"⚠️ 元素检查异常: {e}")
                # 如果元素检查失败，但URL正确且不包含login，认为登录成功
                return "profile" in current_url and "login" not in current_url.lower()

        except Exception as e:
            print(f"❌ 检查登录状态失败: {e}")
            return False

    def _update_account_status(self, account, status):
        """更新账号状态"""
        try:
            # 在主线程中更新UI
            QTimer.singleShot(0, lambda: self._do_update_account_status(account, status))
        except Exception as e:
            print(f"❌ 更新账号状态失败: {e}")

    def _do_update_account_status(self, account, status):
        """在主线程中更新账号状态"""
        try:
            for acc in self.accounts_data:
                if acc['username'] == account['username']:
                    acc['status'] = status
                    if status == "在线":
                        acc['last_login'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    break
            self.refresh_table()
        except Exception as e:
            print(f"❌ 更新UI状态失败: {e}")

    def _show_login_progress(self, message):
        """显示登录进度（线程安全）"""
        print(f"🔄 {message}")
        # 可以在这里添加状态栏更新或进度对话框

    def _show_login_success(self, message):
        """显示登录成功消息（线程安全）"""
        print(f"✅ {message}")
        QTimer.singleShot(0, lambda: QMessageBox.information(self, "登录成功", message))

    def _show_login_error(self, message):
        """显示登录错误消息（线程安全）"""
        print(f"❌ {message}")
        QTimer.singleShot(0, lambda: QMessageBox.critical(self, "登录失败", message))


class AccountDialog(QDialog):
    """
    账号编辑对话框

    用于添加和编辑账号信息的对话框，支持自动化登录和Cookie管理。
    """

    # 定义信号用于线程间通信
    show_cookie_dialog_signal = pyqtSignal(list, str, object)  # cookies, phone, driver

    def __init__(self, parent=None, account_data=None):
        """
        初始化对话框

        Args:
            parent: 父组件
            account_data: 账号数据，None表示添加新账号
        """
        super().__init__(parent)
        self.account_data = account_data
        self.is_edit_mode = account_data is not None
        self.automation_thread = None
        self.init_ui()
        
    def init_ui(self):
        """初始化对话框界面"""
        self.setWindowTitle("编辑账号" if self.is_edit_mode else "添加账号")
        self.setModal(True)
        self.resize(500, 450)

        layout = QVBoxLayout(self)

        # 创建表单
        form_layout = QFormLayout()

        # 用户名（可选）
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("可选，留空时使用手机号作为用户名")
        if self.is_edit_mode:
            self.username_edit.setText(self.account_data["username"])
            self.username_edit.setEnabled(False)  # 编辑模式下用户名不可修改
        form_layout.addRow("用户名:", self.username_edit)

        # 昵称（可选）
        self.nickname_edit = QLineEdit()
        self.nickname_edit.setPlaceholderText("可选，留空时使用用户名作为昵称")
        if self.is_edit_mode:
            self.nickname_edit.setText(self.account_data["nickname"])
        form_layout.addRow("昵称:", self.nickname_edit)

        # 手机号（必填）
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("必填，用于Cookie文件命名和账号标识")
        if self.is_edit_mode and "phone" in self.account_data:
            self.phone_edit.setText(self.account_data["phone"])
        form_layout.addRow("手机号 *:", self.phone_edit)

        # 邮箱（可选）
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("可选，用于账号验证")
        if self.is_edit_mode and "email" in self.account_data:
            self.email_edit.setText(self.account_data.get("email", ""))
        form_layout.addRow("邮箱:", self.email_edit)

        # 备注
        self.remarks_edit = QTextEdit()
        self.remarks_edit.setMaximumHeight(80)
        self.remarks_edit.setPlaceholderText("可选，账号相关备注信息")
        if self.is_edit_mode:
            self.remarks_edit.setText(self.account_data.get("remarks", ""))
        form_layout.addRow("备注:", self.remarks_edit)

        layout.addLayout(form_layout)

        # 自动化登录区域（仅在添加模式下显示）
        if not self.is_edit_mode:
            self.create_automation_section(layout)

        # 按钮区域
        self.create_button_section(layout)
        
    def get_account_data(self):
        """获取表单数据"""
        username = self.username_edit.text().strip()
        nickname = self.nickname_edit.text().strip()
        phone = self.phone_edit.text().strip()
        email = self.email_edit.text().strip()
        remarks = self.remarks_edit.toPlainText().strip()

        # 如果用户名为空，使用手机号作为用户名
        if not username:
            username = phone

        # 如果昵称为空，使用用户名
        if not nickname:
            nickname = username

        data = {
            "username": username,
            "nickname": nickname,
            "phone": phone,
            "email": email if email else "",  # 邮箱可以为空
            "remarks": remarks if remarks else ""  # 备注可以为空
        }

        return data

    def accept(self):
        """验证并接受对话框"""
        phone = self.phone_edit.text().strip()

        # 验证必填字段（只有手机号是必填的）
        if not phone:
            QMessageBox.warning(self, "输入错误", "手机号不能为空！")
            self.phone_edit.setFocus()
            return

        # 验证用户名格式（如果填写了）
        username = self.username_edit.text().strip()
        if username and len(username) < 3:
            QMessageBox.warning(self, "输入错误", "用户名长度不能少于3个字符！")
            self.username_edit.setFocus()
            return

        super().accept()

    def create_automation_section(self, parent_layout):
        """创建自动化登录区域"""
        # 分组框
        automation_group = QGroupBox("自动化登录设置")
        automation_layout = QVBoxLayout(automation_group)

        # 说明文本
        info_label = QLabel(
            "添加账号后可选择进行自动化登录配置，系统将：\n"
            "1. 自动打开快手登录页面\n"
            "2. 等待您手动完成登录\n"
            "3. 自动提取并保存Cookie文件"
        )
        info_label.setStyleSheet("color: #888888; font-size: 11px;")
        automation_layout.addWidget(info_label)

        # 自动化选项
        self.enable_automation_checkbox = QCheckBox("添加账号后立即进行自动化登录配置")
        self.enable_automation_checkbox.setChecked(True)
        automation_layout.addWidget(self.enable_automation_checkbox)

        # 进度显示区域
        self.progress_label = QLabel("")
        self.progress_label.setStyleSheet("color: #FF6B35; font-weight: bold;")
        self.progress_label.hide()
        automation_layout.addWidget(self.progress_label)

        parent_layout.addWidget(automation_group)

    def create_button_section(self, parent_layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()

        # 标准按钮
        self.ok_button = QPushButton("确认添加" if not self.is_edit_mode else "确认修改")
        self.cancel_button = QPushButton("取消")

        # 连接信号
        self.ok_button.clicked.connect(self.handle_ok_clicked)
        self.cancel_button.clicked.connect(self.reject)

        button_layout.addStretch()
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.ok_button)

        parent_layout.addLayout(button_layout)

        # 连接信号
        self.show_cookie_dialog_signal.connect(self._handle_cookie_dialog_signal)


    def handle_ok_clicked(self):
        """处理确认按钮点击"""
        # 验证输入
        if not self.validate_input():
            return

        if self.is_edit_mode:
            # 编辑模式直接保存
            self.accept()
        else:
            # 添加模式，检查是否需要自动化登录
            if hasattr(self, 'enable_automation_checkbox') and self.enable_automation_checkbox.isChecked():
                self.start_automation_login()
            else:
                self.accept()

    def validate_input(self):
        """验证输入数据"""
        phone = self.phone_edit.text().strip()

        # 验证必填字段（只有手机号是必填的）
        if not phone:
            QMessageBox.warning(self, "输入错误", "手机号不能为空！")
            self.phone_edit.setFocus()
            return False

        # 验证用户名格式（如果填写了）
        username = self.username_edit.text().strip()
        if username and len(username) < 3:
            QMessageBox.warning(self, "输入错误", "用户名长度不能少于3个字符！")
            self.username_edit.setFocus()
            return False

        # 验证邮箱格式（如果填写了）
        email = self.email_edit.text().strip()
        if email:
            import re
            if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
                QMessageBox.warning(self, "输入错误", "请输入有效的邮箱地址！")
                self.email_edit.setFocus()
                return False

        return True

    def start_automation_login(self):
        """启动自动化登录流程"""
        try:
            phone = self.phone_edit.text().strip()

            # 禁用按钮防止重复点击
            self.ok_button.setEnabled(False)
            self.ok_button.setText("登录中...")

            # 显示进度
            self.progress_label.show()
            self.progress_label.setText("正在启动浏览器...")

            # 在新线程中启动自动化登录
            self.automation_thread = threading.Thread(
                target=self._automation_worker,
                args=(phone,),
                daemon=True
            )
            self.automation_thread.start()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动自动化登录失败：{e}")
            self.reset_ui_state()

    def _automation_worker(self, phone):
        """自动化工作线程"""
        try:
            # 导入selenium相关模块
            from selenium import webdriver
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.webdriver.chrome.options import Options
            from selenium.common.exceptions import TimeoutException

            # 更新进度
            self._update_progress("正在配置浏览器...")

            # 设置Chrome选项
            options = Options()
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            options.add_argument("--start-maximized")

            # 设置User-Agent
            user_agent = ("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
                         "(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
            options.add_argument(f"--user-agent={user_agent}")

            # 创建WebDriver（使用系统环境中的驱动）
            self._update_progress("正在启动浏览器...")
            try:
                # 直接使用系统环境中的chromedriver
                driver = webdriver.Chrome(options=options)
            except Exception as e:
                # 如果系统环境中没有chromedriver，尝试其他浏览器
                self._update_progress("Chrome驱动未找到，尝试其他浏览器...")
                try:
                    # 尝试Firefox
                    from selenium.webdriver.firefox.options import Options as FirefoxOptions
                    firefox_options = FirefoxOptions()
                    firefox_options.add_argument("--start-maximized")
                    driver = webdriver.Firefox(options=firefox_options)
                except Exception:
                    try:
                        # 尝试Edge
                        from selenium.webdriver.edge.options import Options as EdgeOptions
                        edge_options = EdgeOptions()
                        edge_options.add_argument("--start-maximized")
                        driver = webdriver.Edge(options=edge_options)
                    except Exception:
                        raise Exception("未找到可用的浏览器驱动！请确保已安装Chrome、Firefox或Edge的WebDriver")

            # 执行反检测脚本
            driver.execute_script(
                "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})"
            )

            # 导航到创作者中心登录页面（获取创作者中心Cookie）
            self._update_progress("正在打开创作者中心登录页面...")
            # 直接使用创作者中心的登录页面，这样获取的Cookie更适用
            driver.get("https://passport.kuaishou.com/pc/account/login/?sid=kuaishou.web.cp.api&callback=https%3A%2F%2Fcp.kuaishou.com%2Frest%2Finfra%2Fsts%3FfollowUrl%3Dhttps%253A%252F%252Fcp.kuaishou.com%252Fprofile%26setRootDomain%3Dtrue")

            # 等待用户手动登录
            self._update_progress("请在浏览器中完成登录操作...")

            # 等待登录成功元素出现
            self._update_progress("正在检测登录状态...")
            wait = WebDriverWait(driver, 300)  # 5分钟超时

            # 检测创作者中心登录成功
            success_element = None
            login_success = False

            try:
                self._update_progress("检测创作者中心登录状态...")

                # 监控登录状态
                max_wait_time = 300  # 5分钟
                check_interval = 3   # 每3秒检查一次

                for i in range(0, max_wait_time, check_interval):
                    import time
                    time.sleep(check_interval)

                    try:
                        current_url = driver.current_url
                        self._update_progress(f"当前URL: {current_url}")

                        # 检查是否登录成功并跳转到创作者中心
                        if "cp.kuaishou.com" in current_url and "profile" in current_url:
                            # 检查页面内容确认登录成功
                            page_source = driver.page_source
                            if "创作者" in page_source or "个人资料" in page_source or "数据概览" in page_source:
                                self._update_progress("✅ 检测到创作者中心登录成功！")
                                login_success = True
                                success_element = True
                                break
                            elif "登录" in page_source or "请登录" in page_source:
                                self._update_progress("⚠️ 仍在登录页面，继续等待...")
                                continue
                        elif "login" not in current_url.lower() and "passport" not in current_url.lower():
                            # 可能已经登录但还没跳转，检查页面内容
                            page_source = driver.page_source
                            if any(keyword in page_source for keyword in ["用户", "头像", "个人", "设置", "退出"]):
                                self._update_progress("✅ 检测到登录成功，等待跳转...")
                                # 继续等待跳转到创作者中心
                                continue

                    except Exception as e:
                        self._update_progress(f"检测过程中出现错误: {e}")
                        continue

                    self._update_progress(f"等待登录中... ({i+check_interval}/{max_wait_time}秒)")

                if not login_success:
                    self._update_progress("⚠️ 登录检测超时，请确认是否已经登录成功")
                    # 超时后显示手动确认对话框
                    self._show_manual_confirmation_dialog(driver, phone)
                    return

            except Exception as e:
                self._update_progress(f"⚠️ 检测过程中出现错误: {e}")
                # 出错后显示手动确认对话框
                self._show_manual_confirmation_dialog(driver, phone)
                return

            if login_success:
                # 登录成功，自动获取两个域名的Cookie
                self._update_progress("登录成功！正在获取创作者中心Cookie...")

                # 确保在创作者中心页面
                if "cp.kuaishou.com" not in driver.current_url:
                    driver.get("https://cp.kuaishou.com/profile")
                    import time
                    time.sleep(3)

                # 获取创作者中心Cookie
                creator_cookies = driver.get_cookies()
                self._update_progress(f"获取到创作者中心Cookie: {len(creator_cookies)}个")

                # 尝试访问选品中心获取选品Cookie
                self._update_progress("正在尝试访问选品中心...")
                driver.get("https://cps.kwaixiaodian.com/pc/promoter/selection-center/home")
                time.sleep(8)

                # 检查选品中心是否需要单独登录
                current_url = driver.current_url
                page_source = driver.page_source

                if "login" in current_url.lower() or "登录" in page_source:
                    self._update_progress("⚠️ 选品中心需要单独登录")
                    self._update_progress("💡 请在浏览器中手动登录选品中心")
                    self._update_progress("🔄 等待选品中心登录...")

                    # 等待用户手动登录选品中心
                    selection_login_success = False
                    max_wait_time = 180  # 3分钟
                    check_interval = 5   # 每5秒检查一次

                    for i in range(0, max_wait_time, check_interval):
                        time.sleep(check_interval)
                        try:
                            current_url = driver.current_url
                            page_source = driver.page_source

                            # 检查是否登录成功
                            if ("login" not in current_url.lower() and
                                "cps.kwaixiaodian.com" in current_url and
                                ("选品中心" in page_source or "商品" in page_source)):
                                self._update_progress("✅ 选品中心登录成功！")
                                selection_login_success = True
                                break

                        except Exception as e:
                            continue

                        self._update_progress(f"等待选品中心登录... ({i+check_interval}/{max_wait_time}秒)")

                    if not selection_login_success:
                        self._update_progress("⚠️ 选品中心登录超时，将只保存创作者中心Cookie")
                        all_cookies = creator_cookies
                    else:
                        # 获取选品中心Cookie
                        selection_cookies = driver.get_cookies()
                        self._update_progress(f"获取到选品中心Cookie: {len(selection_cookies)}个")

                        # 保存选品中心Cookie到专门文件
                        try:
                            selection_cookie_file = os.path.join(os.path.dirname(__file__), '..', '..', 'cookies', f"{phone}_selection_cookies.json")
                            os.makedirs(os.path.dirname(selection_cookie_file), exist_ok=True)

                            import json
                            with open(selection_cookie_file, 'w', encoding='utf-8') as f:
                                json.dump(selection_cookies, f, ensure_ascii=False, indent=2)

                            self._update_progress(f"✅ 选品中心Cookie已保存")
                        except Exception as e:
                            self._update_progress(f"保存选品中心Cookie失败: {e}")

                        # 创建双域名通用Cookie
                        self._update_progress("正在创建双域名通用Cookie...")
                        all_cookies = self._create_universal_cookies(creator_cookies, selection_cookies)
                else:
                    # 选品中心直接可访问，获取Cookie
                    selection_cookies = driver.get_cookies()
                    self._update_progress(f"✅ 直接获取到选品中心Cookie: {len(selection_cookies)}个")

                    # 保存选品中心Cookie
                    try:
                        selection_cookie_file = os.path.join(os.path.dirname(__file__), '..', '..', 'cookies', f"{phone}_selection_cookies.json")
                        os.makedirs(os.path.dirname(selection_cookie_file), exist_ok=True)

                        import json
                        with open(selection_cookie_file, 'w', encoding='utf-8') as f:
                            json.dump(selection_cookies, f, ensure_ascii=False, indent=2)

                        self._update_progress(f"✅ 选品中心Cookie已保存")
                    except Exception as e:
                        self._update_progress(f"保存选品中心Cookie失败: {e}")

                    # 创建双域名通用Cookie
                    self._update_progress("正在创建双域名通用Cookie...")
                    all_cookies = self._create_universal_cookies(creator_cookies, selection_cookies)

                # 再次确认登录状态
                try:
                    refresh_wait = WebDriverWait(driver, 10)
                    refresh_wait.until(
                        EC.presence_of_element_located((By.XPATH, "/html/body/div[1]/div[1]/header/section[2]/div[4]/span/div[2]/div[2]"))
                    )
                    self._update_progress("页面刷新完成，登录状态确认，Cookie已更新")
                except:
                    self._update_progress("页面刷新完成")

                # 询问是否保存Cookie（使用合并后的Cookie）
                self._show_save_cookie_dialog_with_cookies(driver, phone, all_cookies)

        except TimeoutException:
            self._show_error("登录超时，请重试")
            # 超时时关闭浏览器
            try:
                if 'driver' in locals():
                    driver.quit()
            except:
                pass
        except Exception as e:
            self._show_error(f"自动化登录失败: {e}")
            # 异常时关闭浏览器
            try:
                if 'driver' in locals():
                    driver.quit()
            except:
                pass
        finally:
            # 重置UI状态，但不关闭浏览器
            self._reset_ui_state()

    def _update_progress(self, message):
        """更新进度信息（线程安全）"""
        print(f"🔄 进度更新: {message}")  # 添加控制台输出用于调试
        if hasattr(self, 'progress_label'):
            # 使用Qt的线程安全方式更新UI
            QTimer.singleShot(0, lambda: self.progress_label.setText(message))
            # 强制刷新UI
            QTimer.singleShot(0, lambda: self.progress_label.repaint())

    def _show_save_cookie_dialog(self, driver, phone):
        """显示保存Cookie对话框"""
        try:
            self._update_progress("正在提取Cookie...")
            print("🍪 开始提取Cookie...")

            # 提取Cookie
            cookies = driver.get_cookies()
            print(f"🍪 成功提取到 {len(cookies)} 个Cookie")
            self._update_progress(f"成功提取到 {len(cookies)} 个Cookie")

            # 通过信号在主线程中显示对话框
            print("🍪 准备显示保存Cookie对话框...")
            self._update_progress("准备显示保存Cookie对话框...")

            # 发射信号到主线程
            self.show_cookie_dialog_signal.emit(cookies, phone, driver)

        except Exception as e:
            print(f"❌ 提取Cookie失败: {e}")
            self._show_error(f"提取Cookie失败: {e}")

    def _show_save_cookie_dialog_with_cookies(self, driver, phone, cookies):
        """显示保存Cookie对话框（使用预先获取的Cookie）"""
        try:
            print(f"🍪 使用预先获取的 {len(cookies)} 个Cookie")
            self._update_progress(f"使用预先获取的 {len(cookies)} 个Cookie")

            # 通过信号在主线程中显示对话框
            print("🍪 准备显示保存Cookie对话框...")
            self._update_progress("准备显示保存Cookie对话框...")

            # 发射信号到主线程
            self.show_cookie_dialog_signal.emit(cookies, phone, driver)

        except Exception as e:
            print(f"❌ 处理Cookie失败: {e}")
            self._show_error(f"处理Cookie失败: {e}")
            # 提取失败时关闭浏览器
            try:
                driver.quit()
            except:
                pass

    def _ask_save_cookies(self, cookies, phone, driver):
        """询问是否保存Cookie"""
        try:
            print("🍪 开始创建Cookie保存对话框...")
            self._update_progress("正在创建Cookie保存对话框...")

            # 创建消息框并设置为置顶
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("登录成功")
            msg_box.setText(f"检测到登录成功！\n\n是否保存Cookie到本地文件？\n文件名: {phone if phone else 'username'}.txt")
            msg_box.setStandardButtons(QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            msg_box.setDefaultButton(QMessageBox.StandardButton.Yes)

            # 设置窗口置顶和焦点
            msg_box.setWindowFlags(msg_box.windowFlags() | Qt.WindowType.WindowStaysOnTopHint)
            msg_box.activateWindow()
            msg_box.raise_()

            print("🍪 显示Cookie保存对话框...")
            self._update_progress("显示Cookie保存对话框...")

            # 显示对话框
            reply = msg_box.exec()
            print(f"🍪 用户选择: {reply}")

        except Exception as e:
            print(f"❌ 创建对话框失败: {e}")
            self._show_error(f"创建对话框失败: {e}")
            return

        if reply == QMessageBox.StandardButton.Yes:
            print("🍪 用户确认保存Cookie，开始保存...")
            self._save_cookies_to_file(cookies, phone)
            print("🍪 Cookie保存流程完成")

        # 用户确认后关闭浏览器
        try:
            driver.quit()
            self._update_progress("✅ 浏览器已关闭，自动化登录完成")
        except Exception as e:
            print(f"关闭浏览器时出错: {e}")

        # 完成后接受对话框
        self.accept()

    def _save_cookies_to_file(self, cookies, phone):
        """保存Cookie到文件"""
        try:
            print(f"🍪 开始保存Cookie，手机号: {phone}, Cookie数量: {len(cookies)}")

            # 获取Cookie存储路径
            try:
                from data.models.base import ConfigManager
                print("🍪 成功导入ConfigManager (相对导入)")
            except ImportError:
                from kuaishou_automation_tool.data.models.base import ConfigManager
                print("🍪 成功导入ConfigManager (绝对导入)")
            config_manager = ConfigManager()
            print("🍪 ConfigManager初始化成功")

            # 获取存储路径，默认为项目根目录下的cookies文件夹
            storage_path = config_manager.get('automation.cookie_storage_path')
            print(f"🍪 配置中的存储路径: {storage_path}")

            if not storage_path:
                project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                storage_path = os.path.join(project_root, 'cookies')
                print(f"🍪 使用默认存储路径: {storage_path}")

            # 确保目录存在
            print(f"🍪 创建目录: {storage_path}")
            os.makedirs(storage_path, exist_ok=True)
            print(f"🍪 目录创建成功")

            # 使用增强的Cookie管理器保存（支持双域名）
            try:
                from business.cookie_manager import CookieManager
                cookie_manager = CookieManager()

                # 检测当前页面域名信息
                current_domain = "kuaishou.com"  # 默认域名
                try:
                    # 这里可以从浏览器获取当前域名，但在保存时通常是kuaishou.com
                    current_domain = "kuaishou.com"
                except:
                    pass

                # 使用增强的保存方法
                file_path = cookie_manager.save_cookies(
                    phone=phone if phone else 'username',
                    cookies=cookies,
                    domain_info=current_domain
                )

                if file_path:
                    print(f"🍪 使用增强Cookie管理器保存成功: {file_path}")

                    # 获取域名支持信息
                    domain_info = cookie_manager.get_cookie_domain_info(phone if phone else 'username')
                    if domain_info:
                        supported_domains = domain_info.get('supported_domains', ['kuaishou.com'])
                        conversion_capable = domain_info.get('conversion_capable', False)

                        success_msg = f"Cookie已保存到:\n{file_path}\n\n"
                        success_msg += f"支持域名: {', '.join(supported_domains)}\n"
                        if conversion_capable:
                            success_msg += "✅ 支持智能双域名转换 (kuaishou.com ↔ kwaixiaodian.com)\n\n"
                            success_msg += "🔄 是否自动生成对应域名的Cookie？"

                            # 询问用户是否要自动转换
                            reply = QMessageBox.question(
                                self, "自动转换Cookie", success_msg,
                                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                                QMessageBox.StandardButton.Yes
                            )

                            if reply == QMessageBox.StandardButton.Yes:
                                self._auto_convert_cookies(phone if phone else 'username', domain_info)
                        else:
                            success_msg += "⚠️ 仅支持当前域名"
                            QMessageBox.information(self, "成功", success_msg)
                    else:
                        QMessageBox.information(self, "成功", f"Cookie已保存到:\n{file_path}")
                else:
                    raise Exception("Cookie管理器保存失败")

            except Exception as cookie_manager_error:
                print(f"⚠️ 增强Cookie管理器失败，使用传统方法: {cookie_manager_error}")

                # 回退到传统保存方法
                cookie_data = {
                    'phone': phone,
                    'cookies': cookies,
                    'created_time': datetime.now().isoformat(),
                    'domain': 'kuaishou.com',
                    'user_agent': ("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
                                  "(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
                }

                # 保存到文件
                filename = f"{phone if phone else 'username'}.txt"
                file_path = os.path.join(storage_path, filename)
                print(f"🍪 准备保存到文件: {file_path}")

                with open(file_path, 'w', encoding='utf-8') as f:
                    import json
                    json.dump(cookie_data, f, ensure_ascii=False, indent=2)

                print(f"🍪 文件保存成功: {file_path}")
                QMessageBox.information(self, "成功", f"Cookie已保存到:\n{file_path}")

        except Exception as e:
            print(f"❌ 保存Cookie失败: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"保存Cookie失败:\n{e}")

    def _auto_convert_cookies(self, phone: str, domain_info: dict):
        """自动转换Cookie到对应域名"""
        try:
            print(f"🔄 开始自动转换Cookie: {phone}")

            from business.cookie_manager import CookieManager
            cookie_manager = CookieManager()

            # 确定当前域名和目标域名
            current_domain = domain_info.get('primary_domain', 'kuaishou.com')

            if 'kuaishou.com' in current_domain:
                target_domain = 'kwaixiaodian.com'
                target_name = "选品中心"
            else:
                target_domain = 'kuaishou.com'
                target_name = "创作者中心"

            print(f"🎯 转换目标: {current_domain} -> {target_domain} ({target_name})")

            # 执行自动转换
            converted_file_path = cookie_manager.auto_convert_cookies_for_domain(phone, target_domain)

            if converted_file_path:
                success_msg = f"🎉 Cookie自动转换成功！\n\n"
                success_msg += f"原始Cookie: {current_domain}\n"
                success_msg += f"转换后Cookie: {target_domain} ({target_name})\n"
                success_msg += f"保存路径: {converted_file_path}\n\n"
                success_msg += f"现在您可以使用转换后的Cookie在{target_name}登录了！"

                QMessageBox.information(self, "转换成功", success_msg)
                print(f"✅ Cookie自动转换成功: {converted_file_path}")

                # 询问是否要立即测试转换后的Cookie
                test_reply = QMessageBox.question(
                    self, "测试转换后的Cookie",
                    f"是否要立即测试转换后的Cookie在{target_name}的登录效果？",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.No
                )

                if test_reply == QMessageBox.StandardButton.Yes:
                    self._test_converted_cookies(converted_file_path, target_domain, target_name)

            else:
                error_msg = f"❌ Cookie自动转换失败\n\n"
                error_msg += f"可能原因:\n"
                error_msg += f"• Cookie格式不兼容\n"
                error_msg += f"• 缺少必要的转换信息\n"
                error_msg += f"• 文件保存权限问题\n\n"
                error_msg += f"请检查日志获取详细错误信息。"

                QMessageBox.warning(self, "转换失败", error_msg)
                print(f"❌ Cookie自动转换失败")

        except Exception as e:
            error_msg = f"Cookie自动转换过程中发生错误:\n{e}"
            QMessageBox.critical(self, "转换错误", error_msg)
            print(f"❌ Cookie自动转换异常: {e}")
            import traceback
            traceback.print_exc()

    def _test_converted_cookies(self, cookie_file_path: str, target_domain: str, target_name: str):
        """测试转换后的Cookie"""
        try:
            print(f"🧪 开始测试转换后的Cookie: {target_name}")

            # 读取转换后的Cookie
            import json
            with open(cookie_file_path, 'r', encoding='utf-8') as f:
                cookie_data = json.load(f)

            cookies = cookie_data.get('cookies', [])
            if not cookies:
                QMessageBox.warning(self, "测试失败", "转换后的Cookie文件为空")
                return

            # 启动浏览器进行测试
            QMessageBox.information(
                self, "开始测试",
                f"即将启动浏览器测试{target_name}登录效果...\n\n"
                f"测试完成后请手动关闭浏览器。"
            )

            driver = self._create_browser_driver()

            # 根据目标域名访问对应页面
            if 'kuaishou.com' in target_domain:
                test_url = "https://cp.kuaishou.com/profile"
                print(f"🌐 测试创作者中心登录: {test_url}")
            else:
                test_url = "https://kwaixiaodian.com"
                print(f"🌐 测试选品中心登录: {test_url}")

            # 先访问主域名
            main_url = f"https://www.{target_domain}"
            driver.get(main_url)

            import time
            time.sleep(2)

            # 注入转换后的Cookie
            print(f"🍪 注入转换后的Cookie...")
            self._inject_cookies(driver, cookies)

            # 跳转到测试页面
            driver.get(test_url)
            time.sleep(3)

            # 检查登录状态
            if self._check_login_success(driver):
                QMessageBox.information(
                    self, "测试成功",
                    f"🎉 转换后的Cookie在{target_name}登录成功！\n\n"
                    f"您现在可以正常使用这套Cookie进行自动化操作了。"
                )
                print(f"✅ 转换后Cookie测试成功: {target_name}")
            else:
                QMessageBox.warning(
                    self, "测试结果",
                    f"⚠️ 转换后的Cookie在{target_name}登录验证未通过\n\n"
                    f"可能需要手动登录一次来激活Cookie。\n"
                    f"浏览器将保持打开状态，您可以手动登录。"
                )
                print(f"⚠️ 转换后Cookie测试未通过: {target_name}")

        except Exception as e:
            error_msg = f"测试转换后Cookie时发生错误:\n{e}"
            QMessageBox.critical(self, "测试错误", error_msg)
            print(f"❌ 测试转换后Cookie异常: {e}")

    def _show_error(self, message):
        """显示错误信息（线程安全）"""
        QTimer.singleShot(0, lambda: QMessageBox.critical(self, "错误", message))

    def _reset_ui_state(self):
        """重置UI状态（线程安全）"""
        QTimer.singleShot(0, self.reset_ui_state)

    def _handle_cookie_dialog_signal(self, cookies, phone, driver):
        """处理Cookie对话框信号（在主线程中执行）"""
        print("🍪 在主线程中处理Cookie对话框...")
        self._ask_save_cookies(cookies, phone, driver)

    def _show_manual_confirmation_dialog(self, driver, phone):
        """显示手动确认对话框"""
        QTimer.singleShot(0, lambda: self._ask_manual_confirmation(driver, phone))

    def _ask_manual_confirmation(self, driver, phone):
        """询问用户是否已经登录成功"""
        # 创建消息框并设置为置顶
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("手动确认登录状态")
        msg_box.setText("自动检测登录状态失败。\n\n"
                       "请确认：\n"
                       "1. 您是否已经在浏览器中成功登录了快手账号？\n"
                       "2. 是否已经进入了快手创作者中心页面？\n\n"
                       "如果已经登录成功，点击'是'来保存Cookie。\n"
                       "如果还没有登录，点击'否'继续等待。")
        msg_box.setStandardButtons(QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No | QMessageBox.StandardButton.Cancel)
        msg_box.setDefaultButton(QMessageBox.StandardButton.No)

        # 设置窗口置顶和焦点
        msg_box.setWindowFlags(msg_box.windowFlags() | Qt.WindowType.WindowStaysOnTopHint)
        msg_box.activateWindow()
        msg_box.raise_()

        # 显示对话框
        reply = msg_box.exec()

        if reply == QMessageBox.StandardButton.Yes:
            # 用户确认已登录，直接保存Cookie
            self._update_progress("用户确认登录成功，正在保存Cookie...")
            self._show_save_cookie_dialog(driver, phone)
        elif reply == QMessageBox.StandardButton.No:
            # 用户表示还没登录，继续等待
            self._update_progress("继续等待登录...")
            # 重新启动检测
            threading.Thread(target=self._continue_waiting_for_login, args=(driver, phone), daemon=True).start()
        else:
            # 用户取消，关闭浏览器
            self._show_error("用户取消了自动化登录")
            try:
                driver.quit()
            except:
                pass
            self._reset_ui_state()

    def _continue_waiting_for_login(self, driver, phone):
        """继续等待登录"""
        try:
            # 再次尝试检测登录状态，但时间更短
            wait = WebDriverWait(driver, 60)  # 1分钟超时

            target_selector = "/html/body/div[1]/div[1]/header/section[2]/div[4]/span/div[2]/div[2]"

            try:
                success_element = wait.until(
                    EC.presence_of_element_located((By.XPATH, target_selector))
                )
                self._update_progress("✅ 检测到登录成功元素")
                # 检测成功，保存Cookie
                self._show_save_cookie_dialog(driver, phone)
            except:
                # 再次失败，再次询问用户
                self._show_manual_confirmation_dialog(driver, phone)

        except Exception as e:
            self._show_error(f"继续等待登录时出错: {e}")
            try:
                driver.quit()
            except:
                pass
            self._reset_ui_state()

    def on_automation_progress(self, message):
        """自动化进度回调"""
        if hasattr(self, 'progress_label'):
            self.progress_label.setText(message)

    def on_automation_success(self, cookie_file_path):
        """自动化成功回调"""
        self.progress_label.setText(f"✅ 登录成功！Cookie已保存到: {os.path.basename(cookie_file_path)}")

        # 询问是否保存账号
        reply = QMessageBox.question(
            self, "登录成功",
            f"自动化登录成功！\nCookie已保存到: {cookie_file_path}\n\n是否保存账号信息？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.Yes
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.accept()
        else:
            self.reset_ui_state()

    def on_automation_error(self, error_message):
        """自动化错误回调"""
        self.progress_label.setText(f"❌ {error_message}")

        QMessageBox.warning(
            self, "自动化登录失败",
            f"自动化登录过程中出现错误：\n{error_message}\n\n您可以选择：\n1. 重试自动化登录\n2. 跳过自动化，仅保存账号信息"
        )

        self.reset_ui_state()

    def reset_ui_state(self):
        """重置UI状态"""
        self.ok_button.setEnabled(True)
        self.ok_button.setText("确认添加" if not self.is_edit_mode else "确认修改")
        if hasattr(self, 'progress_label'):
            self.progress_label.hide()

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 如果有正在运行的自动化线程，先停止
        if self.automation_thread and self.automation_thread.is_alive():
            reply = QMessageBox.question(
                self, "确认关闭",
                "自动化登录正在进行中，确定要关闭吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                # 这里可以添加停止自动化的逻辑
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()
