"""
快手无人带货工具 - 日志工具模块
Kuaishou Automation Tool - Logger Utility

本模块提供日志记录功能，包括：
- 控制台和文件日志输出
- 日志级别配置
- 日志文件轮转
- 彩色日志输出
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime


def setup_logger(name="KuaishouAutomation", log_level="INFO", log_file=None, 
                max_file_size=10*1024*1024, backup_count=5):
    """
    设置日志记录器
    
    Args:
        name (str): 日志记录器名称
        log_level (str): 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file (str, optional): 日志文件路径
        max_file_size (int): 日志文件最大大小（字节）
        backup_count (int): 备份文件数量
        
    Returns:
        logging.Logger: 配置好的日志记录器
    """
    # 创建日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, log_level.upper(), logging.INFO))
    
    # 清除现有处理器
    logger.handlers.clear()
    
    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file:
        # 确保日志目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)
            
        # 创建轮转文件处理器
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


class ColoredFormatter(logging.Formatter):
    """
    彩色日志格式化器
    
    为不同级别的日志添加颜色标识。
    """
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        """
        格式化日志记录
        
        Args:
            record: 日志记录对象
            
        Returns:
            str: 格式化后的日志字符串
        """
        # 获取原始格式化结果
        log_message = super().format(record)
        
        # 添加颜色
        level_name = record.levelname
        if level_name in self.COLORS:
            color = self.COLORS[level_name]
            reset = self.COLORS['RESET']
            log_message = f"{color}{log_message}{reset}"
            
        return log_message


def get_logger(name=None):
    """
    获取日志记录器实例
    
    Args:
        name (str, optional): 日志记录器名称
        
    Returns:
        logging.Logger: 日志记录器实例
    """
    if name is None:
        name = "KuaishouAutomation"
    return logging.getLogger(name)


def log_function_call(func):
    """
    函数调用日志装饰器
    
    Args:
        func: 被装饰的函数
        
    Returns:
        function: 装饰后的函数
    """
    def wrapper(*args, **kwargs):
        logger = get_logger()
        func_name = func.__name__
        
        # 记录函数调用
        logger.debug(f"调用函数: {func_name}")
        
        try:
            result = func(*args, **kwargs)
            logger.debug(f"函数 {func_name} 执行成功")
            return result
        except Exception as e:
            logger.error(f"函数 {func_name} 执行失败: {e}")
            raise
            
    return wrapper


def log_performance(func):
    """
    性能监控日志装饰器
    
    Args:
        func: 被装饰的函数
        
    Returns:
        function: 装饰后的函数
    """
    def wrapper(*args, **kwargs):
        import time
        logger = get_logger()
        func_name = func.__name__
        
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            end_time = time.time()
            execution_time = end_time - start_time
            
            logger.info(f"函数 {func_name} 执行时间: {execution_time:.3f}秒")
            return result
        except Exception as e:
            end_time = time.time()
            execution_time = end_time - start_time
            logger.error(f"函数 {func_name} 执行失败 (耗时: {execution_time:.3f}秒): {e}")
            raise
            
    return wrapper


class LogContext:
    """
    日志上下文管理器
    
    用于在特定代码块中添加额外的日志信息。
    """
    
    def __init__(self, logger, context_info):
        """
        初始化日志上下文
        
        Args:
            logger: 日志记录器
            context_info (str): 上下文信息
        """
        self.logger = logger
        self.context_info = context_info
        
    def __enter__(self):
        """进入上下文"""
        self.logger.info(f"开始: {self.context_info}")
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文"""
        if exc_type is None:
            self.logger.info(f"完成: {self.context_info}")
        else:
            self.logger.error(f"失败: {self.context_info} - {exc_val}")


# 创建默认日志记录器实例
default_logger = setup_logger()


# 便捷函数
def debug(message):
    """记录调试信息"""
    default_logger.debug(message)


def info(message):
    """记录一般信息"""
    default_logger.info(message)


def warning(message):
    """记录警告信息"""
    default_logger.warning(message)


def error(message):
    """记录错误信息"""
    default_logger.error(message)


def critical(message):
    """记录严重错误信息"""
    default_logger.critical(message)
