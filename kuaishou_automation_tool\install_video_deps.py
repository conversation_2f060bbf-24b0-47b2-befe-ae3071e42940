#!/usr/bin/env python3
"""
视频合成依赖库安装脚本
"""

import os
import sys
import subprocess
import platform

def run_command(command):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True,
            encoding='utf-8'
        )
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def install_package(package_name, pip_name=None):
    """安装Python包"""
    if pip_name is None:
        pip_name = package_name
    
    print(f"🔄 正在安装 {package_name}...")
    
    # 尝试使用pip安装
    success, stdout, stderr = run_command(f"pip install {pip_name}")
    
    if success:
        print(f"✅ {package_name} 安装成功")
        return True
    else:
        print(f"❌ {package_name} 安装失败: {stderr}")
        return False

def check_package(package_name, import_name=None):
    """检查包是否已安装"""
    if import_name is None:
        import_name = package_name
    
    try:
        __import__(import_name)
        print(f"✅ {package_name} 已安装")
        return True
    except ImportError:
        print(f"❌ {package_name} 未安装")
        return False

def install_video_dependencies():
    """安装视频合成相关依赖"""
    print("🎬 开始安装图文视频合成依赖库...")
    print("=" * 50)
    
    # 必需的依赖包
    dependencies = [
        # 图像处理
        ("Pillow", "Pillow", "PIL"),
        
        # 语音合成
        ("pyttsx3", "pyttsx3", "pyttsx3"),
        ("gTTS", "gTTS", "gtts"),
        
        # 视频处理
        ("moviepy", "moviepy", "moviepy.editor"),
        ("ffmpeg-python", "ffmpeg-python", "ffmpeg"),
        
        # Windows语音支持（仅Windows）
        ("pywin32", "pywin32", "win32com.client") if platform.system() == "Windows" else None,
    ]
    
    # 过滤掉None项
    dependencies = [dep for dep in dependencies if dep is not None]
    
    installed_count = 0
    total_count = len(dependencies)
    
    for package_name, pip_name, import_name in dependencies:
        print(f"\n📦 处理 {package_name}...")
        
        # 检查是否已安装
        if check_package(package_name, import_name):
            installed_count += 1
            continue
        
        # 尝试安装
        if install_package(package_name, pip_name):
            # 再次检查是否安装成功
            if check_package(package_name, import_name):
                installed_count += 1
            else:
                print(f"⚠️ {package_name} 安装后仍无法导入")
    
    print("\n" + "=" * 50)
    print(f"📊 安装结果: {installed_count}/{total_count} 个包安装成功")
    
    if installed_count == total_count:
        print("🎉 所有依赖库安装完成！")
        print("\n💡 使用说明:")
        print("1. 在主界面点击 '🎬 图文视频' 选项卡")
        print("2. 选择要合成的图片文件")
        print("3. 输入讲解文本")
        print("4. 配置视频参数")
        print("5. 点击 '🎬 生成视频' 按钮")
        return True
    else:
        print("⚠️ 部分依赖库安装失败，可能影响视频合成功能")
        print("\n🔧 手动安装建议:")
        print("pip install Pillow pyttsx3 gTTS moviepy ffmpeg-python")
        if platform.system() == "Windows":
            print("pip install pywin32")
        return False

def install_ffmpeg():
    """安装FFmpeg（如果需要）"""
    print("\n🎥 检查FFmpeg...")
    
    # 检查FFmpeg是否已安装
    success, stdout, stderr = run_command("ffmpeg -version")
    
    if success:
        print("✅ FFmpeg 已安装")
        return True
    
    print("❌ FFmpeg 未安装")
    
    system = platform.system()
    
    if system == "Windows":
        print("💡 Windows用户请手动安装FFmpeg:")
        print("1. 访问 https://ffmpeg.org/download.html")
        print("2. 下载Windows版本")
        print("3. 解压到任意目录")
        print("4. 将bin目录添加到系统PATH环境变量")
        
    elif system == "Darwin":  # macOS
        print("💡 macOS用户可使用Homebrew安装:")
        print("brew install ffmpeg")
        
    elif system == "Linux":
        print("💡 Linux用户可使用包管理器安装:")
        print("Ubuntu/Debian: sudo apt install ffmpeg")
        print("CentOS/RHEL: sudo yum install ffmpeg")
        
    return False

def main():
    """主函数"""
    print("🚀 快手无人带货工具 - 视频合成依赖安装器")
    print("=" * 60)
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        return False
    
    print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 安装Python依赖
    python_deps_ok = install_video_dependencies()
    
    # 检查FFmpeg
    ffmpeg_ok = install_ffmpeg()
    
    print("\n" + "=" * 60)
    
    if python_deps_ok:
        print("🎉 图文视频合成功能已准备就绪！")
        
        if not ffmpeg_ok:
            print("⚠️ 注意: FFmpeg未安装，可能影响某些视频格式的处理")
            print("   建议安装FFmpeg以获得最佳体验")
        
        print("\n🎬 现在可以使用图文视频合成功能了！")
        return True
    else:
        print("❌ 依赖安装不完整，请手动安装缺失的库")
        return False

if __name__ == "__main__":
    try:
        success = main()
        input("\n按回车键退出...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ 安装被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 安装过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
        sys.exit(1)
