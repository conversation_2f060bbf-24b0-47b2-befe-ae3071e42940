// qlowenergyservice.sip generated by MetaSIP
//
// This file is part of the QtBluetooth Python extension module.
//
// Copyright (c) 2022 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QLowEnergyService : QObject /NoDefaultCtors/
{
%TypeHeaderCode
#include <qlowenergyservice.h>
%End

public:
    enum DiscoveryMode
    {
        FullDiscovery,
        SkipValueDiscovery,
    };

    enum ServiceType /BaseType=Flag/
    {
        PrimaryService,
        IncludedService,
    };

    typedef QFlags<QLowEnergyService::ServiceType> ServiceTypes;

    enum ServiceError
    {
        NoError,
        OperationError,
        CharacteristicWriteError,
        DescriptorWriteError,
        CharacteristicReadError,
        DescriptorReadError,
        UnknownError,
    };

    enum ServiceState
    {
        InvalidService,
        DiscoveryRequired,
        ServiceDiscovered,
        LocalService,
        RemoteService,
        RemoteServiceDiscovering,
        RemoteServiceDiscovered,
        DiscoveringService,
    };

    enum WriteMode
    {
        WriteWithResponse,
        WriteWithoutResponse,
        WriteSigned,
    };

    virtual ~QLowEnergyService();
    QList<QBluetoothUuid> includedServices() const;
    QLowEnergyService::ServiceTypes type() const;
    QLowEnergyService::ServiceState state() const;
    QLowEnergyCharacteristic characteristic(const QBluetoothUuid &uuid) const;
    QList<QLowEnergyCharacteristic> characteristics() const;
    QBluetoothUuid serviceUuid() const;
    QString serviceName() const;
    void discoverDetails(QLowEnergyService::DiscoveryMode mode = QLowEnergyService::FullDiscovery);
    QLowEnergyService::ServiceError error() const;
    bool contains(const QLowEnergyCharacteristic &characteristic) const;
    bool contains(const QLowEnergyDescriptor &descriptor) const;
    void writeCharacteristic(const QLowEnergyCharacteristic &characteristic, const QByteArray &newValue, QLowEnergyService::WriteMode mode = QLowEnergyService::WriteWithResponse);
    void writeDescriptor(const QLowEnergyDescriptor &descriptor, const QByteArray &newValue);

signals:
    void stateChanged(QLowEnergyService::ServiceState newState);
    void characteristicChanged(const QLowEnergyCharacteristic &info, const QByteArray &value);
    void characteristicWritten(const QLowEnergyCharacteristic &info, const QByteArray &value);
    void descriptorWritten(const QLowEnergyDescriptor &info, const QByteArray &value);
    void errorOccurred(QLowEnergyService::ServiceError error);

public:
    void readCharacteristic(const QLowEnergyCharacteristic &characteristic);
    void readDescriptor(const QLowEnergyDescriptor &descriptor);

signals:
    void characteristicRead(const QLowEnergyCharacteristic &info, const QByteArray &value);
    void descriptorRead(const QLowEnergyDescriptor &info, const QByteArray &value);
};

%End
