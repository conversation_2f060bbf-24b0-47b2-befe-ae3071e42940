Metadata-Version: 2.1
Name: gTTS
Version: 2.5.4
Summary: gTTS (Google Text-to-Speech), a Python library and CLI tool to interface with Google Translate text-to-speech API
Author-email: <PERSON> <<EMAIL>>
License: MIT
Project-URL: homepage, https://github.com/pndurette/gTTS
Project-URL: documentation, https://gtts.readthedocs.io
Project-URL: repository, https://github.com/pndurette/gTTS
Project-URL: changelog, https://github.com/pndurette/gTTS/blob/main/CHANGELOG.md
Keywords: gtts,text to speech,Google Translate,TTS
Classifier: Environment :: Console
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: MacOS
Classifier: Operating System :: Unix
Classifier: Operating System :: POSIX
Classifier: Operating System :: POSIX :: Linux
Classifier: Operating System :: Microsoft :: Windows
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Software Development :: Libraries
Classifier: Topic :: Multimedia :: Sound/Audio :: Speech
Requires-Python: >=3.7
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: requests<3,>=2.27
Requires-Dist: click<8.2,>=7.1
Provides-Extra: docs
Requires-Dist: sphinx; extra == "docs"
Requires-Dist: sphinx-autobuild; extra == "docs"
Requires-Dist: sphinx-rtd-theme; extra == "docs"
Requires-Dist: sphinx-click; extra == "docs"
Requires-Dist: sphinx-mdinclude; extra == "docs"
Provides-Extra: tests
Requires-Dist: pytest<8.4.0,>=7.1.3; extra == "tests"
Requires-Dist: pytest-cov; extra == "tests"
Requires-Dist: testfixtures; extra == "tests"

# gTTS

**gTTS** (*Google Text-to-Speech*), a Python library and CLI tool to interface with Google Translate's text-to-speech API. 
Write spoken `mp3` data to a file, a file-like object (bytestring) for further audio manipulation, or `stdout`.
<https://gtts.readthedocs.io/>

[![PyPI version](https://img.shields.io/pypi/v/gTTS.svg)](https://pypi.org/project/gTTS/)
[![Python versions](https://img.shields.io/pypi/pyversions/gTTS.svg)](https://pypi.org/project/gTTS/)
[![Tests workflow](https://github.com/pndurette/gtts/actions/workflows/test.yml/badge.svg?branch=main)](https://github.com/pndurette/gTTS/actions)
[![codecov](https://codecov.io/gh/pndurette/gTTS/branch/master/graph/badge.svg)](https://codecov.io/gh/pndurette/gTTS)
[![Commits Since](https://img.shields.io/github/commits-since/pndurette/gTTS/latest.svg)](https://github.com/pndurette/gTTS/commits/)
[![PyPi Downloads](https://static.pepy.tech/badge/gtts)](http://pepy.tech/project/gtts)
[![Buy me a Coffee](https://img.shields.io/badge/buy%20me%20a-coffee-orange)](https://www.buymeacoffee.com/pndurette)

## Features

-   Customizable speech-specific sentence tokenizer that allows for unlimited lengths of text to be read, all while keeping proper intonation, abbreviations, decimals and more;
-   Customizable text pre-processors which can, for example, provide pronunciation corrections;

### Installation

    $ pip install gTTS

### Quickstart

Command Line:

    $ gtts-cli 'hello' --output hello.mp3

Module:

    >>> from gtts import gTTS
    >>> tts = gTTS('hello')
    >>> tts.save('hello.mp3')

See <https://gtts.readthedocs.io/> for documentation and examples.

### Disclaimer

This project is *not* affiliated with Google or Google Cloud. Breaking upstream changes *can* occur without notice. This project is leveraging the undocumented [Google Translate](https://translate.google.com) speech functionality and is *different* from [Google Cloud Text-to-Speech](https://cloud.google.com/text-to-speech/).

### Project

-   [Questions & community](https://github.com/pndurette/gTTS/discussions)
-   [Changelog](CHANGELOG.rst)
-   [Contributing](CONTRIBUTING.rst)

### Licence

[The MIT License (MIT)](LICENSE) Copyright © 2014-2024 Pierre Nicolas Durette & [Contributors](https://github.com/pndurette/gTTS/graphs/contributors)
