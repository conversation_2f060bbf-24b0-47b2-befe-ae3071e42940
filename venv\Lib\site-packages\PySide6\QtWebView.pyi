# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations
"""
This file contains the exact signatures for all functions in module
PySide6.QtWebView, except for defaults which are replaced by "...".

# mypy: disable-error-code="override, overload-overlap"
"""

# Module `PySide6.QtWebView`

import PySide6.QtWebView

from shiboken6 import Shiboken


class QIntList: ...


class QtWebView(Shiboken.Object):
    @staticmethod
    def initialize() -> None: ...


# eof
