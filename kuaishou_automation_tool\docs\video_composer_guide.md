# 🎬 图文视频合成功能使用指南

## 📋 功能概述

图文视频合成功能可以将多张图片和文字讲解自动合成为带货视频，支持：

- 📷 **多图片轮播**：支持JPG、PNG、BMP、WebP等格式
- 🎤 **AI语音合成**：将文字自动转换为语音讲解
- 📝 **字幕生成**：自动生成同步字幕
- 🎥 **视频输出**：生成MP4格式的成品视频
- ⚙️ **参数调节**：支持分辨率、帧率、语速等自定义

## 🚀 快速开始

### 1. 安装依赖库

首先运行依赖安装脚本：

```bash
cd kuaishou_automation_tool
python install_video_deps.py
```

或手动安装：

```bash
pip install Pillow pyttsx3 gTTS moviepy ffmpeg-python
# Windows用户额外安装
pip install pywin32
```

### 2. 使用步骤

1. **启动应用**：运行快手无人带货工具
2. **选择功能**：点击左侧导航栏的 "🎬 图文视频"
3. **选择图片**：点击 "📁 选择图片文件" 按钮，选择要使用的图片
4. **输入文本**：在文字内容区域输入讲解文本
5. **设置输出**：选择视频保存路径
6. **调整参数**：在高级设置中调整视频参数（可选）
7. **生成视频**：点击 "🎬 生成视频" 按钮

## 📝 文本编写技巧

### 带货文案示例

```
欢迎来到我们的直播间！今天为大家推荐这款超值好物。

这个产品有以下几个特点：
第一，质量优良，采用进口材料制作。
第二，价格实惠，比市场价便宜30%。
第三，性价比超高，买一送一限时优惠。

现在下单还有特别优惠，机会难得，不要错过！
点击下方链接立即购买，数量有限，先到先得！
```

### 文案编写要点

- ✅ **开场吸引**：用热情的问候吸引观众
- ✅ **产品介绍**：详细介绍产品特点和优势
- ✅ **优惠信息**：突出价格优势和限时优惠
- ✅ **行动号召**：引导观众立即购买
- ✅ **紧迫感**：营造稀缺性和紧迫感

## ⚙️ 参数设置说明

### 基础设置

| 参数 | 说明 | 建议值 |
|------|------|--------|
| 图片文件 | 支持JPG、PNG、BMP、WebP | 3-8张图片 |
| 文字内容 | 讲解文本，用于生成语音 | 100-500字 |
| 输出路径 | 视频保存位置 | 选择易找到的文件夹 |

### 高级设置

#### 视频参数
- **视频尺寸**：
  - `1080x1920` (竖屏) - 推荐，适合快手
  - `1920x1080` (横屏) - 适合横屏播放
  - `720x1280` (竖屏HD) - 较小文件
  - `1280x720` (横屏HD) - 较小文件

- **帧率 (FPS)**：15-60，推荐30
- **图片显示时间**：1-10秒，推荐3秒

#### 语音参数
- **语音速度**：0.5x-2.0x，推荐1.0x
- **语音音量**：0-100%，推荐80%

#### 字幕设置
- **启用字幕**：建议开启，提升观看体验

## 🎯 最佳实践

### 图片选择
1. **高质量图片**：分辨率至少720p
2. **产品展示**：多角度展示产品
3. **使用场景**：展示产品使用场景
4. **对比效果**：使用前后对比图

### 文案优化
1. **简洁明了**：每句话不超过20字
2. **节奏感**：适当使用标点符号控制语音节奏
3. **关键词**：突出产品卖点和优惠信息
4. **情感化**：使用感叹号增加情感色彩

### 视频优化
1. **时长控制**：建议15-60秒
2. **竖屏优先**：快手用户习惯竖屏观看
3. **清晰度**：选择合适的分辨率平衡质量和文件大小

## 🔧 故障排除

### 常见问题

**Q: 语音生成失败**
A: 检查网络连接，或安装离线语音库pyttsx3

**Q: 视频合成失败**
A: 确保安装了moviepy和ffmpeg-python库

**Q: 图片处理失败**
A: 检查图片格式是否支持，确保安装了Pillow库

**Q: 生成的视频无声音**
A: 检查文字内容是否为空，确保语音合成成功

### 依赖库问题

如果遇到依赖库问题，可以尝试：

1. **重新安装**：
   ```bash
   pip uninstall moviepy ffmpeg-python
   pip install moviepy ffmpeg-python
   ```

2. **更新pip**：
   ```bash
   python -m pip install --upgrade pip
   ```

3. **使用国内镜像**：
   ```bash
   pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ moviepy
   ```

## 📊 技术架构

### 处理流程
1. **图片预处理**：调整尺寸、格式转换
2. **语音合成**：文字转语音（TTS）
3. **字幕生成**：创建SRT字幕文件
4. **视频合成**：图片+音频+字幕合成最终视频

### 支持的TTS引擎
- **pyttsx3**：离线语音合成（推荐）
- **gTTS**：Google在线语音合成
- **Windows SAPI**：Windows系统语音（仅Windows）

### 视频处理库
- **moviepy**：主要视频处理库（推荐）
- **ffmpeg-python**：备用视频处理方案

## 🎉 使用技巧

1. **批量制作**：准备多套图片和文案，批量生成视频
2. **A/B测试**：制作不同版本的视频，测试效果
3. **素材管理**：建立图片和文案素材库
4. **定期更新**：根据季节和热点更新素材

## 📞 技术支持

如果遇到问题，请：
1. 查看控制台错误信息
2. 检查依赖库是否正确安装
3. 确认图片和文字内容格式正确
4. 尝试重新启动应用程序

---

**祝您使用愉快！🎊**
