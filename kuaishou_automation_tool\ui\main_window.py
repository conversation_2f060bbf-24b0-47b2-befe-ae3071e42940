"""
快手无人带货工具 - 主窗口模块
Kuaishou Automation Tool - Main Window Module

本模块实现主窗口的界面布局和基础功能，包括：
- 1200x800像素的主窗口
- 左侧导航栏 + 右侧内容区域布局
- 快手配色主题应用
- 响应式布局支持
"""

import sys
import os

# 尝试导入Qt库，优先使用PySide6，回退到PyQt6
try:
    from PySide6.QtWidgets import (
        QApplication, QMainWindow, QWidget, QHBoxLayout, QVBoxLayout,
        QLabel, QStackedWidget, QPushButton, QFrame
    )
    from PySide6.QtCore import Qt, QSize
    from PySide6.QtGui import QFont, QIcon
    QT_LIBRARY = "PySide6"
except ImportError:
    try:
        from PyQt6.QtWidgets import (
            QApplication, QMainWindow, QWidget, QHBoxLayout, QVBoxLayout,
            QLabel, QStackedWidget, QPushButton, QFrame
        )
        from PyQt6.QtCore import Qt, QSize
        from PyQt6.QtGui import QFont, QIcon
        QT_LIBRARY = "PyQt6"
    except ImportError as e:
        print("❌ 无法导入Qt库，请使用命令行版本: python main_cli.py")
        sys.exit(1)


class MainWindow(QMainWindow):
    """
    主窗口类
    
    实现快手无人带货工具的主界面，包括导航栏和内容区域。
    采用现代化的左侧导航栏 + 右侧内容区域布局。
    
    Attributes:
        navigation_widget (QWidget): 左侧导航栏组件
        content_widget (QStackedWidget): 右侧内容区域组件
        current_page (str): 当前显示的页面标识
    """
    
    def __init__(self):
        """初始化主窗口"""
        super().__init__()
        self.current_page = "account_management"
        self.init_ui()
        self.load_stylesheet()
        
    def init_ui(self):
        """
        初始化用户界面
        
        创建主窗口的基本布局结构：
        - 设置窗口属性（标题、尺寸等）
        - 创建左侧导航栏
        - 创建右侧内容区域
        - 设置布局管理器
        """
        # 设置窗口基本属性
        self.setWindowTitle("快手无人带货工具")
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(QSize(1000, 600))
        
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局（水平布局）
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建左侧导航栏
        self.create_navigation_bar()
        main_layout.addWidget(self.navigation_widget)
        
        # 创建右侧内容区域
        self.create_content_area()
        main_layout.addWidget(self.content_widget, 1)  # 设置拉伸因子为1
        
    def create_navigation_bar(self):
        """
        创建左侧导航栏
        
        包含以下元素：
        - 应用标题
        - 功能模块导航按钮
        - 底部信息区域
        """
        self.navigation_widget = QWidget()
        self.navigation_widget.setObjectName("navigationWidget")
        self.navigation_widget.setFixedWidth(220)
        
        # 创建导航栏布局
        nav_layout = QVBoxLayout(self.navigation_widget)
        nav_layout.setContentsMargins(0, 0, 0, 0)
        nav_layout.setSpacing(0)
        
        # 应用标题
        title_label = QLabel("快手无人带货")
        title_label.setObjectName("navigationTitle")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        nav_layout.addWidget(title_label)
        
        # 导航按钮区域
        nav_buttons_widget = QWidget()
        nav_buttons_layout = QVBoxLayout(nav_buttons_widget)
        nav_buttons_layout.setContentsMargins(0, 20, 0, 0)
        nav_buttons_layout.setSpacing(2)
        
        # 创建导航按钮
        self.nav_buttons = {}
        nav_items = [
            ("account_management", "📱 账号管理", "管理快手账号信息"),
            ("product_management", "🛍️ 商品管理", "管理带货商品信息"),
            ("live_settings", "📺 直播设置", "配置直播相关参数"),
            ("video_composer", "🎬 图文视频", "图片+文字合成带货视频"),
            ("data_statistics", "📊 数据统计", "查看运营数据统计"),
            ("system_settings", "⚙️ 系统设置", "配置系统参数")
        ]
        
        for page_id, text, tooltip in nav_items:
            button = QPushButton(text)
            button.setObjectName("navButton")
            button.setCheckable(True)
            button.setToolTip(tooltip)
            button.clicked.connect(lambda checked, pid=page_id: self.switch_page(pid))
            
            # 设置默认选中账号管理
            if page_id == "account_management":
                button.setChecked(True)
                
            self.nav_buttons[page_id] = button
            nav_buttons_layout.addWidget(button)
        
        # 添加弹性空间
        nav_buttons_layout.addStretch()
        nav_layout.addWidget(nav_buttons_widget)
        
        # 底部信息
        footer_widget = QWidget()
        footer_layout = QVBoxLayout(footer_widget)
        footer_layout.setContentsMargins(10, 10, 10, 20)
        
        version_label = QLabel("版本: v1.0.0")
        version_label.setStyleSheet("color: #888888; font-size: 10px;")
        version_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        footer_layout.addWidget(version_label)
        
        nav_layout.addWidget(footer_widget)
        
    def create_content_area(self):
        """
        创建右侧内容区域
        
        使用QStackedWidget实现多页面切换功能。
        当前包含账号管理页面，为其他功能预留接口。
        """
        self.content_widget = QStackedWidget()
        self.content_widget.setObjectName("contentWidget")
        
        # 创建各个功能页面
        self.create_account_management_page()
        self.create_placeholder_pages()
        
    def create_account_management_page(self):
        """
        创建账号管理页面

        使用实际的AccountWidget组件，提供完整的账号管理功能。
        """
        try:
            # 导入AccountWidget组件
            from .components.account_widget import AccountWidget

            # 创建AccountWidget实例
            self.account_widget = AccountWidget()

            # 添加到内容区域
            self.content_widget.addWidget(self.account_widget)

            print("✅ 账号管理页面创建成功")

        except ImportError as e:
            print(f"❌ 无法导入AccountWidget: {e}")
            # 如果导入失败，创建占位页面
            self.create_account_placeholder_page()
        except Exception as e:
            print(f"❌ 创建账号管理页面失败: {e}")
            # 如果创建失败，创建占位页面
            self.create_account_placeholder_page()

    def create_account_placeholder_page(self):
        """创建账号管理占位页面"""
        account_page = QWidget()
        layout = QVBoxLayout(account_page)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)

        # 页面标题
        title = QLabel("账号管理")
        title.setObjectName("pageTitle")
        layout.addWidget(title)

        # 错误信息
        error_label = QLabel("⚠️ 账号管理组件加载失败，请检查相关模块")
        error_label.setStyleSheet("color: #ff6b35; font-size: 14px; margin-bottom: 20px;")
        layout.addWidget(error_label)

        # 占位符内容
        placeholder = QLabel("📋 账号管理功能暂时不可用")
        placeholder.setStyleSheet("""
            background-color: #2d2d2d;
            border: 2px dashed #4d4d4d;
            border-radius: 10px;
            padding: 40px;
            font-size: 16px;
            color: #888888;
        """)
        placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
        placeholder.setMinimumHeight(300)
        layout.addWidget(placeholder)

        # 添加到内容区域
        self.content_widget.addWidget(account_page)
        
    def create_placeholder_pages(self):
        """
        创建其他功能模块的占位页面

        为后续功能开发预留接口，当前显示占位符内容。
        """
        # 商品管理占位页面
        self.create_placeholder_page("商品管理", "🛍️", "商品信息管理功能正在开发中...")

        # 直播设置占位页面
        self.create_placeholder_page("直播设置", "📺", "直播参数配置功能正在开发中...")

        # 视频合成页面（实际功能）
        self.create_video_composer_page()

        # 数据统计占位页面
        self.create_placeholder_page("数据统计", "📊", "数据统计分析功能正在开发中...")

        # 系统设置页面（实际功能）
        self.create_system_settings_page()

    def create_placeholder_page(self, title, icon, message):
        """创建单个占位页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)

        # 页面标题
        page_title = QLabel(title)
        page_title.setObjectName("pageTitle")
        layout.addWidget(page_title)

        # 占位符内容
        placeholder = QWidget()
        placeholder_layout = QVBoxLayout(placeholder)

        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 64px;")
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        message_label = QLabel(message)
        message_label.setStyleSheet("font-size: 16px; color: #888888;")
        message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        placeholder_layout.addStretch()
        placeholder_layout.addWidget(icon_label)
        placeholder_layout.addWidget(message_label)
        placeholder_layout.addStretch()

        layout.addWidget(placeholder)
        self.content_widget.addWidget(page)

    def create_video_composer_page(self):
        """创建图文视频合成页面"""
        try:
            # 尝试导入视频合成组件
            try:
                from .components.video_composer_widget import VideoComposerWidget
            except ImportError:
                from ui.components.video_composer_widget import VideoComposerWidget

            video_composer_widget = VideoComposerWidget()
            self.content_widget.addWidget(video_composer_widget)
            print("✅ 图文视频合成页面创建成功")

        except ImportError as e:
            print(f"❌ 无法导入VideoComposerWidget: {e}")
            # 如果导入失败，创建占位页面
            self.create_placeholder_page("图文视频", "🎬", f"图文视频合成模块加载失败: {e}")
        except Exception as e:
            print(f"❌ 创建图文视频合成页面失败: {e}")
            # 如果创建失败，创建占位页面
            self.create_placeholder_page("图文视频", "🎬", f"图文视频合成页面创建失败: {e}")

    def create_system_settings_page(self):
        """创建系统设置页面"""
        try:
            # 尝试相对导入
            try:
                from .components.settings_widget import SettingsWidget
            except ImportError:
                # 回退到绝对导入
                from ui.components.settings_widget import SettingsWidget

            settings_widget = SettingsWidget()
            self.content_widget.addWidget(settings_widget)
        except ImportError as e:
            # 如果导入失败，创建占位页面
            self.create_placeholder_page("系统设置", "⚙️", f"系统设置模块加载失败: {e}")
            
    def switch_page(self, page_id):
        """
        切换页面显示
        
        Args:
            page_id (str): 页面标识符
        """
        # 更新按钮状态
        for btn_id, button in self.nav_buttons.items():
            button.setChecked(btn_id == page_id)
        
        # 切换页面
        page_index = {
            "account_management": 0,
            "product_management": 1,
            "live_settings": 2,
            "video_composer": 3,
            "data_statistics": 4,
            "system_settings": 5
        }.get(page_id, 0)
        
        self.content_widget.setCurrentIndex(page_index)
        self.current_page = page_id
        
    def load_stylesheet(self):
        """
        加载样式表
        
        从QSS文件加载快手主题样式。
        """
        try:
            # 获取样式文件路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            style_path = os.path.join(current_dir, "styles", "kuaishou_theme.qss")
            
            # 读取并应用样式
            with open(style_path, 'r', encoding='utf-8') as file:
                stylesheet = file.read()
                self.setStyleSheet(stylesheet)
                
        except FileNotFoundError:
            print(f"警告: 样式文件未找到: {style_path}")
        except Exception as e:
            print(f"错误: 加载样式文件失败: {e}")
            
    def closeEvent(self, event):
        """
        窗口关闭事件处理
        
        Args:
            event: 关闭事件对象
        """
        # 这里可以添加关闭前的清理工作
        print("应用程序正在关闭...")
        event.accept()


def main():
    """
    主函数 - 应用程序入口点
    
    创建QApplication实例和主窗口，启动事件循环。
    """
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("快手无人带货工具")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Kuaishou Automation")
    
    # 创建并显示主窗口
    window = MainWindow()
    window.show()
    
    # 启动事件循环
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
