"""
快手无人带货工具 - 线程管理器
Kuaishou Automation Tool - Thread Manager

本模块实现多线程管理，确保UI响应性，包括：
- 工作线程管理
- 线程间通信
- 任务队列管理
- 线程池管理
"""

import threading
import queue
import time
from typing import Callable, Any, Optional, Dict
from concurrent.futures import ThreadPoolExecutor, Future
from enum import Enum

from .logger import get_logger


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class Task:
    """
    任务类
    
    封装要在工作线程中执行的任务。
    
    Attributes:
        task_id (str): 任务ID
        name (str): 任务名称
        func (Callable): 要执行的函数
        args (tuple): 函数参数
        kwargs (dict): 函数关键字参数
        status (TaskStatus): 任务状态
        result: 任务结果
        error: 错误信息
        progress_callback (Callable): 进度回调函数
        success_callback (Callable): 成功回调函数
        error_callback (Callable): 错误回调函数
    """
    
    def __init__(self, task_id: str, name: str, func: Callable,
                 args: tuple = (), kwargs: dict = None,
                 progress_callback: Callable = None,
                 success_callback: Callable = None,
                 error_callback: Callable = None):
        """
        初始化任务
        
        Args:
            task_id (str): 任务ID
            name (str): 任务名称
            func (Callable): 要执行的函数
            args (tuple): 函数参数
            kwargs (dict): 函数关键字参数
            progress_callback (Callable): 进度回调函数
            success_callback (Callable): 成功回调函数
            error_callback (Callable): 错误回调函数
        """
        self.task_id = task_id
        self.name = name
        self.func = func
        self.args = args or ()
        self.kwargs = kwargs or {}
        self.status = TaskStatus.PENDING
        self.result = None
        self.error = None
        self.progress_callback = progress_callback
        self.success_callback = success_callback
        self.error_callback = error_callback
        self.created_time = time.time()
        self.start_time = None
        self.end_time = None
        
    def execute(self):
        """执行任务"""
        try:
            self.status = TaskStatus.RUNNING
            self.start_time = time.time()
            
            # 执行函数
            self.result = self.func(*self.args, **self.kwargs)
            
            self.status = TaskStatus.COMPLETED
            self.end_time = time.time()
            
            # 调用成功回调
            if self.success_callback:
                self.success_callback(self.result)
                
        except Exception as e:
            self.status = TaskStatus.FAILED
            self.error = e
            self.end_time = time.time()
            
            # 调用错误回调
            if self.error_callback:
                self.error_callback(str(e))
                
    def get_duration(self) -> Optional[float]:
        """获取任务执行时间"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None
        
    def is_completed(self) -> bool:
        """检查任务是否完成"""
        return self.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]


class ThreadManager:
    """
    线程管理器
    
    管理工作线程和任务队列，确保UI线程不被阻塞。
    支持任务队列、线程池、任务监控等功能。
    
    Attributes:
        logger: 日志记录器
        executor (ThreadPoolExecutor): 线程池执行器
        tasks (Dict[str, Task]): 任务字典
        futures (Dict[str, Future]): Future对象字典
        max_workers (int): 最大工作线程数
    """
    
    def __init__(self, max_workers: int = 4):
        """
        初始化线程管理器
        
        Args:
            max_workers (int): 最大工作线程数
        """
        self.logger = get_logger("ThreadManager")
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.tasks = {}
        self.futures = {}
        self._shutdown = False
        
        self.logger.info(f"线程管理器已初始化，最大工作线程数: {max_workers}")
        
    def submit_task(self, task: Task) -> str:
        """
        提交任务到线程池
        
        Args:
            task (Task): 要执行的任务
            
        Returns:
            str: 任务ID
        """
        try:
            if self._shutdown:
                raise RuntimeError("线程管理器已关闭")
                
            # 保存任务
            self.tasks[task.task_id] = task
            
            # 提交到线程池
            future = self.executor.submit(task.execute)
            self.futures[task.task_id] = future
            
            self.logger.info(f"任务已提交: {task.name} (ID: {task.task_id})")
            return task.task_id
            
        except Exception as e:
            self.logger.error(f"提交任务失败: {e}")
            raise
            
    def create_and_submit_task(self, task_id: str, name: str, func: Callable,
                              args: tuple = (), kwargs: dict = None,
                              progress_callback: Callable = None,
                              success_callback: Callable = None,
                              error_callback: Callable = None) -> str:
        """
        创建并提交任务
        
        Args:
            task_id (str): 任务ID
            name (str): 任务名称
            func (Callable): 要执行的函数
            args (tuple): 函数参数
            kwargs (dict): 函数关键字参数
            progress_callback (Callable): 进度回调函数
            success_callback (Callable): 成功回调函数
            error_callback (Callable): 错误回调函数
            
        Returns:
            str: 任务ID
        """
        task = Task(
            task_id=task_id,
            name=name,
            func=func,
            args=args,
            kwargs=kwargs,
            progress_callback=progress_callback,
            success_callback=success_callback,
            error_callback=error_callback
        )
        
        return self.submit_task(task)
        
    def cancel_task(self, task_id: str) -> bool:
        """
        取消任务
        
        Args:
            task_id (str): 任务ID
            
        Returns:
            bool: 是否成功取消
        """
        try:
            if task_id in self.futures:
                future = self.futures[task_id]
                cancelled = future.cancel()
                
                if cancelled and task_id in self.tasks:
                    self.tasks[task_id].status = TaskStatus.CANCELLED
                    
                self.logger.info(f"任务取消{'成功' if cancelled else '失败'}: {task_id}")
                return cancelled
                
        except Exception as e:
            self.logger.error(f"取消任务失败: {e}")
            
        return False
        
    def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """
        获取任务状态
        
        Args:
            task_id (str): 任务ID
            
        Returns:
            TaskStatus: 任务状态，不存在返回None
        """
        if task_id in self.tasks:
            return self.tasks[task_id].status
        return None
        
    def get_task_result(self, task_id: str) -> Any:
        """
        获取任务结果
        
        Args:
            task_id (str): 任务ID
            
        Returns:
            任务结果，失败或不存在返回None
        """
        if task_id in self.tasks:
            task = self.tasks[task_id]
            if task.status == TaskStatus.COMPLETED:
                return task.result
        return None
        
    def get_task_error(self, task_id: str) -> Optional[str]:
        """
        获取任务错误信息
        
        Args:
            task_id (str): 任务ID
            
        Returns:
            str: 错误信息，无错误返回None
        """
        if task_id in self.tasks:
            task = self.tasks[task_id]
            if task.status == TaskStatus.FAILED and task.error:
                return str(task.error)
        return None
        
    def wait_for_task(self, task_id: str, timeout: Optional[float] = None) -> bool:
        """
        等待任务完成
        
        Args:
            task_id (str): 任务ID
            timeout (float, optional): 超时时间（秒）
            
        Returns:
            bool: 任务是否完成
        """
        try:
            if task_id in self.futures:
                future = self.futures[task_id]
                future.result(timeout=timeout)
                return True
        except Exception as e:
            self.logger.error(f"等待任务完成失败: {e}")
            
        return False
        
    def get_running_tasks(self) -> Dict[str, Task]:
        """
        获取正在运行的任务
        
        Returns:
            Dict[str, Task]: 正在运行的任务字典
        """
        return {
            task_id: task for task_id, task in self.tasks.items()
            if task.status == TaskStatus.RUNNING
        }
        
    def get_completed_tasks(self) -> Dict[str, Task]:
        """
        获取已完成的任务
        
        Returns:
            Dict[str, Task]: 已完成的任务字典
        """
        return {
            task_id: task for task_id, task in self.tasks.items()
            if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]
        }
        
    def cleanup_completed_tasks(self) -> int:
        """
        清理已完成的任务
        
        Returns:
            int: 清理的任务数量
        """
        completed_tasks = self.get_completed_tasks()
        cleaned_count = 0
        
        for task_id in completed_tasks.keys():
            if task_id in self.tasks:
                del self.tasks[task_id]
                cleaned_count += 1
                
            if task_id in self.futures:
                del self.futures[task_id]
                
        self.logger.info(f"已清理 {cleaned_count} 个完成的任务")
        return cleaned_count
        
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取线程管理器统计信息
        
        Returns:
            Dict: 统计信息
        """
        total_tasks = len(self.tasks)
        running_tasks = len(self.get_running_tasks())
        completed_tasks = len(self.get_completed_tasks())
        
        return {
            "max_workers": self.max_workers,
            "total_tasks": total_tasks,
            "running_tasks": running_tasks,
            "completed_tasks": completed_tasks,
            "pending_tasks": total_tasks - running_tasks - completed_tasks
        }
        
    def shutdown(self, wait: bool = True):
        """
        关闭线程管理器
        
        Args:
            wait (bool): 是否等待所有任务完成
        """
        try:
            self._shutdown = True
            self.executor.shutdown(wait=wait)
            self.logger.info("线程管理器已关闭")
        except Exception as e:
            self.logger.error(f"关闭线程管理器失败: {e}")
            
    def __enter__(self):
        """上下文管理器入口"""
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.shutdown()


# 全局线程管理器实例
_global_thread_manager = None


def get_thread_manager() -> ThreadManager:
    """
    获取全局线程管理器实例
    
    Returns:
        ThreadManager: 线程管理器实例
    """
    global _global_thread_manager
    if _global_thread_manager is None:
        _global_thread_manager = ThreadManager()
    return _global_thread_manager


def shutdown_global_thread_manager():
    """关闭全局线程管理器"""
    global _global_thread_manager
    if _global_thread_manager:
        _global_thread_manager.shutdown()
        _global_thread_manager = None
