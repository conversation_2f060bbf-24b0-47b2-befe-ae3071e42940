#!/usr/bin/env python3
"""
数据库重建脚本
用于重新创建数据库表结构
"""

import os
import sys

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def rebuild_database():
    """重建数据库"""
    try:
        print("🔄 开始重建数据库...")
        
        # 删除现有数据库文件
        db_path = os.path.join(current_dir, 'data', 'kuaishou_automation.db')
        if os.path.exists(db_path):
            os.remove(db_path)
            print(f"✅ 删除旧数据库文件: {db_path}")
        
        # 导入数据库管理器
        from data.models.base import DatabaseManager
        from data.models.account import Account
        
        # 创建数据库管理器实例
        db_manager = DatabaseManager()

        # 初始化数据库
        db_manager.initialize()
        print("✅ 数据库管理器初始化完成")

        # 创建所有表
        db_manager.create_tables()
        print("✅ 数据库表创建完成")
        
        # 验证表是否创建成功
        session = db_manager.get_session()
        try:
            # 尝试查询账号表
            count = session.query(Account).count()
            print(f"✅ 账号表验证成功，当前记录数: {count}")
        except Exception as e:
            print(f"❌ 表验证失败: {e}")
            return False
        finally:
            session.close()
        
        print("🎉 数据库重建完成！")
        return True
        
    except Exception as e:
        print(f"❌ 数据库重建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = rebuild_database()
    sys.exit(0 if success else 1)
