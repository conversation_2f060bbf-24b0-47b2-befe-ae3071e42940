// qpdfbookmarkmodel.sip generated by MetaSIP
//
// This file is part of the QtPdf Python extension module.
//
// Copyright (c) 2022 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QPdfBookmarkModel : QAbstractItemModel
{
%TypeHeaderCode
#include <qpdfbookmarkmodel.h>
%End

public:
    enum class Role
    {
        Title,
        Level,
        Page,
        Location,
        Zoom,
    };

    explicit QPdfBookmarkModel(QObject *parent /TransferThis/);
    virtual ~QPdfBookmarkModel();
    QPdfDocument *document() const;
    void setDocument(QPdfDocument *document);
    virtual QVariant data(const QModelIndex &index, int role) const;
    virtual QModelIndex index(int row, int column, const QModelIndex &parent = QModelIndex()) const;
    virtual QModelIndex parent(const QModelIndex &index) const;
    virtual int rowCount(const QModelIndex &parent = QModelIndex()) const;
    virtual int columnCount(const QModelIndex &parent = QModelIndex()) const;
    virtual QHash<int, QByteArray> roleNames() const;

signals:
    void documentChanged(QPdfDocument *document);
};
