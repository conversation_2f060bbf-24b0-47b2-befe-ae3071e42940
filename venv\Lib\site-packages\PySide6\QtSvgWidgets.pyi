# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations
"""
This file contains the exact signatures for all functions in module
PySide6.QtSvgWidgets, except for defaults which are replaced by "...".

# mypy: disable-error-code="override, overload-overlap"
"""

# Module `PySide6.QtSvgWidgets`

import PySide6.QtSvgWidgets
import PySide6.QtCore
import PySide6.QtGui
import PySide6.QtWidgets
import PySide6.QtSvg

import typing


class QGraphicsSvgItem(PySide6.QtWidgets.QGraphicsObject):

    @typing.overload
    def __init__(self, fileName: str, /, parentItem: PySide6.QtWidgets.QGraphicsItem | None = ..., *, elementId: str | None = ..., maximumCacheSize: PySide6.QtCore.QSize | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parentItem: PySide6.QtWidgets.QGraphicsItem | None = ..., *, elementId: str | None = ..., maximumCacheSize: PySide6.QtCore.QSize | None = ...) -> None: ...

    def boundingRect(self, /) -> PySide6.QtCore.QRectF: ...
    def elementId(self, /) -> str: ...
    def isCachingEnabled(self, /) -> bool: ...
    def maximumCacheSize(self, /) -> PySide6.QtCore.QSize: ...
    def paint(self, painter: PySide6.QtGui.QPainter, option: PySide6.QtWidgets.QStyleOptionGraphicsItem, /, widget: PySide6.QtWidgets.QWidget | None = ...) -> None: ...
    def renderer(self, /) -> PySide6.QtSvg.QSvgRenderer: ...
    def setCachingEnabled(self, arg__1: bool, /) -> None: ...
    def setElementId(self, id: str, /) -> None: ...
    def setMaximumCacheSize(self, size: PySide6.QtCore.QSize, /) -> None: ...
    def setSharedRenderer(self, renderer: PySide6.QtSvg.QSvgRenderer, /) -> None: ...
    def type(self, /) -> int: ...


class QIntList: ...


class QSvgWidget(PySide6.QtWidgets.QWidget):

    @typing.overload
    def __init__(self, file: str, /, parent: PySide6.QtWidgets.QWidget | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, parent: PySide6.QtWidgets.QWidget | None = ...) -> None: ...

    @typing.overload
    def load(self, file: str, /) -> None: ...
    @typing.overload
    def load(self, contents: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> None: ...
    def options(self, /) -> PySide6.QtSvg.QtSvg.Option: ...
    def paintEvent(self, event: PySide6.QtGui.QPaintEvent, /) -> None: ...
    def renderer(self, /) -> PySide6.QtSvg.QSvgRenderer: ...
    def setOptions(self, options: PySide6.QtSvg.QtSvg.Option, /) -> None: ...
    def sizeHint(self, /) -> PySide6.QtCore.QSize: ...


# eof
