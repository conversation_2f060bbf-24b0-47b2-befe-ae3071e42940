<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<typesystem package="PySide6.QtBluetooth"
            namespace-begin="QT_BEGIN_NAMESPACE" namespace-end="QT_END_NAMESPACE">
    <load-typesystem name="typesystem_core.xml" generate="no"/>
    <namespace-type name="QBluetooth">
        <enum-type name="AttAccessConstraint" flags="AttAccessConstraints"/>
        <enum-type name="Security" flags="SecurityFlags"/>
    </namespace-type>
    <value-type name="QBluetoothAddress"/>
    <value-type name="QBluetoothHostInfo"/>
    <object-type name="QBluetoothLocalDevice">
        <enum-type name="Error"/>
        <enum-type name="HostMode"/>
        <enum-type name="Pairing"/>
    </object-type>
    <object-type name="QBluetoothDeviceDiscoveryAgent">
        <enum-type name="DiscoveryMethod" flags="DiscoveryMethods"/>
        <enum-type name="Error"/>
    </object-type>
    <value-type name="QBluetoothDeviceInfo">
        <enum-type name="CoreConfiguration" flags="CoreConfigurations"/>
        <enum-type name="Field" flags="Fields"/>
        <enum-type name="MajorDeviceClass"/>
        <enum-type name="MinorAudioVideoClass"/>
        <enum-type name="MinorComputerClass"/>
        <enum-type name="MinorHealthClass"/>
        <enum-type name="MinorImagingClass"/>
        <enum-type name="MinorMiscellaneousClass"/>
        <enum-type name="MinorNetworkClass"/>
        <enum-type name="MinorPeripheralClass"/>
        <enum-type name="MinorPhoneClass"/>
        <enum-type name="MinorToyClass"/>
        <enum-type name="MinorWearableClass"/>
        <enum-type name="ServiceClass" flags="ServiceClasses"/>
    </value-type>
    <object-type name="QBluetoothServiceDiscoveryAgent">
        <enum-type name="DiscoveryMode"/>
        <enum-type name="Error"/>
    </object-type>
    <object-type name="QBluetoothServer">
        <enum-type name="Error"/>
    </object-type>
    <value-type name="QBluetoothServiceInfo">
        <enum-type name="AttributeId"/>
        <enum-type name="Protocol"/>
        <value-type name="Alternative"/>
        <value-type name="Sequence"/>
    </value-type>
    <object-type name="QBluetoothSocket">
        <enum-type name="SocketError"/>
        <enum-type name="SocketState"/>
    </object-type>
    <value-type name="QBluetoothUuid">
        <enum-type name="CharacteristicType"/>
        <enum-type name="DescriptorType"/>
        <enum-type name="ProtocolUuid"/>
        <enum-type name="ServiceClassUuid"/>
        <modify-function signature="toUInt16(bool*)const">
            <modify-argument index="1">
              <remove-argument/>
            </modify-argument>
            <modify-argument index="return" pyi-type="Tuple[int, bool]">
              <replace-type modified-type="PyTuple"/>
            </modify-argument>
            <inject-code class="target" position="beginning">
              <insert-template name="fix_bool*"/>
            </inject-code>
        </modify-function>
        <modify-function signature="toUInt32(bool*)const">
            <modify-argument index="1">
              <remove-argument/>
            </modify-argument>
            <modify-argument index="return" pyi-type="Tuple[int, bool]">
              <replace-type modified-type="PyTuple"/>
            </modify-argument>
            <inject-code class="target" position="beginning">
              <insert-template name="fix_bool*"/>
            </inject-code>
        </modify-function>
    </value-type>
    <value-type name="QLowEnergyAdvertisingData">
        <enum-type name="Discoverability"/>
    </value-type>
    <value-type name="QLowEnergyAdvertisingParameters">
        <value-type name="AddressInfo"/>
        <enum-type name="FilterPolicy"/>
        <enum-type name="Mode"/>
    </value-type>
    <value-type name="QLowEnergyCharacteristic">
        <enum-type name="PropertyType" flags="PropertyTypes"/>
    </value-type>
    <value-type name="QLowEnergyCharacteristicData"/>
    <value-type name="QLowEnergyConnectionParameters"/>
    <object-type name="QLowEnergyController">
        <enum-type name="ControllerState"/>
        <enum-type name="Error"/>
        <enum-type name="RemoteAddressType"/>
        <enum-type name="Role"/>
    </object-type>
    <value-type name="QLowEnergyDescriptor"/>
    <value-type name="QLowEnergyDescriptorData"/>
    <object-type name="QLowEnergyService">
        <enum-type name="DiscoveryMode"/>
        <enum-type name="ServiceError"/>
        <enum-type name="ServiceState"/>
        <enum-type name="ServiceType" flags="ServiceTypes"/>
        <enum-type name="WriteMode"/>
    </object-type>
    <value-type name="QLowEnergyServiceData">
        <enum-type name="ServiceType"/>
    </value-type>

    <!-- QtNetwork is pulled in via QtBluetoothDepends. -->
    <suppress-warning text="^Scoped enum 'Q(Ocsp)|(Dtls).*' does not have a type entry.*$"/>

</typesystem>
