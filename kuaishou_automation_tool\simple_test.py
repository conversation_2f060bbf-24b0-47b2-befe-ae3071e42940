"""
简单测试
"""

print("开始测试...")

try:
    print("测试基础导入...")
    import sys
    import os
    print("✅ 基础模块导入成功")
    
    # 添加路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    sys.path.insert(0, current_dir)
    print(f"✅ 路径添加成功: {current_dir}")
    
    # 测试Qt导入
    try:
        from PySide6.QtWidgets import QApplication
        print("✅ PySide6导入成功")
    except ImportError:
        try:
            from PyQt6.QtWidgets import QApplication
            print("✅ PyQt6导入成功")
        except ImportError:
            print("❌ Qt库导入失败")
    
    print("测试完成")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
