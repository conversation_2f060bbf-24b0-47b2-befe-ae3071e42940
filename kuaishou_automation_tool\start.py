"""
快手无人带货工具 - 启动脚本
Kuaishou Automation Tool - Startup Script

智能启动脚本，自动检测环境并选择最佳运行方式。
"""

import sys
import os
import subprocess

def check_qt_availability():
    """检查Qt库是否可用"""
    try:
        # 尝试导入PySide6
        import PySide6.QtWidgets
        return "PySide6"
    except ImportError:
        try:
            # 尝试导入PyQt6
            import PyQt6.QtWidgets
            return "PyQt6"
        except ImportError:
            return None

def check_display_available():
    """检查是否有显示环境（用于Linux/Mac）"""
    if os.name == 'nt':  # Windows
        return True
    else:
        return 'DISPLAY' in os.environ

def print_banner():
    """显示启动横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    快手无人带货自动化工具                      ║
║                  Kuaishou Automation Tool                    ║
║                        启动向导 v1.0.0                       ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def main():
    """主启动函数"""
    print_banner()
    
    print("🔍 正在检测运行环境...")
    
    # 检查Qt库可用性
    qt_lib = check_qt_availability()
    display_available = check_display_available()
    
    print(f"📊 环境检测结果:")
    print(f"   - Qt库: {qt_lib if qt_lib else '❌ 未安装'}")
    print(f"   - 显示环境: {'✅ 可用' if display_available else '❌ 不可用'}")
    
    # 决定启动方式
    if qt_lib and display_available:
        print(f"\n🎨 启动GUI版本 (使用 {qt_lib})")
        print("=" * 60)
        try:
            # 启动GUI版本
            if os.path.exists("main.py"):
                subprocess.run([sys.executable, "main.py"], check=True)
            else:
                print("❌ 找不到main.py文件")
                return 1
        except subprocess.CalledProcessError as e:
            print(f"❌ GUI版本启动失败: {e}")
            print("\n🔄 正在切换到命令行版本...")
            return start_cli_version()
        except KeyboardInterrupt:
            print("\n👋 用户取消启动")
            return 0
    else:
        print(f"\n💻 启动命令行版本")
        if not qt_lib:
            print("💡 提示: 安装PySide6可启用GUI版本: pip install PySide6")
        print("=" * 60)
        return start_cli_version()

def start_cli_version():
    """启动命令行版本"""
    try:
        if os.path.exists("main_cli.py"):
            subprocess.run([sys.executable, "main_cli.py"], check=True)
            return 0
        else:
            print("❌ 找不到main_cli.py文件")
            return 1
    except subprocess.CalledProcessError as e:
        print(f"❌ 命令行版本启动失败: {e}")
        return 1
    except KeyboardInterrupt:
        print("\n👋 用户取消启动")
        return 0

def show_help():
    """显示帮助信息"""
    help_text = """
使用方法:
    python start.py          # 自动选择最佳启动方式
    python start.py --gui     # 强制启动GUI版本
    python start.py --cli     # 强制启动命令行版本
    python start.py --help    # 显示此帮助信息

环境要求:
    - Python 3.9+
    - SQLAlchemy (pip install sqlalchemy)
    - GUI版本需要: PySide6 或 PyQt6

安装GUI依赖:
    pip install PySide6

项目文件:
    - main.py: GUI版本入口
    - main_cli.py: 命令行版本入口
    - test_core.py: 核心功能测试
    """
    print(help_text)

if __name__ == "__main__":
    # 处理命令行参数
    if len(sys.argv) > 1:
        arg = sys.argv[1].lower()
        
        if arg in ['--help', '-h', 'help']:
            show_help()
            sys.exit(0)
        elif arg in ['--gui', '-g', 'gui']:
            print("🎨 强制启动GUI版本...")
            if check_qt_availability():
                try:
                    subprocess.run([sys.executable, "main.py"], check=True)
                    sys.exit(0)
                except subprocess.CalledProcessError as e:
                    print(f"❌ GUI版本启动失败: {e}")
                    sys.exit(1)
            else:
                print("❌ 未安装Qt库，无法启动GUI版本")
                print("💡 请安装: pip install PySide6")
                sys.exit(1)
        elif arg in ['--cli', '-c', 'cli']:
            print("💻 强制启动命令行版本...")
            sys.exit(start_cli_version())
        else:
            print(f"❌ 未知参数: {arg}")
            print("使用 --help 查看帮助信息")
            sys.exit(1)
    else:
        # 自动选择启动方式
        sys.exit(main())
