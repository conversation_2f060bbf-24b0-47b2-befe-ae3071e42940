#!/usr/bin/env python3
"""
测试设置自动保存功能
"""

import os
import sys
import json

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_settings_file_creation():
    """测试设置文件创建"""
    print("🔧 测试设置文件功能...")
    
    # 设置文件路径
    config_dir = os.path.join(current_dir, 'config')
    settings_file = os.path.join(config_dir, 'video_settings.json')
    
    print(f"配置目录: {config_dir}")
    print(f"设置文件: {settings_file}")
    
    # 确保配置目录存在
    os.makedirs(config_dir, exist_ok=True)
    print(f"✅ 配置目录已创建: {config_dir}")
    
    # 创建测试设置
    test_settings = {
        'video_size': '1080x1920 (竖屏)',
        'fps': 30,
        'image_duration': 3.0,
        'voice_engine': 'Edge TTS（免费，质量好）',
        'voice_character': '晓晓 - 女声（温柔甜美）',
        'voice_speed': 100,
        'voice_volume': 80,
        'enable_subtitles': True,
        'subtitle_position': '底部居中',
        'subtitle_background': '半透明黑色背景',
        'subtitle_font_size': 48,
        'subtitle_font_color': '白色',
        'last_output_dir': os.path.expanduser('~/Downloads'),
    }
    
    # 保存测试设置
    try:
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(test_settings, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 测试设置已保存到: {settings_file}")
        
        # 验证文件内容
        with open(settings_file, 'r', encoding='utf-8') as f:
            loaded_settings = json.load(f)
        
        print("📋 保存的设置内容:")
        for key, value in loaded_settings.items():
            print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 设置文件操作失败: {e}")
        return False

def test_settings_validation():
    """测试设置验证"""
    print("\n🔍 测试设置验证...")
    
    # 测试各种设置组合
    test_cases = [
        {
            'name': '推荐配置 - 带货视频',
            'settings': {
                'video_size': '1080x1920 (竖屏)',
                'voice_character': '晓晓 - 女声（温柔甜美）',
                'subtitle_position': '底部居中',
                'subtitle_background': '半透明黑色背景',
                'subtitle_font_color': '白色'
            }
        },
        {
            'name': '突出配置 - 重点强调',
            'settings': {
                'video_size': '1080x1920 (竖屏)',
                'voice_character': '云希 - 男声（成熟稳重）',
                'subtitle_position': '中间居中',
                'subtitle_background': '纯色黑色背景',
                'subtitle_font_color': '黄色'
            }
        },
        {
            'name': '简洁配置 - 无背景字幕',
            'settings': {
                'video_size': '1920x1080 (横屏)',
                'voice_character': '晓伊 - 女声（活泼可爱）',
                'subtitle_position': '顶部居中',
                'subtitle_background': '无背景',
                'subtitle_font_color': '白色'
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}:")
        for key, value in test_case['settings'].items():
            print(f"   {key}: {value}")
    
    return True

def test_default_settings():
    """测试默认设置"""
    print("\n⚙️ 默认设置说明:")
    
    default_settings = {
        '视频尺寸': '1080x1920 (竖屏) - 适合快手',
        '帧率': '30 FPS - 流畅播放',
        '图片时长': '3.0秒 - 适中节奏',
        '语音引擎': '自动选择 - 智能切换',
        '语音角色': '晓晓女声 - 温柔甜美',
        '语音速度': '1.0倍速 - 正常语速',
        '语音音量': '80% - 适中音量',
        '启用字幕': '是 - 提升观看体验',
        '字幕位置': '底部居中 - 不遮挡内容',
        '字幕背景': '半透明黑色 - 最佳可读性',
        '字体大小': '48px - 清晰易读',
        '字体颜色': '白色 - 通用性好'
    }
    
    print("📋 推荐的默认设置:")
    for key, value in default_settings.items():
        print(f"  • {key}: {value}")
    
    return True

def test_auto_save_benefits():
    """说明自动保存的好处"""
    print("\n🎯 自动保存功能优势:")
    
    benefits = [
        "✅ 一次配置，永久记忆",
        "✅ 无需重复设置参数",
        "✅ 提升工作效率",
        "✅ 个性化配置保持",
        "✅ 支持设置导入导出",
        "✅ 意外关闭不丢失设置",
        "✅ 多设备间配置同步"
    ]
    
    for benefit in benefits:
        print(f"  {benefit}")
    
    print("\n🔧 设置管理功能:")
    management_features = [
        "🔄 恢复默认设置 - 一键重置",
        "📤 导出设置 - 备份配置",
        "📥 导入设置 - 恢复配置",
        "💾 自动保存 - 实时更新",
        "🔒 设置备份 - 防止丢失"
    ]
    
    for feature in management_features:
        print(f"  {feature}")

def main():
    """主函数"""
    print("💾 设置自动保存功能测试")
    print("=" * 50)
    print("这个工具用于测试图文视频合成的设置保存功能。")
    print()
    
    # 测试设置文件创建
    file_ok = test_settings_file_creation()
    
    # 测试设置验证
    validation_ok = test_settings_validation()
    
    # 测试默认设置
    default_ok = test_default_settings()
    
    # 说明自动保存优势
    test_auto_save_benefits()
    
    print("\n" + "=" * 50)
    print("📋 功能总结:")
    
    if file_ok:
        print("✅ 设置文件创建和读写正常")
    else:
        print("❌ 设置文件操作有问题")
    
    if validation_ok:
        print("✅ 设置验证功能正常")
    else:
        print("❌ 设置验证有问题")
    
    if default_ok:
        print("✅ 默认设置配置合理")
    else:
        print("❌ 默认设置有问题")
    
    print("\n🎉 现在您可以:")
    print("1. 打开图文视频合成界面")
    print("2. 在高级设置中调整参数")
    print("3. 设置会自动保存，下次打开时保持")
    print("4. 使用设置管理功能备份和恢复配置")
    
    return file_ok and validation_ok and default_ok

if __name__ == "__main__":
    try:
        success = main()
        input("\n按回车键退出...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
        sys.exit(1)
