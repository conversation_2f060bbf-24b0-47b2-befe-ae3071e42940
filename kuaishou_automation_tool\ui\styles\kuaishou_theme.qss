/* 快手无人带货工具 - 主题样式表 */
/* Kuaishou Automation Tool - Theme Stylesheet */

/* ===== 全局样式 Global Styles ===== */
QWidget {
    background-color: #1a1a1a;
    color: #ffffff;
    font-family: "Microsoft YaHei", "Segoe UI", Arial, sans-serif;
    font-size: 12px;
}

/* ===== 主窗口样式 Main Window ===== */
QMainWindow {
    background-color: #1a1a1a;
    border: none;
}

/* ===== 导航栏样式 Navigation Bar ===== */
#navigationWidget {
    background-color: #2d2d2d;
    border-right: 2px solid #FF6B35;
    min-width: 200px;
    max-width: 250px;
}

#navigationTitle {
    background-color: #FF6B35;
    color: #ffffff;
    font-size: 16px;
    font-weight: bold;
    padding: 15px;
    border: none;
}

/* ===== 导航按钮样式 Navigation Buttons ===== */
QPushButton#navButton {
    background-color: transparent;
    color: #cccccc;
    border: none;
    padding: 12px 20px;
    text-align: left;
    font-size: 13px;
    border-radius: 0px;
}

QPushButton#navButton:hover {
    background-color: #3d3d3d;
    color: #ffffff;
}

QPushButton#navButton:pressed {
    background-color: #FF6B35;
    color: #ffffff;
}

QPushButton#navButton:checked {
    background-color: #FF6B35;
    color: #ffffff;
    border-left: 4px solid #FFD700;
}

/* ===== 内容区域样式 Content Area ===== */
#contentWidget {
    background-color: #1a1a1a;
    border: none;
    padding: 20px;
}

/* ===== 标题样式 Title Styles ===== */
QLabel#pageTitle {
    color: #ffffff;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
    padding: 10px 0px;
}

QLabel#sectionTitle {
    color: #FFD700;
    font-size: 16px;
    font-weight: bold;
    margin: 15px 0px 10px 0px;
}

/* ===== 表格样式 Table Styles ===== */
QTableWidget {
    background-color: #2d2d2d;
    alternate-background-color: #3d3d3d;
    gridline-color: #4d4d4d;
    border: 1px solid #4d4d4d;
    border-radius: 5px;
    selection-background-color: #FF6B35;
}

QTableWidget::item {
    padding: 8px;
    border: none;
}

QTableWidget::item:selected {
    background-color: #FF6B35;
    color: #ffffff;
}

QHeaderView::section {
    background-color: #3d3d3d;
    color: #ffffff;
    padding: 10px;
    border: none;
    border-bottom: 2px solid #FF6B35;
    font-weight: bold;
}

/* ===== 按钮样式 Button Styles ===== */
QPushButton {
    background-color: #FF6B35;
    color: #ffffff;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: bold;
    min-height: 20px;
}

QPushButton:hover {
    background-color: #ff7a47;
}

QPushButton:pressed {
    background-color: #e55a2b;
}

QPushButton:disabled {
    background-color: #666666;
    color: #999999;
}

/* ===== 次要按钮样式 Secondary Button ===== */
QPushButton#secondaryButton {
    background-color: #4d4d4d;
    color: #ffffff;
}

QPushButton#secondaryButton:hover {
    background-color: #5d5d5d;
}

QPushButton#secondaryButton:pressed {
    background-color: #3d3d3d;
}

/* ===== 危险按钮样式 Danger Button ===== */
QPushButton#dangerButton {
    background-color: #dc3545;
    color: #ffffff;
}

QPushButton#dangerButton:hover {
    background-color: #c82333;
}

QPushButton#dangerButton:pressed {
    background-color: #bd2130;
}

/* ===== 输入框样式 Input Styles ===== */
QLineEdit {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 2px solid #4d4d4d;
    border-radius: 5px;
    padding: 8px 12px;
    font-size: 12px;
}

QLineEdit:focus {
    border-color: #FF6B35;
}

QLineEdit:disabled {
    background-color: #1a1a1a;
    color: #666666;
    border-color: #333333;
}

/* ===== 文本区域样式 Text Area ===== */
QTextEdit {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 2px solid #4d4d4d;
    border-radius: 5px;
    padding: 8px;
}

QTextEdit:focus {
    border-color: #FF6B35;
}

/* ===== 下拉框样式 ComboBox Styles ===== */
QComboBox {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 2px solid #4d4d4d;
    border-radius: 5px;
    padding: 8px 12px;
    min-width: 100px;
}

QComboBox:focus {
    border-color: #FF6B35;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: url(down_arrow.png);
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #4d4d4d;
    selection-background-color: #FF6B35;
}

/* ===== 复选框样式 CheckBox Styles ===== */
QCheckBox {
    color: #ffffff;
    spacing: 8px;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 2px solid #4d4d4d;
    border-radius: 3px;
    background-color: #2d2d2d;
}

QCheckBox::indicator:checked {
    background-color: #FF6B35;
    border-color: #FF6B35;
}

QCheckBox::indicator:hover {
    border-color: #FF6B35;
}

/* ===== 单选框样式 RadioButton Styles ===== */
QRadioButton {
    color: #ffffff;
    spacing: 8px;
}

QRadioButton::indicator {
    width: 16px;
    height: 16px;
    border: 2px solid #4d4d4d;
    border-radius: 8px;
    background-color: #2d2d2d;
}

QRadioButton::indicator:checked {
    background-color: #FF6B35;
    border-color: #FF6B35;
}

QRadioButton::indicator:hover {
    border-color: #FF6B35;
}

/* ===== 滚动条样式 ScrollBar Styles ===== */
QScrollBar:vertical {
    background-color: #2d2d2d;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #4d4d4d;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #FF6B35;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    border: none;
    background: none;
}

/* ===== 状态标签样式 Status Label Styles ===== */
QLabel#statusOnline {
    color: #28a745;
    font-weight: bold;
}

QLabel#statusOffline {
    color: #dc3545;
    font-weight: bold;
}

QLabel#statusWarning {
    color: #FFD700;
    font-weight: bold;
}

/* ===== 分组框样式 GroupBox Styles ===== */
QGroupBox {
    color: #ffffff;
    border: 2px solid #4d4d4d;
    border-radius: 5px;
    margin-top: 10px;
    padding-top: 10px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px 0 5px;
    color: #FFD700;
    font-weight: bold;
}

/* ===== 工具提示样式 Tooltip Styles ===== */
QToolTip {
    background-color: #2d2d2d;
    color: #ffffff;
    border: 1px solid #FF6B35;
    border-radius: 3px;
    padding: 5px;
    font-size: 11px;
}
