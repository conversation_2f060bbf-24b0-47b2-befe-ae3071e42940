"""
快手无人带货工具 - 基础数据模型
Kuaishou Automation Tool - Base Data Models

本模块定义基础数据模型类和通用数据操作接口。
采用SQLAlchemy ORM框架实现数据持久化。
"""

from sqlalchemy import create_engine, Column, Integer, String, DateTime, Text, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
import os
import json


# 创建基础模型类
Base = declarative_base()


class BaseModel(Base):
    """
    基础模型类
    
    所有数据模型的基类，提供通用字段和方法。
    包含创建时间、更新时间等通用字段。
    
    Attributes:
        id (int): 主键ID
        created_at (datetime): 创建时间
        updated_at (datetime): 更新时间
        is_active (bool): 是否激活状态
    """
    
    __abstract__ = True
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    created_at = Column(DateTime, default=datetime.now, nullable=False)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    def to_dict(self):
        """
        将模型对象转换为字典
        
        Returns:
            dict: 包含模型所有字段的字典
        """
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if isinstance(value, datetime):
                value = value.strftime("%Y-%m-%d %H:%M:%S")
            result[column.name] = value
        return result
        
    def from_dict(self, data):
        """
        从字典更新模型对象
        
        Args:
            data (dict): 包含字段值的字典
        """
        for key, value in data.items():
            if hasattr(self, key) and key not in ['id', 'created_at']:
                setattr(self, key, value)
        self.updated_at = datetime.now()
        
    def __repr__(self):
        """返回模型的字符串表示"""
        return f"<{self.__class__.__name__}(id={self.id})>"


class DatabaseManager:
    """
    数据库管理器
    
    负责数据库连接、会话管理、表创建等操作。
    采用单例模式确保全局唯一的数据库连接。
    
    Attributes:
        engine: SQLAlchemy数据库引擎
        SessionLocal: 会话工厂类
        _instance: 单例实例
    """
    
    _instance = None
    
    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance
        
    def __init__(self):
        """初始化数据库管理器"""
        if not self._initialized:
            self.engine = None
            self.SessionLocal = None
            self._initialized = True
            
    def initialize(self, database_url=None):
        """
        初始化数据库连接
        
        Args:
            database_url (str, optional): 数据库连接URL，默认使用SQLite
        """
        if database_url is None:
            # 默认使用SQLite数据库
            db_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'data')
            os.makedirs(db_dir, exist_ok=True)
            database_url = f"sqlite:///{os.path.join(db_dir, 'kuaishou_automation.db')}"
            
        self.engine = create_engine(
            database_url,
            echo=False,  # 设置为True可以看到SQL语句
            pool_pre_ping=True
        )
        
        self.SessionLocal = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=self.engine
        )
        
        # 创建所有表
        self.create_tables()
        
    def create_tables(self):
        """创建数据库表"""
        if self.engine:
            Base.metadata.create_all(bind=self.engine)
            
    def get_session(self):
        """
        获取数据库会话
        
        Returns:
            Session: SQLAlchemy会话对象
        """
        if self.SessionLocal:
            return self.SessionLocal()
        else:
            raise RuntimeError("数据库未初始化，请先调用initialize()方法")
            
    def close(self):
        """关闭数据库连接"""
        if self.engine:
            self.engine.dispose()


class ConfigManager:
    """
    配置管理器
    
    负责应用程序配置的读取、保存和管理。
    支持JSON格式的配置文件。
    
    Attributes:
        config_file (str): 配置文件路径
        _config (dict): 配置数据字典
    """
    
    def __init__(self, config_file=None):
        """
        初始化配置管理器
        
        Args:
            config_file (str, optional): 配置文件路径
        """
        if config_file is None:
            config_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'config')
            os.makedirs(config_dir, exist_ok=True)
            config_file = os.path.join(config_dir, 'app_config.json')
            
        self.config_file = config_file
        self._config = self.load_config()
        
    def load_config(self):
        """
        加载配置文件
        
        Returns:
            dict: 配置数据字典
        """
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # 返回默认配置
                return self.get_default_config()
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return self.get_default_config()
            
    def save_config(self):
        """保存配置到文件"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            
    def get_default_config(self):
        """
        获取默认配置
        
        Returns:
            dict: 默认配置字典
        """
        return {
            "app": {
                "name": "快手无人带货工具",
                "version": "1.0.0",
                "debug": False
            },
            "database": {
                "type": "sqlite",
                "path": "data/kuaishou_automation.db"
            },
            "ui": {
                "theme": "kuaishou_dark",
                "language": "zh_CN",
                "window_size": [1200, 800],
                "window_position": [100, 100]
            },
            "automation": {
                "check_interval": 30,
                "max_retry_count": 3,
                "timeout": 60
            },
            "logging": {
                "level": "INFO",
                "file_path": "logs/app.log",
                "max_file_size": 10485760,  # 10MB
                "backup_count": 5
            }
        }
        
    def get(self, key, default=None):
        """
        获取配置值
        
        Args:
            key (str): 配置键，支持点号分隔的嵌套键
            default: 默认值
            
        Returns:
            配置值或默认值
        """
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
            
    def set(self, key, value):
        """
        设置配置值
        
        Args:
            key (str): 配置键，支持点号分隔的嵌套键
            value: 配置值
        """
        keys = key.split('.')
        config = self._config
        
        # 创建嵌套字典结构
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
            
        config[keys[-1]] = value
        
    def update(self, updates):
        """
        批量更新配置
        
        Args:
            updates (dict): 要更新的配置字典
        """
        def deep_update(base_dict, update_dict):
            for key, value in update_dict.items():
                if isinstance(value, dict) and key in base_dict and isinstance(base_dict[key], dict):
                    deep_update(base_dict[key], value)
                else:
                    base_dict[key] = value
                    
        deep_update(self._config, updates)
        
    def reset_to_default(self):
        """重置为默认配置"""
        self._config = self.get_default_config()
        
    def get_all(self):
        """
        获取所有配置
        
        Returns:
            dict: 完整的配置字典
        """
        return self._config.copy()
