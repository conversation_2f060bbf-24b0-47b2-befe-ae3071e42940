from enum import IntFlag

import comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0 as __wrapper_module__
from comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0 import (
    typelib_path, OLE_XPOS_PIXELS, OLE_XSIZE_PIXELS, OLE_HANDLE,
    OLE_YPOS_PIXELS, StdPicture, Font, OLE_YSIZE_PIXELS,
    OLE_CANCELBOOL, DIS<PERSON>ROPERTY, GUID, StdFont, _lcid, FONTSIZE,
    _check_version, IPicture, COMMETHOD, Checked,
    OLE_ENABLEDEFAULTBOOL, dispid, Unchecked, O<PERSON>_COLOR, FONTNAME,
    OLE_YSIZE_HIMETRIC, O<PERSON>_YPOS_CONTAINER, Monochrome,
    OLE_OPTEXCLUSIVE, HRESULT, FONTSTRIKETHROUGH, OLE_XSIZE_CONTAINER,
    IFontDisp, EXCEPINFO, CoClass, <PERSON><PERSON><PERSON>NDERSCOR<PERSON>, <PERSON>LE_YPOS_HIMETRIC,
    BSTR, <PERSON><PERSON>PPARAMS, Picture, OLE_XSIZE_HIMETRIC, IEnumVARIANT,
    OLE_XPOS_HIMETRIC, FontEvents, Default, OLE_YSIZE_CONTAINER,
    Library, Color, OLE_XPOS_CONTAINER, IDispatch, IPictureDisp,
    FONTITALIC, IFont, FONTBOLD, VgaColor, VARIANT_BOOL, DISPMETHOD,
    IUnknown, IFontEventsDisp, Gray
)


class OLE_TRISTATE(IntFlag):
    Unchecked = 0
    Checked = 1
    Gray = 2


class LoadPictureConstants(IntFlag):
    Default = 0
    Monochrome = 1
    VgaColor = 2
    Color = 4


__all__ = [
    'typelib_path', 'OLE_XPOS_PIXELS', 'FONTSTRIKETHROUGH',
    'OLE_XSIZE_PIXELS', 'OLE_HANDLE', 'OLE_YPOS_PIXELS',
    'OLE_XSIZE_CONTAINER', 'Font', 'StdPicture', 'OLE_YSIZE_PIXELS',
    'IFontEventsDisp', 'IFontDisp', 'OLE_CANCELBOOL',
    'FONTUNDERSCORE', 'OLE_YPOS_HIMETRIC', 'StdFont', 'Picture',
    'OLE_XSIZE_HIMETRIC', 'OLE_XPOS_HIMETRIC', 'FONTSIZE',
    'FontEvents', 'OLE_YSIZE_CONTAINER', 'Library', 'OLE_TRISTATE',
    'IPicture', 'Color', 'OLE_XPOS_CONTAINER', 'Checked',
    'OLE_ENABLEDEFAULTBOOL', 'IPictureDisp', 'Unchecked',
    'FONTITALIC', 'IFont', 'OLE_COLOR', 'FONTNAME', 'FONTBOLD',
    'OLE_YSIZE_HIMETRIC', 'OLE_YPOS_CONTAINER',
    'LoadPictureConstants', 'VgaColor', 'Monochrome',
    'OLE_OPTEXCLUSIVE', 'Default', 'Gray'
]

