"""
快手无人带货工具 - 账号数据仓库
Kuaishou Automation Tool - Account Repository

本模块实现账号数据的CRUD操作和业务查询方法。
采用仓库模式封装数据访问逻辑。
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from sqlalchemy import and_, or_, desc, asc
from ..models.account import Account, AccountStatus, AccountType, AccountStatistics
from ..models.base import DatabaseManager


class AccountRepository:
    """
    账号数据仓库类
    
    提供账号数据的完整CRUD操作和业务查询方法。
    封装数据库操作细节，提供简洁的业务接口。
    
    Attributes:
        db_manager (DatabaseManager): 数据库管理器实例
    """
    
    def __init__(self):
        """初始化账号仓库"""
        self.db_manager = DatabaseManager()
        
    def create(self, account_data: Dict[str, Any]) -> Optional[Account]:
        """
        创建新账号
        
        Args:
            account_data (dict): 账号数据字典
            
        Returns:
            Account: 创建的账号对象，失败返回None
            
        Raises:
            ValueError: 数据验证失败
            IntegrityError: 数据完整性约束失败（如用户名重复）
        """
        session = self.db_manager.get_session()
        try:
            # 创建账号对象
            account = Account()
            account.from_dict(account_data)
            
            # 添加到会话
            session.add(account)
            session.commit()
            session.refresh(account)
            
            return account
            
        except IntegrityError as e:
            session.rollback()
            if "username" in str(e):
                raise ValueError("用户名已存在")
            else:
                raise ValueError(f"数据完整性错误: {e}")
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()
            
    def get_by_id(self, account_id: int) -> Optional[Account]:
        """
        根据ID获取账号
        
        Args:
            account_id (int): 账号ID
            
        Returns:
            Account: 账号对象，不存在返回None
        """
        session = self.db_manager.get_session()
        try:
            return session.query(Account).filter(
                and_(Account.id == account_id, Account.is_active == True)
            ).first()
        finally:
            session.close()
            
    def get_by_username(self, username: str) -> Optional[Account]:
        """
        根据用户名获取账号
        
        Args:
            username (str): 用户名
            
        Returns:
            Account: 账号对象，不存在返回None
        """
        session = self.db_manager.get_session()
        try:
            return session.query(Account).filter(
                and_(Account.username == username, Account.is_active == True)
            ).first()
        finally:
            session.close()
            
    def get_all(self, include_inactive: bool = False) -> List[Account]:
        """
        获取所有账号
        
        Args:
            include_inactive (bool): 是否包含非激活账号
            
        Returns:
            List[Account]: 账号列表
        """
        session = self.db_manager.get_session()
        try:
            query = session.query(Account)
            if not include_inactive:
                query = query.filter(Account.is_active == True)
            return query.order_by(desc(Account.created_at)).all()
        finally:
            session.close()
            
    def get_by_status(self, status: AccountStatus) -> List[Account]:
        """
        根据状态获取账号列表
        
        Args:
            status (AccountStatus): 账号状态
            
        Returns:
            List[Account]: 指定状态的账号列表
        """
        session = self.db_manager.get_session()
        try:
            return session.query(Account).filter(
                and_(Account.status == status, Account.is_active == True)
            ).order_by(desc(Account.last_login_time)).all()
        finally:
            session.close()
            
    def get_by_type(self, account_type: AccountType) -> List[Account]:
        """
        根据类型获取账号列表
        
        Args:
            account_type (AccountType): 账号类型
            
        Returns:
            List[Account]: 指定类型的账号列表
        """
        session = self.db_manager.get_session()
        try:
            return session.query(Account).filter(
                and_(Account.account_type == account_type, Account.is_active == True)
            ).order_by(desc(Account.created_at)).all()
        finally:
            session.close()
            
    def search(self, keyword: str, limit: int = 50) -> List[Account]:
        """
        搜索账号
        
        Args:
            keyword (str): 搜索关键词（用户名或昵称）
            limit (int): 结果数量限制
            
        Returns:
            List[Account]: 搜索结果列表
        """
        session = self.db_manager.get_session()
        try:
            return session.query(Account).filter(
                and_(
                    or_(
                        Account.username.contains(keyword),
                        Account.nickname.contains(keyword)
                    ),
                    Account.is_active == True
                )
            ).limit(limit).all()
        finally:
            session.close()
            
    def update(self, account_id: int, update_data: Dict[str, Any]) -> Optional[Account]:
        """
        更新账号信息
        
        Args:
            account_id (int): 账号ID
            update_data (dict): 更新数据字典
            
        Returns:
            Account: 更新后的账号对象，失败返回None
        """
        session = self.db_manager.get_session()
        try:
            account = session.query(Account).filter(
                and_(Account.id == account_id, Account.is_active == True)
            ).first()
            
            if account:
                account.from_dict(update_data)
                session.commit()
                session.refresh(account)
                return account
            else:
                return None
                
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()
            
    def update_status(self, account_id: int, status: AccountStatus, ip_address: str = None) -> bool:
        """
        更新账号状态
        
        Args:
            account_id (int): 账号ID
            status (AccountStatus): 新状态
            ip_address (str, optional): IP地址（用于登录状态）
            
        Returns:
            bool: 更新是否成功
        """
        session = self.db_manager.get_session()
        try:
            account = session.query(Account).filter(
                and_(Account.id == account_id, Account.is_active == True)
            ).first()
            
            if account:
                if status == AccountStatus.ONLINE:
                    account.update_login_info(ip_address)
                else:
                    account.status = status
                    
                session.commit()
                return True
            else:
                return False
                
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()
            
    def delete(self, account_id: int, soft_delete: bool = True) -> bool:
        """
        删除账号
        
        Args:
            account_id (int): 账号ID
            soft_delete (bool): 是否软删除（标记为非激活）
            
        Returns:
            bool: 删除是否成功
        """
        session = self.db_manager.get_session()
        try:
            account = session.query(Account).filter(Account.id == account_id).first()
            
            if account:
                if soft_delete:
                    account.is_active = False
                    session.commit()
                else:
                    session.delete(account)
                    session.commit()
                return True
            else:
                return False
                
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()
            
    def batch_update_status(self, account_ids: List[int], status: AccountStatus) -> int:
        """
        批量更新账号状态
        
        Args:
            account_ids (List[int]): 账号ID列表
            status (AccountStatus): 新状态
            
        Returns:
            int: 成功更新的账号数量
        """
        session = self.db_manager.get_session()
        try:
            updated_count = session.query(Account).filter(
                and_(Account.id.in_(account_ids), Account.is_active == True)
            ).update({Account.status: status}, synchronize_session=False)
            
            session.commit()
            return updated_count
            
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()
            
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取账号统计信息
        
        Returns:
            dict: 统计信息字典
        """
        session = self.db_manager.get_session()
        try:
            return {
                'total_count': AccountStatistics.get_total_count(session),
                'status_count': AccountStatistics.get_status_count(session),
                'type_count': AccountStatistics.get_type_count(session)
            }
        finally:
            session.close()
            
    def get_online_accounts(self) -> List[Account]:
        """
        获取在线账号列表
        
        Returns:
            List[Account]: 在线账号列表
        """
        return self.get_by_status(AccountStatus.ONLINE)
        
    def get_available_accounts(self) -> List[Account]:
        """
        获取可用账号列表（非封禁、非异常状态）
        
        Returns:
            List[Account]: 可用账号列表
        """
        session = self.db_manager.get_session()
        try:
            return session.query(Account).filter(
                and_(
                    Account.status.notin_([AccountStatus.SUSPENDED, AccountStatus.ERROR]),
                    Account.is_active == True
                )
            ).order_by(desc(Account.last_login_time)).all()
        finally:
            session.close()
            
    def exists_username(self, username: str, exclude_id: int = None) -> bool:
        """
        检查用户名是否存在
        
        Args:
            username (str): 用户名
            exclude_id (int, optional): 排除的账号ID（用于更新时检查）
            
        Returns:
            bool: 用户名是否存在
        """
        session = self.db_manager.get_session()
        try:
            query = session.query(Account).filter(
                and_(Account.username == username, Account.is_active == True)
            )
            
            if exclude_id:
                query = query.filter(Account.id != exclude_id)
                
            return query.first() is not None
        finally:
            session.close()
            
    def get_paginated(self, page: int = 1, per_page: int = 20, 
                     status_filter: AccountStatus = None,
                     type_filter: AccountType = None) -> Dict[str, Any]:
        """
        分页获取账号列表
        
        Args:
            page (int): 页码（从1开始）
            per_page (int): 每页数量
            status_filter (AccountStatus, optional): 状态过滤
            type_filter (AccountType, optional): 类型过滤
            
        Returns:
            dict: 分页结果字典，包含items、total、page、per_page等
        """
        session = self.db_manager.get_session()
        try:
            query = session.query(Account).filter(Account.is_active == True)
            
            # 应用过滤条件
            if status_filter:
                query = query.filter(Account.status == status_filter)
            if type_filter:
                query = query.filter(Account.account_type == type_filter)
                
            # 获取总数
            total = query.count()
            
            # 分页查询
            offset = (page - 1) * per_page
            items = query.order_by(desc(Account.created_at)).offset(offset).limit(per_page).all()
            
            return {
                'items': items,
                'total': total,
                'page': page,
                'per_page': per_page,
                'pages': (total + per_page - 1) // per_page
            }
        finally:
            session.close()
