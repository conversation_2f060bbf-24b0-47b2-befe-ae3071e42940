#!/usr/bin/env python3
"""
测试不同语音引擎的质量
"""

import os
import sys
import asyncio

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_edge_tts():
    """测试Edge TTS"""
    print("🎤 测试Edge TTS...")
    
    try:
        import edge_tts
        
        # 测试文本
        test_text = "欢迎来到我们的直播间！今天为大家推荐这款超值好物。"
        
        # 不同的语音角色
        voices = {
            "晓晓-女声": "zh-CN-XiaoxiaoNeural",
            "云希-男声": "zh-CN-YunxiNeural", 
            "晓伊-女声": "zh-CN-XiaoyiNeural",
            "云健-男声": "zh-CN-YunjianNeural",
            "晓辰-女声": "zh-CN-XiaochenNeural"
        }
        
        async def generate_voice_samples():
            for name, voice in voices.items():
                output_path = f"test_voice_{name}.mp3"
                print(f"  生成 {name} 语音样本...")
                
                communicate = edge_tts.Communicate(test_text, voice)
                await communicate.save(output_path)
                
                if os.path.exists(output_path):
                    file_size = os.path.getsize(output_path) / 1024  # KB
                    print(f"  ✅ {name}: {file_size:.1f} KB")
                else:
                    print(f"  ❌ {name}: 生成失败")
        
        # 运行异步函数
        asyncio.run(generate_voice_samples())
        
        print("✅ Edge TTS测试完成")
        return True
        
    except ImportError:
        print("❌ edge-tts未安装")
        return False
    except Exception as e:
        print(f"❌ Edge TTS测试失败: {e}")
        return False

def test_gtts():
    """测试Google TTS"""
    print("\n🎤 测试Google TTS...")
    
    try:
        from gtts import gTTS
        
        test_text = "欢迎来到我们的直播间！今天为大家推荐这款超值好物。"
        output_path = "test_voice_gtts.mp3"
        
        print("  生成Google TTS语音...")
        tts = gTTS(text=test_text, lang='zh', slow=False)
        tts.save(output_path)
        
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / 1024  # KB
            print(f"  ✅ Google TTS: {file_size:.1f} KB")
            return True
        else:
            print("  ❌ Google TTS: 生成失败")
            return False
            
    except ImportError:
        print("❌ gTTS未安装")
        return False
    except Exception as e:
        print(f"❌ Google TTS测试失败: {e}")
        return False

def test_pyttsx3():
    """测试pyttsx3"""
    print("\n🎤 测试pyttsx3...")
    
    try:
        import pyttsx3
        
        test_text = "欢迎来到我们的直播间！今天为大家推荐这款超值好物。"
        output_path = "test_voice_pyttsx3.wav"
        
        print("  生成pyttsx3语音...")
        engine = pyttsx3.init()
        
        # 设置语音参数
        voices = engine.getProperty('voices')
        if voices:
            for voice in voices:
                if 'chinese' in voice.name.lower() or 'zh' in voice.id.lower():
                    engine.setProperty('voice', voice.id)
                    break
        
        engine.setProperty('rate', 200)
        engine.setProperty('volume', 0.8)
        
        engine.save_to_file(test_text, output_path)
        engine.runAndWait()
        
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / 1024  # KB
            print(f"  ✅ pyttsx3: {file_size:.1f} KB")
            return True
        else:
            print("  ❌ pyttsx3: 生成失败")
            return False
            
    except ImportError:
        print("❌ pyttsx3未安装")
        return False
    except Exception as e:
        print(f"❌ pyttsx3测试失败: {e}")
        return False

def compare_voice_quality():
    """比较语音质量"""
    print("\n📊 语音质量对比:")
    print("=" * 50)
    
    print("🏆 推荐排序（按自然度）:")
    print("1. Edge TTS - 免费，质量最好，接近真人")
    print("   • 晓晓: 温柔甜美女声，适合美妆、服装")
    print("   • 云希: 成熟稳重男声，适合数码、汽车")
    print("   • 晓伊: 活泼可爱女声，适合零食、玩具")
    print("   • 云健: 年轻阳光男声，适合运动、时尚")
    print("   • 晓辰: 知性优雅女声，适合书籍、教育")
    
    print("\n2. Google TTS - 在线，质量中等")
    print("   • 需要网络连接")
    print("   • 语音较为机械")
    
    print("\n3. pyttsx3 - 离线，质量一般")
    print("   • 完全离线")
    print("   • 语音比较机械")
    
    print("\n💡 使用建议:")
    print("• 带货视频推荐使用Edge TTS的晓晓或晓伊")
    print("• 男性产品可以使用云希或云健")
    print("• 无网络环境使用pyttsx3")

def cleanup_test_files():
    """清理测试文件"""
    test_files = [
        "test_voice_晓晓-女声.mp3",
        "test_voice_云希-男声.mp3", 
        "test_voice_晓伊-女声.mp3",
        "test_voice_云健-男声.mp3",
        "test_voice_晓辰-女声.mp3",
        "test_voice_gtts.mp3",
        "test_voice_pyttsx3.wav"
    ]
    
    print("\n🧹 清理测试文件...")
    for file in test_files:
        if os.path.exists(file):
            try:
                os.remove(file)
                print(f"  删除: {file}")
            except:
                pass

def main():
    """主函数"""
    print("🎤 语音质量测试工具")
    print("=" * 50)
    
    # 测试各种TTS引擎
    edge_ok = test_edge_tts()
    gtts_ok = test_gtts()
    pyttsx3_ok = test_pyttsx3()
    
    # 显示对比结果
    compare_voice_quality()
    
    print("\n" + "=" * 50)
    print("📋 测试结果总结:")
    print(f"• Edge TTS: {'✅ 可用' if edge_ok else '❌ 不可用'}")
    print(f"• Google TTS: {'✅ 可用' if gtts_ok else '❌ 不可用'}")
    print(f"• pyttsx3: {'✅ 可用' if pyttsx3_ok else '❌ 不可用'}")
    
    if edge_ok:
        print("\n🎉 推荐使用Edge TTS，语音质量最接近真人！")
        print("在图文视频合成界面选择不同的语音角色体验。")
    
    # 询问是否清理测试文件
    try:
        choice = input("\n是否删除测试语音文件？(y/n): ").lower()
        if choice in ['y', 'yes', '是']:
            cleanup_test_files()
    except:
        pass
    
    return edge_ok or gtts_ok or pyttsx3_ok

if __name__ == "__main__":
    try:
        success = main()
        input("\n按回车键退出...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
        sys.exit(1)
