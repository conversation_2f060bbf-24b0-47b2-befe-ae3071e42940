# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations
"""
This file contains the exact signatures for all functions in module
PySide6.QtScxml, except for defaults which are replaced by "...".

# mypy: disable-error-code="override, overload-overlap"
"""

# Module `PySide6.QtScxml`

import PySide6.QtScxml
import PySide6.QtCore

import enum
import typing
import collections.abc
from PySide6.QtCore import Signal
from shiboken6 import Shiboken


class QIntList: ...


class QScxmlCompiler(Shiboken.Object):

    class Loader(Shiboken.Object):

        def __init__(self, /) -> None: ...

        def load(self, name: str, baseDir: str, /) -> typing.Tuple[PySide6.QtCore.QByteArray, typing.List[str]]: ...


    def __init__(self, xmlReader: PySide6.QtCore.QXmlStreamReader, /) -> None: ...

    def compile(self, /) -> PySide6.QtScxml.QScxmlStateMachine: ...
    def errors(self, /) -> typing.List[PySide6.QtScxml.QScxmlError]: ...
    def fileName(self, /) -> str: ...
    def loader(self, /) -> PySide6.QtScxml.QScxmlCompiler.Loader: ...
    def setFileName(self, fileName: str, /) -> None: ...
    def setLoader(self, newLoader: PySide6.QtScxml.QScxmlCompiler.Loader, /) -> None: ...


class QScxmlCppDataModel(PySide6.QtScxml.QScxmlDataModel):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def evaluateAssignment(self, id: int, /) -> bool: ...
    def evaluateForeach(self, id: int, body: PySide6.QtScxml.QScxmlDataModel.ForeachLoopBody, /) -> bool: ...
    def evaluateInitialization(self, id: int, /) -> bool: ...
    def hasScxmlProperty(self, name: str, /) -> bool: ...
    def inState(self, stateName: str, /) -> bool: ...
    def scxmlEvent(self, /) -> PySide6.QtScxml.QScxmlEvent: ...
    def scxmlProperty(self, name: str, /) -> typing.Any: ...
    def setScxmlEvent(self, scxmlEvent: PySide6.QtScxml.QScxmlEvent, /) -> None: ...
    def setScxmlProperty(self, name: str, value: typing.Any, context: str, /) -> bool: ...
    def setup(self, initialDataValues: typing.Dict[str, typing.Any], /) -> bool: ...


class QScxmlDataModel(PySide6.QtCore.QObject):

    stateMachineChanged      : typing.ClassVar[Signal] = ... # stateMachineChanged(QScxmlStateMachine*)

    class ForeachLoopBody(Shiboken.Object):

        def __init__(self, /) -> None: ...

        def run(self, /) -> bool: ...


    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, stateMachine: PySide6.QtScxml.QScxmlStateMachine | None = ...) -> None: ...

    @staticmethod
    def createScxmlDataModel(pluginKey: str, /) -> PySide6.QtScxml.QScxmlDataModel: ...
    def evaluateAssignment(self, id: int, /) -> bool: ...
    def evaluateForeach(self, id: int, body: PySide6.QtScxml.QScxmlDataModel.ForeachLoopBody, /) -> bool: ...
    def evaluateInitialization(self, id: int, /) -> bool: ...
    def evaluateToBool(self, id: int, /) -> bool: ...
    def evaluateToString(self, id: int, /) -> str: ...
    def evaluateToVariant(self, id: int, /) -> typing.Any: ...
    def evaluateToVoid(self, id: int, /) -> bool: ...
    def hasScxmlProperty(self, name: str, /) -> bool: ...
    def scxmlProperty(self, name: str, /) -> typing.Any: ...
    def setScxmlEvent(self, event: PySide6.QtScxml.QScxmlEvent, /) -> None: ...
    def setScxmlProperty(self, name: str, value: typing.Any, context: str, /) -> bool: ...
    def setStateMachine(self, stateMachine: PySide6.QtScxml.QScxmlStateMachine, /) -> None: ...
    def setup(self, initialDataValues: typing.Dict[str, typing.Any], /) -> bool: ...
    def stateMachine(self, /) -> PySide6.QtScxml.QScxmlStateMachine: ...


class QScxmlDynamicScxmlServiceFactory(PySide6.QtScxml.QScxmlInvokableServiceFactory):

    def __init__(self, invokeInfo: PySide6.QtScxml.QScxmlExecutableContent.InvokeInfo, names: collections.abc.Sequence[int], parameters: collections.abc.Sequence[PySide6.QtScxml.QScxmlExecutableContent.ParameterInfo], /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def invoke(self, parentStateMachine: PySide6.QtScxml.QScxmlStateMachine, /) -> PySide6.QtScxml.QScxmlInvokableService: ...


class QScxmlError(Shiboken.Object):

    @typing.overload
    def __init__(self, arg__1: PySide6.QtScxml.QScxmlError, /, *, valid: bool | None = ..., fileName: str | None = ..., line: int | None = ..., column: int | None = ..., description: str | None = ...) -> None: ...
    @typing.overload
    def __init__(self, fileName: str, line: int, column: int, description: str, /, *, valid: bool | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, valid: bool | None = ..., fileName: str | None = ..., line: int | None = ..., column: int | None = ..., description: str | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def column(self, /) -> int: ...
    def description(self, /) -> str: ...
    def fileName(self, /) -> str: ...
    def isValid(self, /) -> bool: ...
    def line(self, /) -> int: ...
    def toString(self, /) -> str: ...


class QScxmlEvent(Shiboken.Object):

    class EventType(enum.Enum):

        PlatformEvent             = ...  # 0x0
        InternalEvent             = ...  # 0x1
        ExternalEvent             = ...  # 0x2


    @typing.overload
    def __init__(self, other: PySide6.QtScxml.QScxmlEvent, /, *, name: str | None = ..., eventType: PySide6.QtScxml.QScxmlEvent.EventType | None = ..., scxmlType: str | None = ..., sendId: str | None = ..., origin: str | None = ..., originType: str | None = ..., invokeId: str | None = ..., delay: int | None = ..., data: typing.Optional[typing.Any] = ..., errorEvent: bool | None = ..., errorMessage: str | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, name: str | None = ..., eventType: PySide6.QtScxml.QScxmlEvent.EventType | None = ..., scxmlType: str | None = ..., sendId: str | None = ..., origin: str | None = ..., originType: str | None = ..., invokeId: str | None = ..., delay: int | None = ..., data: typing.Optional[typing.Any] = ..., errorEvent: bool | None = ..., errorMessage: str | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def clear(self, /) -> None: ...
    def data(self, /) -> typing.Any: ...
    def delay(self, /) -> int: ...
    def errorMessage(self, /) -> str: ...
    def eventType(self, /) -> PySide6.QtScxml.QScxmlEvent.EventType: ...
    def invokeId(self, /) -> str: ...
    def isErrorEvent(self, /) -> bool: ...
    def name(self, /) -> str: ...
    def origin(self, /) -> str: ...
    def originType(self, /) -> str: ...
    def scxmlType(self, /) -> str: ...
    def sendId(self, /) -> str: ...
    def setData(self, data: typing.Any, /) -> None: ...
    def setDelay(self, delayInMiliSecs: int, /) -> None: ...
    def setErrorMessage(self, message: str, /) -> None: ...
    def setEventType(self, type: PySide6.QtScxml.QScxmlEvent.EventType, /) -> None: ...
    def setInvokeId(self, invokeId: str, /) -> None: ...
    def setName(self, name: str, /) -> None: ...
    def setOrigin(self, origin: str, /) -> None: ...
    def setOriginType(self, originType: str, /) -> None: ...
    def setSendId(self, sendId: str, /) -> None: ...


class QScxmlExecutableContent(Shiboken.Object):

    class AssignmentInfo(Shiboken.Object):

        @typing.overload
        def __init__(self, /) -> None: ...
        @typing.overload
        def __init__(self, AssignmentInfo: PySide6.QtScxml.QScxmlExecutableContent.AssignmentInfo, /) -> None: ...

        def __copy__(self, /) -> typing.Self: ...

    class EvaluatorInfo(Shiboken.Object):

        @typing.overload
        def __init__(self, /) -> None: ...
        @typing.overload
        def __init__(self, EvaluatorInfo: PySide6.QtScxml.QScxmlExecutableContent.EvaluatorInfo, /) -> None: ...

        def __copy__(self, /) -> typing.Self: ...

    class ForeachInfo(Shiboken.Object):

        @typing.overload
        def __init__(self, /) -> None: ...
        @typing.overload
        def __init__(self, ForeachInfo: PySide6.QtScxml.QScxmlExecutableContent.ForeachInfo, /) -> None: ...

        def __copy__(self, /) -> typing.Self: ...

    class InvokeInfo(Shiboken.Object):

        @typing.overload
        def __init__(self, /) -> None: ...
        @typing.overload
        def __init__(self, InvokeInfo: PySide6.QtScxml.QScxmlExecutableContent.InvokeInfo, /) -> None: ...

        def __copy__(self, /) -> typing.Self: ...

    class ParameterInfo(Shiboken.Object):

        @typing.overload
        def __init__(self, /) -> None: ...
        @typing.overload
        def __init__(self, ParameterInfo: PySide6.QtScxml.QScxmlExecutableContent.ParameterInfo, /) -> None: ...

        def __copy__(self, /) -> typing.Self: ...


class QScxmlInvokableService(PySide6.QtCore.QObject):

    def __init__(self, parentStateMachine: PySide6.QtScxml.QScxmlStateMachine, parent: PySide6.QtScxml.QScxmlInvokableServiceFactory, /, *, id: str | None = ..., name: str | None = ...) -> None: ...

    def id(self, /) -> str: ...
    def name(self, /) -> str: ...
    def parentStateMachine(self, /) -> PySide6.QtScxml.QScxmlStateMachine: ...
    def postEvent(self, event: PySide6.QtScxml.QScxmlEvent, /) -> None: ...
    def start(self, /) -> bool: ...


class QScxmlInvokableServiceFactory(PySide6.QtCore.QObject):

    def __init__(self, invokeInfo: PySide6.QtScxml.QScxmlExecutableContent.InvokeInfo, names: collections.abc.Sequence[int], parameters: collections.abc.Sequence[PySide6.QtScxml.QScxmlExecutableContent.ParameterInfo], /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def invoke(self, parentStateMachine: PySide6.QtScxml.QScxmlStateMachine, /) -> PySide6.QtScxml.QScxmlInvokableService: ...
    def invokeInfo(self, /) -> PySide6.QtScxml.QScxmlExecutableContent.InvokeInfo: ...
    def names(self, /) -> typing.List[int]: ...
    def parameters(self, /) -> typing.List[PySide6.QtScxml.QScxmlExecutableContent.ParameterInfo]: ...


class QScxmlNullDataModel(PySide6.QtScxml.QScxmlDataModel):

    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def evaluateAssignment(self, id: int, /) -> bool: ...
    def evaluateForeach(self, id: int, body: PySide6.QtScxml.QScxmlDataModel.ForeachLoopBody, /) -> bool: ...
    def evaluateInitialization(self, id: int, /) -> bool: ...
    def evaluateToBool(self, id: int, /) -> bool: ...
    def evaluateToString(self, id: int, /) -> str: ...
    def evaluateToVariant(self, id: int, /) -> typing.Any: ...
    def evaluateToVoid(self, id: int, /) -> bool: ...
    def hasScxmlProperty(self, name: str, /) -> bool: ...
    def scxmlProperty(self, name: str, /) -> typing.Any: ...
    def setScxmlEvent(self, event: PySide6.QtScxml.QScxmlEvent, /) -> None: ...
    def setScxmlProperty(self, name: str, value: typing.Any, context: str, /) -> bool: ...
    def setup(self, initialDataValues: typing.Dict[str, typing.Any], /) -> bool: ...


class QScxmlStateMachine(PySide6.QtCore.QObject):

    dataModelChanged         : typing.ClassVar[Signal] = ... # dataModelChanged(QScxmlDataModel*)
    finished                 : typing.ClassVar[Signal] = ... # finished()
    initialValuesChanged     : typing.ClassVar[Signal] = ... # initialValuesChanged(QVariantMap)
    initializedChanged       : typing.ClassVar[Signal] = ... # initializedChanged(bool)
    invokedServicesChanged   : typing.ClassVar[Signal] = ... # invokedServicesChanged(QList<QScxmlInvokableService*>)
    loaderChanged            : typing.ClassVar[Signal] = ... # loaderChanged(QScxmlCompiler::Loader*)
    log                      : typing.ClassVar[Signal] = ... # log(QString,QString)
    reachedStableState       : typing.ClassVar[Signal] = ... # reachedStableState()
    runningChanged           : typing.ClassVar[Signal] = ... # runningChanged(bool)
    tableDataChanged         : typing.ClassVar[Signal] = ... # tableDataChanged(QScxmlTableData*)

    def __init__(self, metaObject: PySide6.QtCore.QMetaObject, /, parent: PySide6.QtCore.QObject | None = ..., *, running: bool | None = ..., initialized: bool | None = ..., dataModel: PySide6.QtScxml.QScxmlDataModel | None = ..., initialValues: typing.Optional[typing.Dict[str, typing.Any]] = ..., invokedServices: collections.abc.Sequence[PySide6.QtScxml.QScxmlInvokableService] | None = ..., sessionId: str | None = ..., name: str | None = ..., invoked: bool | None = ..., parseErrors: collections.abc.Sequence[PySide6.QtScxml.QScxmlError] | None = ..., loader: PySide6.QtScxml.QScxmlCompiler.Loader | None = ..., tableData: PySide6.QtScxml.QScxmlTableData | None = ...) -> None: ...

    def activeStateNames(self, /, compress: bool = ...) -> typing.List[str]: ...
    def cancelDelayedEvent(self, sendId: str, /) -> None: ...
    def connectToEvent(self, scxmlEventSpec: str, receiver: PySide6.QtCore.QObject, method: bytes | bytearray | memoryview, /, type: PySide6.QtCore.Qt.ConnectionType = ...) -> PySide6.QtCore.QMetaObject.Connection: ...
    def connectToState(self, scxmlStateName: str, receiver: PySide6.QtCore.QObject, method: bytes | bytearray | memoryview, /, type: PySide6.QtCore.Qt.ConnectionType = ...) -> PySide6.QtCore.QMetaObject.Connection: ...
    def dataModel(self, /) -> PySide6.QtScxml.QScxmlDataModel: ...
    @staticmethod
    def fromData(data: PySide6.QtCore.QIODevice, /, fileName: str = ...) -> PySide6.QtScxml.QScxmlStateMachine: ...
    @staticmethod
    def fromFile(fileName: str, /) -> PySide6.QtScxml.QScxmlStateMachine: ...
    def init(self, /) -> bool: ...
    def initialValues(self, /) -> typing.Dict[str, typing.Any]: ...
    def invokedServices(self, /) -> typing.List[PySide6.QtScxml.QScxmlInvokableService]: ...
    @typing.overload
    def isActive(self, scxmlStateName: str, /) -> bool: ...
    @typing.overload
    def isActive(self, stateIndex: int, /) -> bool: ...
    def isDispatchableTarget(self, target: str, /) -> bool: ...
    def isInitialized(self, /) -> bool: ...
    def isInvoked(self, /) -> bool: ...
    def isRunning(self, /) -> bool: ...
    def loader(self, /) -> PySide6.QtScxml.QScxmlCompiler.Loader: ...
    def name(self, /) -> str: ...
    def parseErrors(self, /) -> typing.List[PySide6.QtScxml.QScxmlError]: ...
    def sessionId(self, /) -> str: ...
    def setDataModel(self, model: PySide6.QtScxml.QScxmlDataModel, /) -> None: ...
    def setInitialValues(self, initialValues: typing.Dict[str, typing.Any], /) -> None: ...
    def setLoader(self, loader: PySide6.QtScxml.QScxmlCompiler.Loader, /) -> None: ...
    def setRunning(self, running: bool, /) -> None: ...
    def setTableData(self, tableData: PySide6.QtScxml.QScxmlTableData, /) -> None: ...
    def start(self, /) -> None: ...
    def stateNames(self, /, compress: bool = ...) -> typing.List[str]: ...
    def stop(self, /) -> None: ...
    @typing.overload
    def submitEvent(self, event: PySide6.QtScxml.QScxmlEvent, /) -> None: ...
    @typing.overload
    def submitEvent(self, eventName: str, /) -> None: ...
    @typing.overload
    def submitEvent(self, eventName: str, data: typing.Any, /) -> None: ...
    def tableData(self, /) -> PySide6.QtScxml.QScxmlTableData: ...


class QScxmlStaticScxmlServiceFactory(PySide6.QtScxml.QScxmlInvokableServiceFactory):

    def __init__(self, metaObject: PySide6.QtCore.QMetaObject, invokeInfo: PySide6.QtScxml.QScxmlExecutableContent.InvokeInfo, nameList: collections.abc.Sequence[int], parameters: collections.abc.Sequence[PySide6.QtScxml.QScxmlExecutableContent.ParameterInfo], /, parent: PySide6.QtCore.QObject | None = ...) -> None: ...

    def invoke(self, parentStateMachine: PySide6.QtScxml.QScxmlStateMachine, /) -> PySide6.QtScxml.QScxmlInvokableService: ...


class QScxmlTableData(Shiboken.Object):

    def __init__(self, /) -> None: ...

    def assignmentInfo(self, assignmentId: int, /) -> PySide6.QtScxml.QScxmlExecutableContent.AssignmentInfo: ...
    def dataNames(self, /) -> typing.Tuple[typing.List[int], int]: ...
    def evaluatorInfo(self, evaluatorId: int, /) -> PySide6.QtScxml.QScxmlExecutableContent.EvaluatorInfo: ...
    def foreachInfo(self, foreachId: int, /) -> PySide6.QtScxml.QScxmlExecutableContent.ForeachInfo: ...
    def initialSetup(self, /) -> int: ...
    def instructions(self, /) -> typing.List[int]: ...
    def name(self, /) -> str: ...
    def serviceFactory(self, id: int, /) -> PySide6.QtScxml.QScxmlInvokableServiceFactory: ...
    def stateMachineTable(self, /) -> typing.List[int]: ...
    def string(self, id: int, /) -> str: ...


# eof
