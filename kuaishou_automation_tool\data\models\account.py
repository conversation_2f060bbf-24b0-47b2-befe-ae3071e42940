"""
快手无人带货工具 - 账号数据模型
Kuaishou Automation Tool - Account Data Model

本模块定义账号相关的数据模型，包括：
- 账号基本信息模型
- 账号状态枚举
- 账号验证方法
"""

from sqlalchemy import Column, String, DateTime, Text, Enum, Integer
from sqlalchemy.orm import validates
from datetime import datetime
import enum
import re
from .base import BaseModel


class AccountStatus(enum.Enum):
    """
    账号状态枚举
    
    定义账号的各种状态类型。
    """
    ONLINE = "在线"
    OFFLINE = "离线"
    SUSPENDED = "已封禁"
    PENDING = "待验证"
    ERROR = "异常"


class AccountType(enum.Enum):
    """
    账号类型枚举
    
    定义账号的类型分类。
    """
    MAIN = "主账号"
    BACKUP = "备用账号"
    TEST = "测试账号"


class Account(BaseModel):
    """
    账号数据模型
    
    存储快手账号的基本信息和状态数据。
    
    Attributes:
        username (str): 用户名，唯一标识
        nickname (str): 昵称
        phone (str): 绑定手机号
        email (str): 绑定邮箱
        password_hash (str): 密码哈希值
        status (AccountStatus): 账号状态
        account_type (AccountType): 账号类型
        last_login_time (datetime): 最后登录时间
        last_login_ip (str): 最后登录IP
        login_count (int): 登录次数
        remarks (str): 备注信息
        avatar_url (str): 头像URL
        follower_count (int): 粉丝数量
        following_count (int): 关注数量
        video_count (int): 视频数量
        like_count (int): 获赞数量
    """
    
    __tablename__ = 'accounts'
    
    # 基本信息
    username = Column(String(50), unique=True, nullable=False, index=True)
    nickname = Column(String(100), nullable=False)
    phone = Column(String(20), nullable=True, index=True)  # 可选字段，用于Cookie文件命名
    email = Column(String(100), nullable=True)
    password_hash = Column(String(255), nullable=True)

    # 自动化相关信息
    cookie_file_path = Column(String(500), nullable=True)  # Cookie文件路径
    last_automation_time = Column(DateTime, nullable=True)  # 最后自动化操作时间
    automation_status = Column(String(50), default="未配置", nullable=False)  # 自动化状态
    
    # 状态信息
    status = Column(Enum(AccountStatus), default=AccountStatus.OFFLINE, nullable=False)
    account_type = Column(Enum(AccountType), default=AccountType.MAIN, nullable=False)
    last_login_time = Column(DateTime, nullable=True)
    last_login_ip = Column(String(45), nullable=True)  # 支持IPv6
    login_count = Column(Integer, default=0, nullable=False)
    
    # 扩展信息
    remarks = Column(Text, nullable=True)
    avatar_url = Column(String(500), nullable=True)
    
    # 统计信息
    follower_count = Column(Integer, default=0, nullable=False)
    following_count = Column(Integer, default=0, nullable=False)
    video_count = Column(Integer, default=0, nullable=False)
    like_count = Column(Integer, default=0, nullable=False)
    
    @validates('username')
    def validate_username(self, key, username):
        """
        验证用户名格式

        Args:
            key: 字段名
            username: 用户名值

        Returns:
            str: 验证后的用户名

        Raises:
            ValueError: 用户名格式不正确
        """
        if not username:
            raise ValueError("用户名不能为空")

        # 放宽用户名长度限制，允许手机号作为用户名
        if len(username) > 50:
            raise ValueError("用户名长度不能超过50个字符")

        # 放宽用户名字符限制，允许更多字符（包括手机号常用字符）
        # 只禁止明显的特殊字符，允许字母、数字、下划线、连字符、点号
        if re.search(r'[<>"\'\\\s]', username):
            raise ValueError("用户名不能包含空格和特殊字符 < > \" ' \\")

        return username.strip()
        
    @validates('nickname')
    def validate_nickname(self, key, nickname):
        """
        验证昵称格式
        
        Args:
            key: 字段名
            nickname: 昵称值
            
        Returns:
            str: 验证后的昵称
            
        Raises:
            ValueError: 昵称格式不正确
        """
        if not nickname:
            raise ValueError("昵称不能为空")
            
        if len(nickname) > 100:
            raise ValueError("昵称长度不能超过100个字符")
            
        return nickname.strip()
        
    @validates('phone')
    def validate_phone(self, key, phone):
        """
        验证手机号格式

        Args:
            key: 字段名
            phone: 手机号值

        Returns:
            str: 验证后的手机号

        Raises:
            ValueError: 手机号格式不正确
        """
        # 手机号现在是可选的，可以为空
        if phone is None or phone == "":
            return phone

        # 如果提供了手机号，不做格式验证，允许任意格式
        return phone
        
    @validates('email')
    def validate_email(self, key, email):
        """
        验证邮箱格式
        
        Args:
            key: 字段名
            email: 邮箱值
            
        Returns:
            str: 验证后的邮箱
            
        Raises:
            ValueError: 邮箱格式不正确
        """
        if email:
            # 简单的邮箱验证
            if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
                raise ValueError("邮箱格式不正确")
                
        return email
        
    def update_login_info(self, ip_address=None):
        """
        更新登录信息

        Args:
            ip_address (str, optional): 登录IP地址
        """
        self.last_login_time = datetime.now()
        if self.login_count is None:
            self.login_count = 0
        self.login_count += 1
        if ip_address:
            self.last_login_ip = ip_address
        self.status = AccountStatus.ONLINE
        
    def set_offline(self):
        """设置账号为离线状态"""
        self.status = AccountStatus.OFFLINE
        
    def set_suspended(self, reason=None):
        """
        设置账号为封禁状态
        
        Args:
            reason (str, optional): 封禁原因
        """
        self.status = AccountStatus.SUSPENDED
        if reason:
            current_remarks = self.remarks or ""
            self.remarks = f"{current_remarks}\n封禁原因: {reason}".strip()
            
    def is_online(self):
        """
        检查账号是否在线
        
        Returns:
            bool: 是否在线
        """
        return self.status == AccountStatus.ONLINE
        
    def is_available(self):
        """
        检查账号是否可用
        
        Returns:
            bool: 是否可用（非封禁状态）
        """
        return self.status not in [AccountStatus.SUSPENDED, AccountStatus.ERROR]
        
    def get_status_display(self):
        """
        获取状态显示文本
        
        Returns:
            str: 状态显示文本
        """
        return self.status.value if self.status else "未知"
        
    def get_type_display(self):
        """
        获取类型显示文本
        
        Returns:
            str: 类型显示文本
        """
        return self.account_type.value if self.account_type else "未知"
        
    def get_last_login_display(self):
        """
        获取最后登录时间显示文本

        Returns:
            str: 最后登录时间显示文本
        """
        if self.last_login_time:
            return self.last_login_time.strftime("%Y-%m-%d %H:%M:%S")
        else:
            return "从未登录"

    def get_cookie_filename(self):
        """
        获取Cookie文件名

        Returns:
            str: Cookie文件名（基于手机号或用户名）
        """
        if self.phone and self.phone.strip():
            return f"{self.phone}.txt"
        else:
            # 如果没有手机号，使用用户名作为文件名
            return f"{self.username}.txt"

    def update_automation_info(self, cookie_path=None, status="已配置"):
        """
        更新自动化相关信息

        Args:
            cookie_path (str, optional): Cookie文件路径
            status (str): 自动化状态
        """
        self.last_automation_time = datetime.now()
        self.automation_status = status
        if cookie_path:
            self.cookie_file_path = cookie_path

    def has_valid_cookies(self):
        """
        检查是否有有效的Cookie文件

        Returns:
            bool: 是否有有效Cookie
        """
        if not self.cookie_file_path:
            return False

        import os
        return os.path.exists(self.cookie_file_path) and os.path.getsize(self.cookie_file_path) > 0

    def get_automation_status_display(self):
        """
        获取自动化状态显示文本

        Returns:
            str: 自动化状态显示文本
        """
        if self.automation_status == "已配置" and self.has_valid_cookies():
            return "✅ 已配置"
        elif self.automation_status == "配置中":
            return "🔄 配置中"
        elif self.automation_status == "配置失败":
            return "❌ 配置失败"
        else:
            return "⚪ 未配置"
            
    def to_dict(self):
        """
        转换为字典格式
        
        Returns:
            dict: 账号信息字典
        """
        data = super().to_dict()
        
        # 添加显示字段
        data['status_display'] = self.get_status_display()
        data['type_display'] = self.get_type_display()
        data['last_login_display'] = self.get_last_login_display()
        
        # 移除敏感信息
        if 'password_hash' in data:
            del data['password_hash']
            
        return data
        
    def __str__(self):
        """返回账号的字符串表示"""
        return f"{self.nickname}({self.username})"
        
    def __repr__(self):
        """返回账号的详细字符串表示"""
        return f"<Account(username='{self.username}', nickname='{self.nickname}', status='{self.get_status_display()}')>"


class AccountStatistics:
    """
    账号统计信息类
    
    提供账号相关的统计方法。
    """
    
    @staticmethod
    def get_status_count(session):
        """
        获取各状态账号数量统计
        
        Args:
            session: 数据库会话
            
        Returns:
            dict: 状态统计字典
        """
        from sqlalchemy import func
        
        result = session.query(
            Account.status,
            func.count(Account.id).label('count')
        ).group_by(Account.status).all()
        
        stats = {status.value: 0 for status in AccountStatus}
        for status, count in result:
            if status:
                stats[status.value] = count
                
        return stats
        
    @staticmethod
    def get_type_count(session):
        """
        获取各类型账号数量统计
        
        Args:
            session: 数据库会话
            
        Returns:
            dict: 类型统计字典
        """
        from sqlalchemy import func
        
        result = session.query(
            Account.account_type,
            func.count(Account.id).label('count')
        ).group_by(Account.account_type).all()
        
        stats = {account_type.value: 0 for account_type in AccountType}
        for account_type, count in result:
            if account_type:
                stats[account_type.value] = count
                
        return stats
        
    @staticmethod
    def get_total_count(session):
        """
        获取账号总数
        
        Args:
            session: 数据库会话
            
        Returns:
            int: 账号总数
        """
        return session.query(Account).filter(Account.is_active == True).count()
