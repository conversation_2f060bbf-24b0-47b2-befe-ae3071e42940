#!/usr/bin/env python3
"""
图文视频合成界面组件
"""

import os
import sys
import threading
from typing import List, Optional

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

# 动态导入Qt库
try:
    from PySide6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
        QTextEdit, QListWidget, QListWidgetItem, QProgressBar,
        QGroupBox, QSpinBox, QDoubleSpinBox, QComboBox,
        QFileDialog, QMessageBox, QScrollArea, QGridLayout,
        QCheckBox, QSlider, QTabWidget
    )
    from PySide6.QtCore import Qt, QTimer, Signal as pyqtSignal
    from PySide6.QtGui import QPixmap, QFont
except ImportError:
    try:
        from PyQt6.QtWidgets import (
            QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
            QTextEdit, QListWidget, QListWidgetItem, QProgressBar,
            QGroupBox, QSpinBox, QDoubleSpinBox, QComboBox,
            QFileDialog, QMessageBox, QScrollArea, QGridLayout,
            QCheckBox, QSlider, QTabWidget
        )
        from PyQt6.QtCore import Qt, QTimer, pyqtSignal
        from PyQt6.QtGui import QPixmap, QFont
    except ImportError:
        from PyQt5.QtWidgets import (
            QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
            QTextEdit, QListWidget, QListWidgetItem, QProgressBar,
            QGroupBox, QSpinBox, QDoubleSpinBox, QComboBox,
            QFileDialog, QMessageBox, QScrollArea, QGridLayout,
            QCheckBox, QSlider, QTabWidget
        )
        from PyQt5.QtCore import Qt, QTimer, pyqtSignal
        from PyQt5.QtGui import QPixmap, QFont

try:
    from business.video_composer import VideoComposer
except ImportError:
    # 如果导入失败，创建一个简单的替代类
    class VideoComposer:
        def create_video_from_images_and_text(self, *args, **kwargs):
            return False

class VideoComposerWidget(QWidget):
    """图文视频合成界面"""

    # 定义信号
    video_generation_finished = pyqtSignal(bool)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.video_composer = VideoComposer()
        self.selected_images = []

        # 连接信号
        self.video_generation_finished.connect(self._on_generation_complete)

        # 设置文件路径
        self.settings_file = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'video_settings.json')

        self.init_ui()
        self.load_settings()  # 加载保存的设置
        self.connect_auto_save()  # 连接自动保存信号
        self.ensure_default_settings()  # 确保默认设置被保存
        
    def init_ui(self):
        """初始化界面"""
        main_layout = QVBoxLayout(self)
        
        # 创建标题
        title_label = QLabel("🎬 图文视频合成工具")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(title_label)
        
        # 创建选项卡
        tab_widget = QTabWidget()

        # 基础设置选项卡
        basic_tab = self.create_basic_tab()
        tab_widget.addTab(basic_tab, "📝 基础设置")

        # 选品设置选项卡
        product_selection_tab = self.create_product_selection_tab()
        tab_widget.addTab(product_selection_tab, "🛍️ 选品设置")

        # 高级设置选项卡
        advanced_tab = self.create_advanced_tab()
        tab_widget.addTab(advanced_tab, "⚙️ 高级设置")

        main_layout.addWidget(tab_widget)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # 生成按钮
        self.generate_button = QPushButton("🎬 生成视频")
        self.generate_button.setFixedHeight(50)
        self.generate_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ff6b6b, stop:1 #ee5a52);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ff8e8e, stop:1 #ff6b6b);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ee5a52, stop:1 #e74c3c);
            }
        """)
        self.generate_button.clicked.connect(self.generate_video)
        main_layout.addWidget(self.generate_button)
        
    def create_basic_tab(self) -> QWidget:
        """创建基础设置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 图片选择区域
        image_group = QGroupBox("📷 选择图片")
        image_layout = QVBoxLayout(image_group)
        
        # 图片选择按钮
        select_images_btn = QPushButton("📁 选择图片文件")
        select_images_btn.clicked.connect(self.select_images)
        image_layout.addWidget(select_images_btn)
        
        # 图片列表
        self.image_list = QListWidget()
        self.image_list.setMaximumHeight(150)
        image_layout.addWidget(self.image_list)
        
        layout.addWidget(image_group)
        
        # 文字内容区域
        text_group = QGroupBox("📝 文字内容（讲解文本）")
        text_layout = QVBoxLayout(text_group)
        
        self.text_edit = QTextEdit()
        self.text_edit.setPlaceholderText(
            "请输入讲解文本，例如：\n\n"
            "欢迎来到我们的直播间！今天为大家推荐这款超值好物。\n"
            "这个产品有以下几个特点：质量优良、价格实惠、性价比超高。\n"
            "现在下单还有特别优惠，机会难得，不要错过！"
        )
        self.text_edit.setMinimumHeight(120)
        text_layout.addWidget(self.text_edit)

        # 添加文本清理预览按钮
        preview_button = QPushButton("🔍 预览清理后的文本")
        preview_button.setStyleSheet("""
            QPushButton {
                background-color: #f0f0f0;
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 5px 10px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
        """)
        preview_button.clicked.connect(self.preview_cleaned_text)
        text_layout.addWidget(preview_button)
        
        layout.addWidget(text_group)
        
        # 输出设置
        output_group = QGroupBox("💾 输出设置")
        output_layout = QVBoxLayout(output_group)
        
        # 输出路径
        output_path_layout = QHBoxLayout()
        self.output_path_label = QLabel("选择输出文件...")
        select_output_btn = QPushButton("📁 选择")
        select_output_btn.clicked.connect(self.select_output_path)
        output_path_layout.addWidget(self.output_path_label)
        output_path_layout.addWidget(select_output_btn)
        output_layout.addLayout(output_path_layout)
        
        layout.addWidget(output_group)
        
        return widget
        
    def create_advanced_tab(self) -> QWidget:
        """创建高级设置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 视频参数设置
        video_group = QGroupBox("🎥 视频参数")
        video_layout = QGridLayout(video_group)
        
        # 视频尺寸
        video_layout.addWidget(QLabel("视频尺寸:"), 0, 0)
        self.size_combo = QComboBox()
        self.size_combo.addItems([
            "1080x1920 (竖屏)",
            "1920x1080 (横屏)",
            "720x1280 (竖屏HD)",
            "1280x720 (横屏HD)"
        ])
        video_layout.addWidget(self.size_combo, 0, 1)
        
        # 帧率
        video_layout.addWidget(QLabel("帧率 (FPS):"), 1, 0)
        self.fps_spin = QSpinBox()
        self.fps_spin.setRange(15, 60)
        self.fps_spin.setValue(30)
        video_layout.addWidget(self.fps_spin, 1, 1)
        
        # 图片显示时间
        video_layout.addWidget(QLabel("每张图片显示时间 (秒):"), 2, 0)
        self.duration_spin = QDoubleSpinBox()
        self.duration_spin.setRange(1.0, 10.0)
        self.duration_spin.setValue(3.0)
        self.duration_spin.setSingleStep(0.5)
        video_layout.addWidget(self.duration_spin, 2, 1)
        
        layout.addWidget(video_group)
        
        # 语音参数设置
        voice_group = QGroupBox("🎤 语音参数")
        voice_layout = QGridLayout(voice_group)

        # 语音引擎选择
        voice_layout.addWidget(QLabel("语音引擎:"), 0, 0)
        self.voice_engine_combo = QComboBox()
        self.voice_engine_combo.addItems([
            "自动选择（推荐）",
            "Edge TTS（免费，质量好）",
            "Google TTS（在线）",
            "Windows SAPI（离线）",
            "pyttsx3（离线备用）"
        ])
        voice_layout.addWidget(self.voice_engine_combo, 0, 1)

        # 语音角色选择
        voice_layout.addWidget(QLabel("语音角色:"), 1, 0)
        self.voice_character_combo = QComboBox()
        self.voice_character_combo.addItems([
            "晓晓 - 女声（温柔甜美）",
            "云希 - 男声（成熟稳重）",
            "晓伊 - 女声（活泼可爱）",
            "云健 - 男声（年轻阳光）",
            "晓辰 - 女声（知性优雅）"
        ])
        voice_layout.addWidget(self.voice_character_combo, 1, 1)
        
        # 语音速度
        voice_layout.addWidget(QLabel("语音速度:"), 2, 0)
        self.speed_slider = QSlider(Qt.Orientation.Horizontal)
        self.speed_slider.setRange(50, 200)
        self.speed_slider.setValue(100)
        self.speed_label = QLabel("1.0x")
        self.speed_slider.valueChanged.connect(
            lambda v: self.speed_label.setText(f"{v/100:.1f}x")
        )
        speed_layout = QHBoxLayout()
        speed_layout.addWidget(self.speed_slider)
        speed_layout.addWidget(self.speed_label)
        voice_layout.addLayout(speed_layout, 2, 1)

        # 语音音量
        voice_layout.addWidget(QLabel("语音音量:"), 3, 0)
        self.volume_slider = QSlider(Qt.Orientation.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(80)
        self.volume_label = QLabel("80%")
        self.volume_slider.valueChanged.connect(
            lambda v: self.volume_label.setText(f"{v}%")
        )
        volume_layout = QHBoxLayout()
        volume_layout.addWidget(self.volume_slider)
        volume_layout.addWidget(self.volume_label)
        voice_layout.addLayout(volume_layout, 3, 1)
        
        layout.addWidget(voice_group)
        
        # 字幕设置
        subtitle_group = QGroupBox("📄 字幕设置")
        subtitle_layout = QGridLayout(subtitle_group)

        # 启用字幕
        self.enable_subtitles = QCheckBox("启用字幕")
        self.enable_subtitles.setChecked(True)
        subtitle_layout.addWidget(self.enable_subtitles, 0, 0, 1, 2)

        # 字幕位置
        subtitle_layout.addWidget(QLabel("字幕位置:"), 1, 0)
        self.subtitle_position_combo = QComboBox()
        self.subtitle_position_combo.addItems([
            "底部居中",
            "顶部居中",
            "中间居中",
            "底部左对齐",
            "底部右对齐"
        ])
        subtitle_layout.addWidget(self.subtitle_position_combo, 1, 1)

        # 字幕背景样式
        subtitle_layout.addWidget(QLabel("背景样式:"), 2, 0)
        self.subtitle_bg_combo = QComboBox()
        self.subtitle_bg_combo.addItems([
            "半透明黑色背景",
            "纯色黑色背景",
            "无背景",
            "白色半透明背景",
            "彩色渐变背景"
        ])
        subtitle_layout.addWidget(self.subtitle_bg_combo, 2, 1)

        # 字体大小
        subtitle_layout.addWidget(QLabel("字体大小:"), 3, 0)
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(20, 100)
        self.font_size_spin.setValue(48)
        self.font_size_spin.setSuffix("px")
        subtitle_layout.addWidget(self.font_size_spin, 3, 1)

        # 字体颜色
        subtitle_layout.addWidget(QLabel("字体颜色:"), 4, 0)
        self.font_color_combo = QComboBox()
        self.font_color_combo.addItems([
            "白色",
            "黄色",
            "红色",
            "蓝色",
            "绿色",
            "黑色"
        ])
        subtitle_layout.addWidget(self.font_color_combo, 4, 1)
        
        layout.addWidget(subtitle_group)

        # 设置管理按钮
        settings_group = QGroupBox("⚙️ 设置管理")
        settings_layout = QHBoxLayout(settings_group)

        # 重置设置按钮
        reset_button = QPushButton("🔄 恢复默认设置")
        reset_button.setStyleSheet("""
            QPushButton {
                background-color: #ff9800;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #f57c00;
            }
            QPushButton:pressed {
                background-color: #ef6c00;
            }
        """)
        reset_button.clicked.connect(self.reset_settings)
        settings_layout.addWidget(reset_button)

        # 导出设置按钮
        export_button = QPushButton("📤 导出设置")
        export_button.setStyleSheet("""
            QPushButton {
                background-color: #4caf50;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)
        export_button.clicked.connect(self.export_settings)
        settings_layout.addWidget(export_button)

        # 导入设置按钮
        import_button = QPushButton("📥 导入设置")
        import_button.setStyleSheet("""
            QPushButton {
                background-color: #2196f3;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976d2;
            }
            QPushButton:pressed {
                background-color: #1565c0;
            }
        """)
        import_button.clicked.connect(self.import_settings)
        settings_layout.addWidget(import_button)

        settings_layout.addStretch()  # 添加弹性空间

        layout.addWidget(settings_group)

        return widget

    def create_product_selection_tab(self) -> QWidget:
        """创建选品设置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)

        # 商品类目选择
        category_group = QGroupBox("📂 商品类目")
        category_layout = QVBoxLayout(category_group)

        # 主要类目按钮
        main_categories_layout = QHBoxLayout()
        self.category_buttons = {}
        main_categories = [
            ("all", "全部"),
            ("food_drink", "食品饮料"),
            ("home", "家居用品"),
            ("women_fashion", "女装女鞋"),
            ("beauty", "美妆护肤"),
            ("personal_care", "个护清洁")
        ]

        for category_id, category_name in main_categories:
            btn = QPushButton(category_name)
            btn.setCheckable(True)
            btn.setObjectName("categoryButton")
            btn.setStyleSheet("""
                QPushButton#categoryButton {
                    background-color: #f5f5f5;
                    color: #333333;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    padding: 8px 16px;
                    font-size: 12px;
                    font-weight: bold;
                    min-width: 80px;
                }
                QPushButton#categoryButton:checked {
                    background-color: #ff6b35;
                    color: white;
                    border-color: #ff6b35;
                }
                QPushButton#categoryButton:hover {
                    background-color: #e0e0e0;
                    color: #333333;
                }
                QPushButton#categoryButton:checked:hover {
                    background-color: #ff8555;
                    color: white;
                }
            """)
            btn.clicked.connect(lambda checked, cid=category_id: self.on_category_selected(cid))
            self.category_buttons[category_id] = btn
            main_categories_layout.addWidget(btn)

        # 设置默认选中"全部"
        self.category_buttons["all"].setChecked(True)
        main_categories_layout.addStretch()
        category_layout.addLayout(main_categories_layout)

        # 次要类目按钮
        sub_categories_layout = QHBoxLayout()
        sub_categories = [
            ("medical", "医疗保健"),
            ("baby", "母婴玩具"),
            ("tea_fresh", "茶酒生鲜"),
            ("men_fashion", "男装男鞋"),
            ("sports", "运动户外"),
            ("more", "更多")
        ]

        for category_id, category_name in sub_categories:
            btn = QPushButton(category_name)
            btn.setCheckable(True)
            btn.setObjectName("categoryButton")
            btn.setStyleSheet("""
                QPushButton#categoryButton {
                    background-color: #f5f5f5;
                    color: #333333;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    padding: 8px 16px;
                    font-size: 12px;
                    font-weight: bold;
                    min-width: 80px;
                }
                QPushButton#categoryButton:checked {
                    background-color: #ff6b35;
                    color: white;
                    border-color: #ff6b35;
                }
                QPushButton#categoryButton:hover {
                    background-color: #e0e0e0;
                    color: #333333;
                }
                QPushButton#categoryButton:checked:hover {
                    background-color: #ff8555;
                    color: white;
                }
            """)
            btn.clicked.connect(lambda checked, cid=category_id: self.on_category_selected(cid))
            self.category_buttons[category_id] = btn
            sub_categories_layout.addWidget(btn)

        sub_categories_layout.addStretch()
        category_layout.addLayout(sub_categories_layout)
        scroll_layout.addWidget(category_group)

        # 商品属性筛选
        attributes_group = QGroupBox("🏷️ 商品属性")
        attributes_layout = QVBoxLayout(attributes_group)

        # 第一行属性
        attr_row1_layout = QHBoxLayout()
        self.attribute_checkboxes = {}

        attributes_row1 = [
            ("burst_plan", "爆品计划"),
            ("brand", "品牌"),
            ("operation_support", "支持运营"),
            ("commission", "佣金"),
            ("return_guarantee", "退货保障"),
            ("good_rating", "好评率")
        ]

        for attr_id, attr_name in attributes_row1:
            checkbox = QCheckBox(attr_name)
            checkbox.setStyleSheet("""
                QCheckBox {
                    font-size: 12px;
                    spacing: 5px;
                }
                QCheckBox::indicator {
                    width: 16px;
                    height: 16px;
                }
                QCheckBox::indicator:unchecked {
                    border: 1px solid #ccc;
                    background-color: white;
                    border-radius: 3px;
                }
                QCheckBox::indicator:checked {
                    border: 1px solid #ff6b35;
                    background-color: #ff6b35;
                    border-radius: 3px;
                }
            """)
            self.attribute_checkboxes[attr_id] = checkbox
            attr_row1_layout.addWidget(checkbox)

        attr_row1_layout.addStretch()
        attributes_layout.addLayout(attr_row1_layout)
        scroll_layout.addWidget(attributes_group)

        # 其他清洁选项
        other_group = QGroupBox("🧹 其他清洁")
        other_layout = QHBoxLayout(other_group)

        other_options = [
            ("clean_option1", "清洁选项1"),
            ("clean_option2", "清洁选项2"),
            ("clean_option3", "清洁选项3"),
            ("clean_option4", "清洁选项4")
        ]

        for option_id, option_name in other_options:
            checkbox = QCheckBox(option_name)
            checkbox.setStyleSheet("""
                QCheckBox {
                    font-size: 12px;
                    spacing: 5px;
                }
                QCheckBox::indicator {
                    width: 16px;
                    height: 16px;
                }
                QCheckBox::indicator:unchecked {
                    border: 1px solid #ccc;
                    background-color: white;
                    border-radius: 3px;
                }
                QCheckBox::indicator:checked {
                    border: 1px solid #ff6b35;
                    background-color: #ff6b35;
                    border-radius: 3px;
                }
            """)
            self.attribute_checkboxes[option_id] = checkbox
            other_layout.addWidget(checkbox)

        other_layout.addStretch()
        scroll_layout.addWidget(other_group)

        # 价格范围设置
        price_group = QGroupBox("💰 价格范围")
        price_layout = QGridLayout(price_group)

        # 最低价格
        price_layout.addWidget(QLabel("最低价格:"), 0, 0)
        self.min_price_spin = QDoubleSpinBox()
        self.min_price_spin.setRange(0.0, 99999.0)
        self.min_price_spin.setValue(0.0)
        self.min_price_spin.setSuffix(" 元")
        self.min_price_spin.setStyleSheet("""
            QDoubleSpinBox {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 5px;
                font-size: 12px;
            }
        """)
        price_layout.addWidget(self.min_price_spin, 0, 1)

        # 最高价格
        price_layout.addWidget(QLabel("最高价格:"), 0, 2)
        self.max_price_spin = QDoubleSpinBox()
        self.max_price_spin.setRange(0.0, 99999.0)
        self.max_price_spin.setValue(999.0)
        self.max_price_spin.setSuffix(" 元")
        self.max_price_spin.setStyleSheet("""
            QDoubleSpinBox {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 5px;
                font-size: 12px;
            }
        """)
        price_layout.addWidget(self.max_price_spin, 0, 3)

        # 销量要求
        price_layout.addWidget(QLabel("最低销量:"), 1, 0)
        self.min_sales_spin = QSpinBox()
        self.min_sales_spin.setRange(0, 999999)
        self.min_sales_spin.setValue(0)
        self.min_sales_spin.setSuffix(" 件")
        self.min_sales_spin.setStyleSheet("""
            QSpinBox {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 5px;
                font-size: 12px;
            }
        """)
        price_layout.addWidget(self.min_sales_spin, 1, 1)

        # 好评率要求
        price_layout.addWidget(QLabel("最低好评率:"), 1, 2)
        self.min_rating_spin = QDoubleSpinBox()
        self.min_rating_spin.setRange(0.0, 100.0)
        self.min_rating_spin.setValue(90.0)
        self.min_rating_spin.setSuffix(" %")
        self.min_rating_spin.setStyleSheet("""
            QDoubleSpinBox {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 5px;
                font-size: 12px;
            }
        """)
        price_layout.addWidget(self.min_rating_spin, 1, 3)

        scroll_layout.addWidget(price_group)

        # 设置滚动区域
        scroll_area.setWidget(scroll_content)
        layout.addWidget(scroll_area)

        return widget

    def on_category_selected(self, category_id: str):
        """处理商品类目选择"""
        # 如果选择"全部"，取消其他所有选择
        if category_id == "all":
            for btn_id, button in self.category_buttons.items():
                if btn_id != "all":
                    button.setChecked(False)
        else:
            # 如果选择其他类目，取消"全部"的选择
            self.category_buttons["all"].setChecked(False)

        # 获取当前选中的类目
        selected_categories = []
        for btn_id, button in self.category_buttons.items():
            if button.isChecked():
                selected_categories.append(btn_id)

        print(f"选中的商品类目: {selected_categories}")

        # 这里可以添加根据类目筛选商品的逻辑
        self.filter_products_by_category(selected_categories)

    def filter_products_by_category(self, categories: List[str]):
        """根据类目筛选商品"""
        # 这里实现根据选中类目筛选商品的逻辑
        # 目前只是打印信息，后续可以连接到实际的商品数据源
        if "all" in categories:
            print("显示所有类目的商品")
        else:
            print(f"筛选类目: {', '.join(categories)}")

    def select_images(self):
        """选择图片文件"""
        file_dialog = QFileDialog()
        file_paths, _ = file_dialog.getOpenFileNames(
            self,
            "选择图片文件",
            "",
            "图片文件 (*.jpg *.jpeg *.png *.bmp *.webp);;所有文件 (*)"
        )

        if file_paths:
            self.selected_images = file_paths
            self.update_image_list()

    def update_image_list(self):
        """更新图片列表显示"""
        self.image_list.clear()
        for i, img_path in enumerate(self.selected_images):
            filename = os.path.basename(img_path)
            item = QListWidgetItem(f"{i+1}. {filename}")
            self.image_list.addItem(item)

    def select_output_path(self):
        """选择输出路径"""
        file_dialog = QFileDialog()

        # 使用上次保存的目录
        default_dir = getattr(self, 'last_output_dir', os.path.expanduser('~/Downloads'))
        default_filename = os.path.join(default_dir, "output_video.mp4")

        file_path, _ = file_dialog.getSaveFileName(
            self,
            "选择输出视频文件",
            default_filename,
            "视频文件 (*.mp4);;所有文件 (*)"
        )

        if file_path:
            self.output_path = file_path
            self.output_path_label.setText(os.path.basename(file_path))

            # 保存目录以便下次使用
            self.last_output_dir = os.path.dirname(file_path)
            self.save_settings()  # 立即保存设置

    def get_video_config(self) -> dict:
        """获取视频配置"""
        # 解析视频尺寸
        size_text = self.size_combo.currentText()
        if "1080x1920" in size_text:
            width, height = 1080, 1920
        elif "1920x1080" in size_text:
            width, height = 1920, 1080
        elif "720x1280" in size_text:
            width, height = 720, 1280
        else:  # 1280x720
            width, height = 1280, 720

        # 获取语音角色映射
        voice_mapping = {
            "晓晓 - 女声（温柔甜美）": "zh-CN-XiaoxiaoNeural",
            "云希 - 男声（成熟稳重）": "zh-CN-YunxiNeural",
            "晓伊 - 女声（活泼可爱）": "zh-CN-XiaoyiNeural",
            "云健 - 男声（年轻阳光）": "zh-CN-YunjianNeural",
            "晓辰 - 女声（知性优雅）": "zh-CN-XiaochenNeural"
        }

        selected_voice = self.voice_character_combo.currentText()
        edge_voice = voice_mapping.get(selected_voice, "zh-CN-XiaoxiaoNeural")

        # 字体颜色映射
        color_mapping = {
            "白色": "white",
            "黄色": "yellow",
            "红色": "red",
            "蓝色": "blue",
            "绿色": "green",
            "黑色": "black"
        }

        # 字幕位置映射
        position_mapping = {
            "底部居中": "bottom_center",
            "顶部居中": "top_center",
            "中间居中": "center",
            "底部左对齐": "bottom_left",
            "底部右对齐": "bottom_right"
        }

        config = {
            'video_width': width,
            'video_height': height,
            'fps': self.fps_spin.value(),
            'image_duration': self.duration_spin.value(),
            'voice_speed': self.speed_slider.value() / 100.0,
            'voice_volume': self.volume_slider.value() / 100.0,
            'enable_subtitles': self.enable_subtitles.isChecked(),
            'voice_engine': self.voice_engine_combo.currentText(),
            'edge_voice': edge_voice,
            'azure_voice': edge_voice.replace('Neural', 'Neural'),
            # 字幕配置
            'subtitle_position': position_mapping.get(self.subtitle_position_combo.currentText(), "bottom_center"),
            'subtitle_background': self.subtitle_bg_combo.currentText(),
            'subtitle_font_size': self.font_size_spin.value(),
            'subtitle_font_color': color_mapping.get(self.font_color_combo.currentText(), "white"),
        }

        return config

    def validate_inputs(self) -> bool:
        """验证输入"""
        if not self.selected_images:
            QMessageBox.warning(self, "警告", "请先选择图片文件！")
            return False

        text_content = self.text_edit.toPlainText().strip()
        if not text_content:
            QMessageBox.warning(self, "警告", "请输入讲解文本！")
            return False

        if not hasattr(self, 'output_path') or not self.output_path:
            QMessageBox.warning(self, "警告", "请选择输出文件路径！")
            return False

        return True

    def generate_video(self):
        """生成视频"""
        if not self.validate_inputs():
            return

        # 禁用生成按钮
        self.generate_button.setEnabled(False)
        self.generate_button.setText("🎬 生成中...")

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不确定进度

        # 在新线程中生成视频
        thread = threading.Thread(
            target=self._generate_video_thread,
            daemon=True
        )
        thread.start()

    def _generate_video_thread(self):
        """在后台线程中生成视频"""
        try:
            text_content = self.text_edit.toPlainText().strip()
            config = self.get_video_config()

            # 调用视频合成器
            success = self.video_composer.create_video_from_images_and_text(
                images=self.selected_images,
                text_content=text_content,
                output_path=self.output_path,
                config=config
            )

            # 发射信号通知主线程
            self.video_generation_finished.emit(success)

        except Exception as e:
            print(f"视频生成失败: {e}")
            import traceback
            traceback.print_exc()
            self.video_generation_finished.emit(False)

    def _on_generation_complete(self, success: bool):
        """视频生成完成回调"""
        # 恢复UI状态
        self.generate_button.setEnabled(True)
        self.generate_button.setText("🎬 生成视频")
        self.progress_bar.setVisible(False)

        if success:
            QMessageBox.information(
                self,
                "成功",
                f"视频生成成功！\n\n文件保存在:\n{self.output_path}"
            )
        else:
            QMessageBox.critical(
                self,
                "失败",
                "视频生成失败！\n\n请检查:\n"
                "1. 图片文件是否有效\n"
                "2. 输出路径是否可写\n"
                "3. 是否安装了必要的依赖库"
            )

    def preview_cleaned_text(self):
        """预览清理后的文本"""
        original_text = self.text_edit.toPlainText().strip()
        if not original_text:
            QMessageBox.warning(self, "提示", "请先输入文本内容！")
            return

        # 使用与video_composer相同的清理逻辑
        cleaned_text = self._clean_text_for_preview(original_text)

        # 显示对比结果
        dialog = QMessageBox(self)
        dialog.setWindowTitle("文本清理预览")
        dialog.setIcon(QMessageBox.Icon.Information)

        message = f"""
📝 原始文本 ({len(original_text)} 字符):
{original_text[:200]}{'...' if len(original_text) > 200 else ''}

🔍 清理后文本 ({len(cleaned_text)} 字符):
{cleaned_text[:200]}{'...' if len(cleaned_text) > 200 else ''}

✨ 清理内容:
• 移除了网址链接
• 移除了邮箱地址
• 移除了电话号码
• 移除了特殊符号
• 移除了长数字串
        """

        dialog.setText(message)
        dialog.exec()

    def _clean_text_for_preview(self, text: str) -> str:
        """清理文本预览（与video_composer保持一致）"""
        import re

        # 移除网址
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        text = re.sub(r'www\.(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)

        # 移除邮箱地址
        text = re.sub(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', '', text)

        # 移除电话号码
        text = re.sub(r'\b\d{3,4}[-.\s]?\d{3,4}[-.\s]?\d{4}\b', '', text)
        text = re.sub(r'\b1[3-9]\d{9}\b', '', text)

        # 移除XML/HTML标签
        text = re.sub(r'<[^>]+>', '', text)

        # 移除XML命名空间和属性
        text = re.sub(r'xmlns\s*=\s*["\'][^"\']*["\']', '', text)
        text = re.sub(r'xml:\w+\s*=\s*["\'][^"\']*["\']', '', text)

        # 移除特殊符号和多余空格
        text = re.sub(r'[#@$%^&*()_+=\[\]{}|\\:";\'<>?,./]', ' ', text)
        text = re.sub(r'\s+', ' ', text)

        # 移除数字串（超过4位的纯数字）
        text = re.sub(r'\b\d{5,}\b', '', text)

        # 清理首尾空格
        text = text.strip()

        return text

    def save_settings(self):
        """保存当前设置到文件"""
        try:
            import json

            # 确保配置目录存在
            config_dir = os.path.dirname(self.settings_file)
            os.makedirs(config_dir, exist_ok=True)

            # 收集当前设置
            settings = {
                'video_size': self.size_combo.currentText(),
                'fps': self.fps_spin.value(),
                'image_duration': self.duration_spin.value(),
                'voice_engine': self.voice_engine_combo.currentText(),
                'voice_character': self.voice_character_combo.currentText(),
                'voice_speed': self.speed_slider.value(),
                'voice_volume': self.volume_slider.value(),
                'enable_subtitles': self.enable_subtitles.isChecked(),
                'subtitle_position': self.subtitle_position_combo.currentText(),
                'subtitle_background': self.subtitle_bg_combo.currentText(),
                'subtitle_font_size': self.font_size_spin.value(),
                'subtitle_font_color': self.font_color_combo.currentText(),
                'last_output_dir': getattr(self, 'last_output_dir', ''),
                # 选品设置
                'selected_categories': [btn_id for btn_id, btn in self.category_buttons.items() if btn.isChecked()],
                'selected_attributes': [attr_id for attr_id, checkbox in self.attribute_checkboxes.items() if checkbox.isChecked()],
                'min_price': self.min_price_spin.value(),
                'max_price': self.max_price_spin.value(),
                'min_sales': self.min_sales_spin.value(),
                'min_rating': self.min_rating_spin.value(),
            }

            # 保存到文件
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)

            print(f"✅ 设置已保存到: {self.settings_file}")

        except Exception as e:
            print(f"❌ 保存设置失败: {e}")

    def load_settings(self):
        """从文件加载设置"""
        try:
            import json

            if not os.path.exists(self.settings_file):
                print("📝 首次使用，将使用默认设置")
                return

            # 从文件读取设置
            with open(self.settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)

            # 应用设置到界面控件
            if 'video_size' in settings:
                index = self.size_combo.findText(settings['video_size'])
                if index >= 0:
                    self.size_combo.setCurrentIndex(index)

            if 'fps' in settings:
                self.fps_spin.setValue(settings['fps'])

            if 'image_duration' in settings:
                self.duration_spin.setValue(settings['image_duration'])

            if 'voice_engine' in settings:
                index = self.voice_engine_combo.findText(settings['voice_engine'])
                if index >= 0:
                    self.voice_engine_combo.setCurrentIndex(index)

            if 'voice_character' in settings:
                index = self.voice_character_combo.findText(settings['voice_character'])
                if index >= 0:
                    self.voice_character_combo.setCurrentIndex(index)

            if 'voice_speed' in settings:
                self.speed_slider.setValue(settings['voice_speed'])

            if 'voice_volume' in settings:
                self.volume_slider.setValue(settings['voice_volume'])

            if 'enable_subtitles' in settings:
                self.enable_subtitles.setChecked(settings['enable_subtitles'])

            if 'subtitle_position' in settings:
                index = self.subtitle_position_combo.findText(settings['subtitle_position'])
                if index >= 0:
                    self.subtitle_position_combo.setCurrentIndex(index)

            if 'subtitle_background' in settings:
                index = self.subtitle_bg_combo.findText(settings['subtitle_background'])
                if index >= 0:
                    self.subtitle_bg_combo.setCurrentIndex(index)

            if 'subtitle_font_size' in settings:
                self.font_size_spin.setValue(settings['subtitle_font_size'])

            if 'subtitle_font_color' in settings:
                index = self.font_color_combo.findText(settings['subtitle_font_color'])
                if index >= 0:
                    self.font_color_combo.setCurrentIndex(index)

            if 'last_output_dir' in settings:
                self.last_output_dir = settings['last_output_dir']

            # 加载选品设置
            if 'selected_categories' in settings:
                # 先清除所有选择
                for button in self.category_buttons.values():
                    button.setChecked(False)
                # 恢复保存的选择
                selected_categories = settings['selected_categories']
                if selected_categories:  # 如果有保存的选择
                    for category_id in selected_categories:
                        if category_id in self.category_buttons:
                            self.category_buttons[category_id].setChecked(True)
                else:  # 如果没有保存的选择，默认选中"全部"
                    self.category_buttons["all"].setChecked(True)

            if 'selected_attributes' in settings:
                # 先清除所有选择
                for checkbox in self.attribute_checkboxes.values():
                    checkbox.setChecked(False)
                # 恢复保存的选择
                for attr_id in settings['selected_attributes']:
                    if attr_id in self.attribute_checkboxes:
                        self.attribute_checkboxes[attr_id].setChecked(True)

            if 'min_price' in settings:
                self.min_price_spin.setValue(settings['min_price'])

            if 'max_price' in settings:
                self.max_price_spin.setValue(settings['max_price'])

            if 'min_sales' in settings:
                self.min_sales_spin.setValue(settings['min_sales'])

            if 'min_rating' in settings:
                self.min_rating_spin.setValue(settings['min_rating'])

            print(f"✅ 设置已从文件加载: {self.settings_file}")

        except Exception as e:
            print(f"❌ 加载设置失败: {e}")
            print("将使用默认设置")

    def connect_auto_save(self):
        """连接所有控件的自动保存信号"""
        try:
            # 视频参数控件
            self.size_combo.currentTextChanged.connect(self.save_settings)
            self.fps_spin.valueChanged.connect(self.save_settings)
            self.duration_spin.valueChanged.connect(self.save_settings)

            # 语音参数控件
            self.voice_engine_combo.currentTextChanged.connect(self.save_settings)
            self.voice_character_combo.currentTextChanged.connect(self.save_settings)
            self.speed_slider.valueChanged.connect(self.save_settings)
            self.volume_slider.valueChanged.connect(self.save_settings)

            # 字幕参数控件
            self.enable_subtitles.toggled.connect(self.save_settings)
            self.subtitle_position_combo.currentTextChanged.connect(self.save_settings)
            self.subtitle_bg_combo.currentTextChanged.connect(self.save_settings)
            self.font_size_spin.valueChanged.connect(self.save_settings)
            self.font_color_combo.currentTextChanged.connect(self.save_settings)

            # 选品设置控件
            for button in self.category_buttons.values():
                button.toggled.connect(self.save_settings)
            for checkbox in self.attribute_checkboxes.values():
                checkbox.toggled.connect(self.save_settings)
            self.min_price_spin.valueChanged.connect(self.save_settings)
            self.max_price_spin.valueChanged.connect(self.save_settings)
            self.min_sales_spin.valueChanged.connect(self.save_settings)
            self.min_rating_spin.valueChanged.connect(self.save_settings)

            print("✅ 自动保存功能已启用")

        except Exception as e:
            print(f"❌ 连接自动保存失败: {e}")

    def ensure_default_settings(self):
        """确保默认设置被正确保存"""
        try:
            # 检查是否有选中的类目
            selected_categories = [btn_id for btn_id, btn in self.category_buttons.items() if btn.isChecked()]

            # 如果没有选中任何类目，默认选中"全部"
            if not selected_categories:
                self.category_buttons["all"].setChecked(True)
                print("设置默认选中类目: 全部")

            # 保存当前设置
            self.save_settings()

        except Exception as e:
            print(f"❌ 确保默认设置失败: {e}")

    def reset_settings(self):
        """重置为默认设置"""
        reply = QMessageBox.question(
            self,
            "确认重置",
            "确定要恢复默认设置吗？\n当前设置将被覆盖。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                # 恢复默认值
                self.size_combo.setCurrentIndex(0)  # 1080x1920 (竖屏)
                self.fps_spin.setValue(30)
                self.duration_spin.setValue(3.0)
                self.voice_engine_combo.setCurrentIndex(0)  # 自动选择
                self.voice_character_combo.setCurrentIndex(0)  # 晓晓
                self.speed_slider.setValue(100)  # 1.0x
                self.volume_slider.setValue(80)  # 80%
                self.enable_subtitles.setChecked(True)
                self.subtitle_position_combo.setCurrentIndex(0)  # 底部居中
                self.subtitle_bg_combo.setCurrentIndex(0)  # 半透明黑色背景
                self.font_size_spin.setValue(48)
                self.font_color_combo.setCurrentIndex(0)  # 白色

                # 重置选品设置
                # 清除所有类目选择，只选中"全部"
                for button in self.category_buttons.values():
                    button.setChecked(False)
                self.category_buttons["all"].setChecked(True)

                # 清除所有属性选择
                for checkbox in self.attribute_checkboxes.values():
                    checkbox.setChecked(False)

                # 重置价格和销量设置
                self.min_price_spin.setValue(0.0)
                self.max_price_spin.setValue(999.0)
                self.min_sales_spin.setValue(0)
                self.min_rating_spin.setValue(90.0)

                # 保存默认设置
                self.save_settings()

                QMessageBox.information(self, "重置完成", "设置已恢复为默认值！")

            except Exception as e:
                QMessageBox.critical(self, "重置失败", f"恢复默认设置失败：{e}")

    def export_settings(self):
        """导出设置到文件"""
        try:
            file_dialog = QFileDialog()
            file_path, _ = file_dialog.getSaveFileName(
                self,
                "导出设置文件",
                "video_settings_backup.json",
                "JSON文件 (*.json);;所有文件 (*)"
            )

            if file_path:
                import shutil
                shutil.copy2(self.settings_file, file_path)
                QMessageBox.information(
                    self,
                    "导出成功",
                    f"设置已导出到：\n{file_path}"
                )

        except Exception as e:
            QMessageBox.critical(self, "导出失败", f"导出设置失败：{e}")

    def import_settings(self):
        """从文件导入设置"""
        try:
            file_dialog = QFileDialog()
            file_path, _ = file_dialog.getOpenFileName(
                self,
                "导入设置文件",
                "",
                "JSON文件 (*.json);;所有文件 (*)"
            )

            if file_path:
                import shutil
                # 备份当前设置
                backup_path = self.settings_file + ".backup"
                if os.path.exists(self.settings_file):
                    shutil.copy2(self.settings_file, backup_path)

                # 导入新设置
                shutil.copy2(file_path, self.settings_file)

                # 重新加载设置
                self.load_settings()

                QMessageBox.information(
                    self,
                    "导入成功",
                    f"设置已从文件导入：\n{file_path}\n\n原设置已备份为：\n{backup_path}"
                )

        except Exception as e:
            QMessageBox.critical(self, "导入失败", f"导入设置失败：{e}")
