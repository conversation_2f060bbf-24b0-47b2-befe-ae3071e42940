Metadata-Version: 2.1
Name: qt6-tools
Version: 6.1.0.1.2
Summary: Wrappers for the raw Qt programs from qt6-applications
Home-page: https://github.com/altendky/qt-tools
Author: <PERSON>
Author-email: <EMAIL>
License: LGPLv3
Platform: UNKNOWN
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: GNU Lesser General Public License v3 (LGPLv3)
Classifier: Operating System :: POSIX :: Linux
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: Microsoft :: Windows
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Topic :: Software Development
Classifier: Topic :: Utilities
Requires-Python: >=3.5
Description-Content-Type: text/x-rst
Requires-Dist: qt6-applications (<*******,>=*******.2)
Requires-Dist: click (~=7.0)

========
qt-tools
========


|PyPI| |Pythons| |GitHub|

Perhaps docs will follow but for now see `the pyqt-tools readme`_.  This
package provides just the wrappers for `qt-applications`_.


.. |PyPI| image:: https://img.shields.io/pypi/v/qt5-applications.svg
   :alt: PyPI version
   :target: https://pypi.org/project/qt5-applications/

.. |Pythons| image:: https://img.shields.io/pypi/pyversions/qt5-applications.svg
   :alt: supported Python versions
   :target: https://pypi.org/project/qt5-applications/

.. |GitHub| image:: https://img.shields.io/github/last-commit/altendky/qt-applications/main.svg
   :alt: source on GitHub
   :target: https://github.com/altendky/qt-applications

.. _`the pyqt-tools readme`: https://github.com/altendky/pyqt-tools#pyqt-tools
.. _`qt-applications`: https://github.com/altendky/qt-applications#qt-applications


