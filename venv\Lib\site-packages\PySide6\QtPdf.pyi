# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations
"""
This file contains the exact signatures for all functions in module
PySide6.QtPdf, except for defaults which are replaced by "...".

# mypy: disable-error-code="override, overload-overlap"
"""

# Module `PySide6.QtPdf`

import PySide6.QtPdf
import PySide6.QtCore
import PySide6.QtGui

import enum
import typing
import collections.abc
from PySide6.QtCore import Signal
from shiboken6 import Shiboken


class QIntList: ...


class QPdfBookmarkModel(PySide6.QtCore.QAbstractItemModel):

    documentChanged          : typing.ClassVar[Signal] = ... # documentChanged(QPdfDocument*)

    class Role(enum.IntEnum):

        Title                     = ...  # 0x100
        Level                     = ...  # 0x101
        Page                      = ...  # 0x102
        Location                  = ...  # 0x103
        Zoom                      = ...  # 0x104
        NRoles                    = ...  # 0x105


    @typing.overload
    def __init__(self, parent: PySide6.QtCore.QObject, /, *, document: PySide6.QtPdf.QPdfDocument | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, document: PySide6.QtPdf.QPdfDocument | None = ...) -> None: ...

    def columnCount(self, /, parent: PySide6.QtCore.QModelIndex | PySide6.QtCore.QPersistentModelIndex = ...) -> int: ...
    def data(self, index: PySide6.QtCore.QModelIndex | PySide6.QtCore.QPersistentModelIndex, role: int, /) -> typing.Any: ...
    def document(self, /) -> PySide6.QtPdf.QPdfDocument: ...
    def index(self, row: int, column: int, /, parent: PySide6.QtCore.QModelIndex | PySide6.QtCore.QPersistentModelIndex = ...) -> PySide6.QtCore.QModelIndex: ...
    @typing.overload
    def parent(self, /) -> PySide6.QtCore.QObject: ...
    @typing.overload
    def parent(self, index: PySide6.QtCore.QModelIndex | PySide6.QtCore.QPersistentModelIndex, /) -> PySide6.QtCore.QModelIndex: ...
    def roleNames(self, /) -> typing.Dict[int, PySide6.QtCore.QByteArray]: ...
    def rowCount(self, /, parent: PySide6.QtCore.QModelIndex | PySide6.QtCore.QPersistentModelIndex = ...) -> int: ...
    def setDocument(self, document: PySide6.QtPdf.QPdfDocument, /) -> None: ...


class QPdfDocument(PySide6.QtCore.QObject):

    pageCountChanged         : typing.ClassVar[Signal] = ... # pageCountChanged(int)
    pageModelChanged         : typing.ClassVar[Signal] = ... # pageModelChanged()
    passwordChanged          : typing.ClassVar[Signal] = ... # passwordChanged()
    passwordRequired         : typing.ClassVar[Signal] = ... # passwordRequired()
    statusChanged            : typing.ClassVar[Signal] = ... # statusChanged(QPdfDocument::Status)

    class Error(enum.Enum):

        None_                     = ...  # 0x0
        Unknown                   = ...  # 0x1
        DataNotYetAvailable       = ...  # 0x2
        FileNotFound              = ...  # 0x3
        InvalidFileFormat         = ...  # 0x4
        IncorrectPassword         = ...  # 0x5
        UnsupportedSecurityScheme = ...  # 0x6

    class MetaDataField(enum.Enum):

        Title                     = ...  # 0x0
        Subject                   = ...  # 0x1
        Author                    = ...  # 0x2
        Keywords                  = ...  # 0x3
        Producer                  = ...  # 0x4
        Creator                   = ...  # 0x5
        CreationDate              = ...  # 0x6
        ModificationDate          = ...  # 0x7

    class PageModelRole(enum.Enum):

        Label                     = ...  # 0x100
        PointSize                 = ...  # 0x101
        NRoles                    = ...  # 0x102

    class Status(enum.Enum):

        Null                      = ...  # 0x0
        Loading                   = ...  # 0x1
        Ready                     = ...  # 0x2
        Unloading                 = ...  # 0x3
        Error                     = ...  # 0x4


    @typing.overload
    def __init__(self, parent: PySide6.QtCore.QObject, /, *, pageCount: int | None = ..., password: str | None = ..., status: PySide6.QtPdf.QPdfDocument.Status | None = ..., pageModel: PySide6.QtCore.QAbstractListModel | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, pageCount: int | None = ..., password: str | None = ..., status: PySide6.QtPdf.QPdfDocument.Status | None = ..., pageModel: PySide6.QtCore.QAbstractListModel | None = ...) -> None: ...

    def close(self, /) -> None: ...
    def error(self, /) -> PySide6.QtPdf.QPdfDocument.Error: ...
    def getAllText(self, page: int, /) -> PySide6.QtPdf.QPdfSelection: ...
    def getSelection(self, page: int, start: PySide6.QtCore.QPointF | PySide6.QtCore.QPoint | PySide6.QtGui.QPainterPath.Element, end: PySide6.QtCore.QPointF | PySide6.QtCore.QPoint | PySide6.QtGui.QPainterPath.Element, /) -> PySide6.QtPdf.QPdfSelection: ...
    def getSelectionAtIndex(self, page: int, startIndex: int, maxLength: int, /) -> PySide6.QtPdf.QPdfSelection: ...
    @typing.overload
    def load(self, device: PySide6.QtCore.QIODevice, /) -> None: ...
    @typing.overload
    def load(self, fileName: str, /) -> PySide6.QtPdf.QPdfDocument.Error: ...
    def metaData(self, field: PySide6.QtPdf.QPdfDocument.MetaDataField, /) -> typing.Any: ...
    def pageCount(self, /) -> int: ...
    def pageIndexForLabel(self, label: str, /) -> int: ...
    def pageLabel(self, page: int, /) -> str: ...
    def pageModel(self, /) -> PySide6.QtCore.QAbstractListModel: ...
    def pagePointSize(self, page: int, /) -> PySide6.QtCore.QSizeF: ...
    def password(self, /) -> str: ...
    def render(self, page: int, imageSize: PySide6.QtCore.QSize, /, options: PySide6.QtPdf.QPdfDocumentRenderOptions = ...) -> PySide6.QtGui.QImage: ...
    def setPassword(self, password: str, /) -> None: ...
    def status(self, /) -> PySide6.QtPdf.QPdfDocument.Status: ...


class QPdfDocumentRenderOptions(Shiboken.Object):

    class RenderFlag(enum.Flag):

        None_                     = ...  # 0x0
        Annotations               = ...  # 0x1
        OptimizedForLcd           = ...  # 0x2
        Grayscale                 = ...  # 0x4
        ForceHalftone             = ...  # 0x8
        TextAliased               = ...  # 0x10
        ImageAliased              = ...  # 0x20
        PathAliased               = ...  # 0x40

    class Rotation(enum.Enum):

        None_                     = ...  # 0x0
        Clockwise90               = ...  # 0x1
        Clockwise180              = ...  # 0x2
        Clockwise270              = ...  # 0x3


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, QPdfDocumentRenderOptions: PySide6.QtPdf.QPdfDocumentRenderOptions, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, rhs: PySide6.QtPdf.QPdfDocumentRenderOptions, /) -> bool: ...
    def __ne__(self, rhs: PySide6.QtPdf.QPdfDocumentRenderOptions, /) -> bool: ...
    def renderFlags(self, /) -> PySide6.QtPdf.QPdfDocumentRenderOptions.RenderFlag: ...
    def rotation(self, /) -> PySide6.QtPdf.QPdfDocumentRenderOptions.Rotation: ...
    def scaledClipRect(self, /) -> PySide6.QtCore.QRect: ...
    def scaledSize(self, /) -> PySide6.QtCore.QSize: ...
    def setRenderFlags(self, r: PySide6.QtPdf.QPdfDocumentRenderOptions.RenderFlag, /) -> None: ...
    def setRotation(self, r: PySide6.QtPdf.QPdfDocumentRenderOptions.Rotation, /) -> None: ...
    def setScaledClipRect(self, r: PySide6.QtCore.QRect, /) -> None: ...
    def setScaledSize(self, s: PySide6.QtCore.QSize, /) -> None: ...


class QPdfLink(Shiboken.Object):

    @typing.overload
    def __init__(self, other: PySide6.QtPdf.QPdfLink, /, *, valid: bool | None = ..., page: int | None = ..., location: PySide6.QtCore.QPointF | None = ..., zoom: float | None = ..., url: PySide6.QtCore.QUrl | None = ..., contextBefore: str | None = ..., contextAfter: str | None = ..., rectangles: collections.abc.Sequence[PySide6.QtCore.QRectF] | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, valid: bool | None = ..., page: int | None = ..., location: PySide6.QtCore.QPointF | None = ..., zoom: float | None = ..., url: PySide6.QtCore.QUrl | None = ..., contextBefore: str | None = ..., contextAfter: str | None = ..., rectangles: collections.abc.Sequence[PySide6.QtCore.QRectF] | None = ...) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __repr__(self, /) -> str: ...
    def contextAfter(self, /) -> str: ...
    def contextBefore(self, /) -> str: ...
    def copyToClipboard(self, /, mode: PySide6.QtGui.QClipboard.Mode = ...) -> None: ...
    def isValid(self, /) -> bool: ...
    def location(self, /) -> PySide6.QtCore.QPointF: ...
    def page(self, /) -> int: ...
    def rectangles(self, /) -> typing.List[PySide6.QtCore.QRectF]: ...
    def swap(self, other: PySide6.QtPdf.QPdfLink, /) -> None: ...
    def toString(self, /) -> str: ...
    def url(self, /) -> PySide6.QtCore.QUrl: ...
    def zoom(self, /) -> float: ...


class QPdfLinkModel(PySide6.QtCore.QAbstractListModel):

    documentChanged          : typing.ClassVar[Signal] = ... # documentChanged()
    pageChanged              : typing.ClassVar[Signal] = ... # pageChanged(int)

    class Role(enum.Enum):

        Link                      = ...  # 0x100
        Rectangle                 = ...  # 0x101
        Url                       = ...  # 0x102
        Page                      = ...  # 0x103
        Location                  = ...  # 0x104
        Zoom                      = ...  # 0x105
        NRoles                    = ...  # 0x106


    def __init__(self, /, parent: PySide6.QtCore.QObject | None = ..., *, document: PySide6.QtPdf.QPdfDocument | None = ..., page: int | None = ...) -> None: ...

    def data(self, index: PySide6.QtCore.QModelIndex | PySide6.QtCore.QPersistentModelIndex, role: int, /) -> typing.Any: ...
    def document(self, /) -> PySide6.QtPdf.QPdfDocument: ...
    def linkAt(self, point: PySide6.QtCore.QPointF | PySide6.QtCore.QPoint | PySide6.QtGui.QPainterPath.Element, /) -> PySide6.QtPdf.QPdfLink: ...
    def page(self, /) -> int: ...
    def roleNames(self, /) -> typing.Dict[int, PySide6.QtCore.QByteArray]: ...
    def rowCount(self, parent: PySide6.QtCore.QModelIndex | PySide6.QtCore.QPersistentModelIndex, /) -> int: ...
    def setDocument(self, document: PySide6.QtPdf.QPdfDocument, /) -> None: ...
    def setPage(self, page: int, /) -> None: ...


class QPdfPageNavigator(PySide6.QtCore.QObject):

    backAvailableChanged     : typing.ClassVar[Signal] = ... # backAvailableChanged(bool)
    currentLocationChanged   : typing.ClassVar[Signal] = ... # currentLocationChanged(QPointF)
    currentPageChanged       : typing.ClassVar[Signal] = ... # currentPageChanged(int)
    currentZoomChanged       : typing.ClassVar[Signal] = ... # currentZoomChanged(double)
    forwardAvailableChanged  : typing.ClassVar[Signal] = ... # forwardAvailableChanged(bool)
    jumped                   : typing.ClassVar[Signal] = ... # jumped(QPdfLink)

    @typing.overload
    def __init__(self, parent: PySide6.QtCore.QObject, /, *, currentPage: int | None = ..., currentLocation: PySide6.QtCore.QPointF | None = ..., currentZoom: float | None = ..., backAvailable: bool | None = ..., forwardAvailable: bool | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, currentPage: int | None = ..., currentLocation: PySide6.QtCore.QPointF | None = ..., currentZoom: float | None = ..., backAvailable: bool | None = ..., forwardAvailable: bool | None = ...) -> None: ...

    def back(self, /) -> None: ...
    def backAvailable(self, /) -> bool: ...
    def clear(self, /) -> None: ...
    def currentLink(self, /) -> PySide6.QtPdf.QPdfLink: ...
    def currentLocation(self, /) -> PySide6.QtCore.QPointF: ...
    def currentPage(self, /) -> int: ...
    def currentZoom(self, /) -> float: ...
    def forward(self, /) -> None: ...
    def forwardAvailable(self, /) -> bool: ...
    @typing.overload
    def jump(self, destination: PySide6.QtPdf.QPdfLink, /) -> None: ...
    @typing.overload
    def jump(self, page: int, location: PySide6.QtCore.QPointF | PySide6.QtCore.QPoint | PySide6.QtGui.QPainterPath.Element, /, zoom: float | None = ...) -> None: ...
    def update(self, page: int, location: PySide6.QtCore.QPointF | PySide6.QtCore.QPoint | PySide6.QtGui.QPainterPath.Element, zoom: float, /) -> None: ...


class QPdfPageRenderer(PySide6.QtCore.QObject):

    documentChanged          : typing.ClassVar[Signal] = ... # documentChanged(QPdfDocument*)
    pageRendered             : typing.ClassVar[Signal] = ... # pageRendered(int,QSize,QImage,QPdfDocumentRenderOptions,qulonglong)
    renderModeChanged        : typing.ClassVar[Signal] = ... # renderModeChanged(QPdfPageRenderer::RenderMode)

    class RenderMode(enum.Enum):

        MultiThreaded             = ...  # 0x0
        SingleThreaded            = ...  # 0x1


    @typing.overload
    def __init__(self, parent: PySide6.QtCore.QObject, /, *, document: PySide6.QtPdf.QPdfDocument | None = ..., renderMode: PySide6.QtPdf.QPdfPageRenderer.RenderMode | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, document: PySide6.QtPdf.QPdfDocument | None = ..., renderMode: PySide6.QtPdf.QPdfPageRenderer.RenderMode | None = ...) -> None: ...

    def document(self, /) -> PySide6.QtPdf.QPdfDocument: ...
    def renderMode(self, /) -> PySide6.QtPdf.QPdfPageRenderer.RenderMode: ...
    def requestPage(self, pageNumber: int, imageSize: PySide6.QtCore.QSize, /, options: PySide6.QtPdf.QPdfDocumentRenderOptions = ...) -> int: ...
    def setDocument(self, document: PySide6.QtPdf.QPdfDocument, /) -> None: ...
    def setRenderMode(self, mode: PySide6.QtPdf.QPdfPageRenderer.RenderMode, /) -> None: ...


class QPdfSearchModel(PySide6.QtCore.QAbstractListModel):

    countChanged             : typing.ClassVar[Signal] = ... # countChanged()
    documentChanged          : typing.ClassVar[Signal] = ... # documentChanged()
    searchStringChanged      : typing.ClassVar[Signal] = ... # searchStringChanged()

    class Role(enum.Enum):

        Page                      = ...  # 0x100
        IndexOnPage               = ...  # 0x101
        Location                  = ...  # 0x102
        ContextBefore             = ...  # 0x103
        ContextAfter              = ...  # 0x104
        NRoles                    = ...  # 0x105


    @typing.overload
    def __init__(self, parent: PySide6.QtCore.QObject, /, *, document: PySide6.QtPdf.QPdfDocument | None = ..., searchString: str | None = ..., count: int | None = ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, document: PySide6.QtPdf.QPdfDocument | None = ..., searchString: str | None = ..., count: int | None = ...) -> None: ...

    def count(self, /) -> int: ...
    def data(self, index: PySide6.QtCore.QModelIndex | PySide6.QtCore.QPersistentModelIndex, role: int, /) -> typing.Any: ...
    def document(self, /) -> PySide6.QtPdf.QPdfDocument: ...
    def resultAtIndex(self, index: int, /) -> PySide6.QtPdf.QPdfLink: ...
    def resultsOnPage(self, page: int, /) -> typing.List[PySide6.QtPdf.QPdfLink]: ...
    def roleNames(self, /) -> typing.Dict[int, PySide6.QtCore.QByteArray]: ...
    def rowCount(self, parent: PySide6.QtCore.QModelIndex | PySide6.QtCore.QPersistentModelIndex, /) -> int: ...
    def searchString(self, /) -> str: ...
    def setDocument(self, document: PySide6.QtPdf.QPdfDocument, /) -> None: ...
    def setSearchString(self, searchString: str, /) -> None: ...
    def timerEvent(self, event: PySide6.QtCore.QTimerEvent, /) -> None: ...
    def updatePage(self, page: int, /) -> None: ...


class QPdfSelection(Shiboken.Object):

    def __init__(self, other: PySide6.QtPdf.QPdfSelection, /, *, valid: bool | None = ..., bounds: collections.abc.Sequence[PySide6.QtGui.QPolygonF] | None = ..., boundingRectangle: PySide6.QtCore.QRectF | None = ..., text: str | None = ..., startIndex: int | None = ..., endIndex: int | None = ...) -> None: ...

    def boundingRectangle(self, /) -> PySide6.QtCore.QRectF: ...
    def bounds(self, /) -> typing.List[PySide6.QtGui.QPolygonF]: ...
    def copyToClipboard(self, /, mode: PySide6.QtGui.QClipboard.Mode = ...) -> None: ...
    def endIndex(self, /) -> int: ...
    def isValid(self, /) -> bool: ...
    def startIndex(self, /) -> int: ...
    def swap(self, other: PySide6.QtPdf.QPdfSelection, /) -> None: ...
    def text(self, /) -> str: ...


# eof
